---
# OpenVPN Client Installation Playbook
# This playbook installs and configures OpenVPN client on edge servers
# to connect to the enhanced whitelist VPN gateway
#
# Usage:
#   ansible-playbook -i your_hosts.yml playbooks/infra_openvpn_client.yml -e "run_hosts=hostname1,hostname2"
#
# Requirements:
#   - client.ovpn file must be placed in roles/fm.openvpn-client/files/client.ovpn
#   - Target servers must have a primary network interface (auto-detected)

- name: Install and Configure OpenVPN Client
  hosts: "{{ run_hosts.split(',') }}"

  become: true
  gather_facts: true

  vars:
    # VPN Server Configuration
    openvpn_server_hostname: "eyecue-whitelist-vpn-francium-lb-f943a130c03e187d.elb.ap-southeast-2.amazonaws.com"

    # Client Configuration
    openvpn_client_auto_start: true
    openvpn_client_log_level: 3
    openvpn_client_enable_monitoring: false

    # S3 Configuration for Cross-Account VPN Config Download
    # Uncomment these to download client configs from S3 instead of using local files
    vpn_config_s3_bucket: "eyecue-francium-vpn-client-configs"
    vpn_config_s3_region: "ap-southeast-2"

  pre_tasks:
    - name: Validate required variables
      assert:
        that:
          - openvpn_server_hostname is defined
          - openvpn_server_hostname | length > 0
        fail_msg: "Required variable openvpn_server_hostname must be defined"

    - name: Auto-detect primary network interface
      block:
        - name: Get default route interface
          shell: ip route show default | awk '/default/ {print $5}' | head -1
          register: default_interface_result
          changed_when: false

        - name: Set primary interface from default route
          set_fact:
            openvpn_client_wired_interface: "{{ default_interface_result.stdout }}"
          when:
            - default_interface_result.stdout is defined
            - default_interface_result.stdout | length > 0
            - openvpn_client_wired_interface is not defined

        - name: Fallback to ansible default interface
          set_fact:
            openvpn_client_wired_interface: "{{ ansible_default_ipv4.interface }}"
          when:
            - openvpn_client_wired_interface is not defined
            - ansible_default_ipv4.interface is defined

        - name: Final fallback - find first physical ethernet interface
          shell: |
            for iface in $(ls /sys/class/net/); do
              if [[ $iface =~ ^(en|eth) ]] && [[ -d /sys/class/net/$iface/device ]]; then
                echo $iface
                break
              fi
            done
          register: physical_interface_result
          changed_when: false
          when: openvpn_client_wired_interface is not defined

        - name: Set interface from physical detection
          set_fact:
            openvpn_client_wired_interface: "{{ physical_interface_result.stdout }}"
          when:
            - openvpn_client_wired_interface is not defined
            - physical_interface_result.stdout is defined
            - physical_interface_result.stdout | length > 0

        - name: Fail if no suitable interface found
          fail:
            msg: "Could not auto-detect primary network interface. Please set openvpn_client_wired_interface manually."
          when: openvpn_client_wired_interface is not defined

    - name: Display configuration summary
      debug:
        msg:
          - "VPN Server: {{ openvpn_server_hostname }}"
          - "Auto Start: {{ openvpn_client_auto_start }}"
          - "Primary Interface: {{ openvpn_client_wired_interface }} (auto-detected)"
          - "Target Hosts: {{ ansible_play_hosts }}"

  roles:
    - role: fm.openvpn-client
      tags: 
        - openvpn
        - vpn
        - network

  post_tasks:
    - name: Display post-installation information
      debug:
        msg:
          - "✓ OpenVPN client installation completed successfully!"
          - ""
          - "To test the VPN connection on the edge server:"
          - "  sudo /usr/local/bin/vpn-manager.sh start   # Start VPN"
          - "  sudo /usr/local/bin/vpn-manager.sh status  # Check status"
          - "  sudo /usr/local/bin/vpn-manager.sh stop    # Stop VPN"
          - ""
          - "Service management:"
          - "  sudo systemctl start openvpn-client     # Start VPN service"
          - "  sudo systemctl stop openvpn-client      # Stop VPN service"
          - "  sudo systemctl enable openvpn-client    # Enable auto-start on boot"
          - ""
          - "Troubleshooting:"
          - "  sudo /usr/local/bin/vpn-troubleshoot.sh # Generate troubleshooting report"
          - ""
          - "Configuration: /etc/openvpn/client/client.ovpn"
          - "Logs: /var/log/openvpn/client.log, /var/log/openvpn/service.log"

    - name: VPN connection ready
      debug:
        msg:
          - "✓ OpenVPN client is configured with actual certificates and ready to use!"
          - ""
          - "To check connection status:"
          - "  sudo /usr/local/bin/vpn-manager.sh status"
