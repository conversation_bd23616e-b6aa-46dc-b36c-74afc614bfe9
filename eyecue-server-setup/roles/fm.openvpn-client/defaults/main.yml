---
# OpenVPN Client Role Default Variables

# VPN Configuration
openvpn_client_auto_start: false
openvpn_client_log_level: 3

# Network Interface Configuration
# Primary interface is auto-detected by the playbook
# Uncomment and set manually if auto-detection fails
# openvpn_client_wired_interface: "eno2"

# S3 Configuration for Cross-Account VPN Config Download
# Set these variables to download client config from S3 instead of using local files
# vpn_config_s3_bucket: "eyecue-francium-vpn-client-configs"
# vpn_config_s3_region: "ap-southeast-2"

# Directory Configuration
openvpn_client_config_dir: "/etc/openvpn/client"
openvpn_client_log_dir: "/var/log/openvpn"
openvpn_client_script_dir: "/usr/local/bin"

# Service Configuration
openvpn_client_service_name: "openvpn-client"
openvpn_client_config_file: "client.ovpn"

# Connection Settings
openvpn_client_connect_timeout: 30
openvpn_client_ping_interval: 10
openvpn_client_ping_restart: 120

# Required Variables (must be set by user)
# openvpn_server_hostname: ""       # VPN server hostname/FQDN

# Note: The client.ovpn file should be placed in the role's files/ directory
