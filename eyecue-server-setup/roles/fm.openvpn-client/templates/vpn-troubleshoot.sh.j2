#!/bin/bash
# OpenVPN Troubleshooting Script
# Generated by Ansible - DO NOT EDIT MANUALLY

# Configuration
VPN_CONFIG="{{ openvpn_client_config_dir }}/{{ openvpn_client_config_file }}"
WIRED_INTERFACE="{{ openvpn_client_wired_interface }}"
SERVICE_NAME="{{ openvpn_client_service_name }}"
LOG_FILE="{{ openvpn_client_log_dir }}/client.log"
SERVICE_LOG_FILE="{{ openvpn_client_log_dir }}/service.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== OpenVPN Troubleshooting Report ===${NC}"
echo "Generated: $(date)"
echo ""

# System Information
echo -e "${YELLOW}--- System Information ---${NC}"
echo "OS: $(lsb_release -d 2>/dev/null | cut -f2 || cat /etc/os-release | grep PRETTY_NAME | cut -d'"' -f2)"
echo "OpenVPN Version: $(openvpn --version | head -1)"
echo ""

# Network Configuration
echo -e "${YELLOW}--- Network Configuration ---${NC}"
echo "Primary Interface ($WIRED_INTERFACE):"
if ip addr show "$WIRED_INTERFACE" >/dev/null 2>&1; then
    ip addr show "$WIRED_INTERFACE" | grep -E "(inet |state )"
else
    echo -e "  ${RED}Interface $WIRED_INTERFACE not found${NC}"
fi
echo ""

echo "Default Routes:"
ip route show | grep default
echo ""

# VPN Server Connectivity
echo -e "${YELLOW}--- VPN Server Connectivity ---${NC}"
VPN_SERVER=$(grep "^remote " "$VPN_CONFIG" | awk '{print $2}' | head -1)
VPN_PORT=$(grep "^remote " "$VPN_CONFIG" | awk '{print $3}' | head -1)
echo "VPN Server: $VPN_SERVER:$VPN_PORT"

if [ -n "$VPN_SERVER" ]; then
    echo "DNS Resolution:"
    VPN_IPS=$(dig +short "$VPN_SERVER" 2>/dev/null | head -3)
    if [ -n "$VPN_IPS" ]; then
        echo "$VPN_IPS"
    else
        echo -e "  ${RED}DNS resolution failed${NC}"
    fi
    echo ""

    echo "UDP Port Connectivity Test:"
    if timeout 5 nc -u -z "$VPN_SERVER" "$VPN_PORT" 2>/dev/null; then
        echo -e "  ${GREEN}Port $VPN_PORT/UDP: Reachable${NC}"
    else
        echo -e "  ${RED}Port $VPN_PORT/UDP: Not reachable${NC}"
        echo "  (Check customer firewall allows UDP $VPN_PORT to $VPN_SERVER)"
    fi
fi
echo ""

# Service Status
echo -e "${YELLOW}--- Service Status ---${NC}"
if systemctl is-active "$SERVICE_NAME" >/dev/null 2>&1; then
    echo -e "${GREEN}Service Status: Active${NC}"
    echo "Uptime: $(systemctl show "$SERVICE_NAME" --property=ActiveEnterTimestamp --value | cut -d' ' -f2-)"
else
    echo -e "${RED}Service Status: Inactive/Failed${NC}"
    echo "Last failure:"
    systemctl status "$SERVICE_NAME" --no-pager -l | grep -A 3 -B 3 "failed\|error\|Error"
fi
echo ""

# VPN Connection Status
echo -e "${YELLOW}--- VPN Connection Status ---${NC}"
if ip addr show tun0 >/dev/null 2>&1; then
    echo -e "${GREEN}✓ TUN interface exists${NC}"
    TUN_IP=$(ip addr show tun0 | grep "inet " | awk '{print $2}')
    echo "VPN IP: $TUN_IP"

    # Test if traffic is routing through VPN
    echo "Testing traffic routing..."
    PUBLIC_IP=$(curl -s --max-time 5 ifconfig.me 2>/dev/null)
    if [ -n "$PUBLIC_IP" ]; then
        echo "Current public IP: $PUBLIC_IP"
        echo -e "${GREEN}✓ Internet access through VPN working${NC}"
    else
        echo -e "${RED}✗ No internet access${NC}"
    fi
else
    echo -e "${RED}✗ TUN interface not found - VPN not connected${NC}"
fi
echo ""

# Recent Issues
echo -e "${YELLOW}--- Recent Issues ---${NC}"
echo "Recent errors from systemd journal:"
ERROR_LOGS=$(journalctl -u "$SERVICE_NAME" --since "1 hour ago" --no-pager | grep -i "error\|failed\|timeout\|cannot" | tail -5)
if [ -n "$ERROR_LOGS" ]; then
    echo "$ERROR_LOGS"
else
    echo "No recent errors found"
fi
echo ""

echo "Recent OpenVPN log entries:"
if [ -f "$LOG_FILE" ]; then
    RECENT_LOGS=$(tail -10 "$LOG_FILE" | grep -v "Incoming Data Channel\|Outgoing Data Channel\|Control Channel")
    if [ -n "$RECENT_LOGS" ]; then
        echo "$RECENT_LOGS"
    else
        echo "No significant recent log entries"
    fi
else
    echo "OpenVPN log file not found: $LOG_FILE"
fi
echo ""

# Configuration Validation
echo -e "${YELLOW}--- Configuration Validation ---${NC}"
if [ -f "$VPN_CONFIG" ]; then
    echo -e "${GREEN}✓ Config file exists: $VPN_CONFIG${NC}"

    # Check key configuration
    REMOTE_LINE=$(grep "^remote " "$VPN_CONFIG" | head -1)
    if [ -n "$REMOTE_LINE" ]; then
        echo "✓ Remote server configured: $REMOTE_LINE"
    else
        echo -e "${RED}✗ No remote server configured${NC}"
    fi

    # Check certificates
    if grep -q "BEGIN CERTIFICATE" "$VPN_CONFIG"; then
        echo "✓ Client certificate present"
    else
        echo -e "${RED}✗ Client certificate missing${NC}"
    fi

    if grep -q "BEGIN PRIVATE KEY" "$VPN_CONFIG"; then
        echo "✓ Private key present"
    else
        echo -e "${RED}✗ Private key missing${NC}"
    fi
else
    echo -e "${RED}✗ Config file not found: $VPN_CONFIG${NC}"
fi
echo ""

# Quick Actions
echo -e "${YELLOW}--- Quick Actions ---${NC}"
if ! systemctl is-active "$SERVICE_NAME" >/dev/null 2>&1; then
    echo "VPN service is not running. Try:"
    echo "  sudo systemctl restart $SERVICE_NAME"
elif ! ip addr show tun0 >/dev/null 2>&1; then
    echo "VPN service running but no tunnel. Try:"
    echo "  sudo /usr/local/bin/vpn-manager.sh stop"
    echo "  sudo /usr/local/bin/vpn-manager.sh start"
elif [ -z "$(curl -s --max-time 5 ifconfig.me 2>/dev/null)" ]; then
    echo "VPN connected but no internet. Check:"
    echo "  - Customer firewall allows UDP $VPN_PORT to $VPN_SERVER"
    echo "  - Network connectivity to VPN server"
else
    echo -e "${GREEN}✓ VPN appears to be working correctly${NC}"
    echo "Current public IP: $(curl -s --max-time 5 ifconfig.me 2>/dev/null)"
fi
echo ""

echo -e "${BLUE}=== End of Troubleshooting Report ===${NC}"
