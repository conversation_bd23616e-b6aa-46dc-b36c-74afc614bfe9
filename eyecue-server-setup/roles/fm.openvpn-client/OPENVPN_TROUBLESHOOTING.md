# OpenVPN Client Troubleshooting Guide

Quick troubleshooting for the enhanced whitelist VPN gateway client.

## Quick Diagnosis

Run the automated troubleshooting script first:
```bash
sudo /usr/local/bin/vpn-troubleshoot.sh
```

## Common Issues

### 1. VPN Service Won't Start

**Check Status:**
```bash
sudo systemctl status openvpn-client
sudo journalctl -u openvpn-client --since "10 minutes ago"
```

**Solutions:**
- **Network not ready**: Wait 30 seconds and restart service
  ```bash
  sudo systemctl restart openvpn-client
  ```

- **DNS issues**: Test VPN server resolution
  ```bash
  nslookup eyecue-whitelist-vpn-francium-lb-f943a130c03e187d.elb.ap-southeast-2.amazonaws.com
  ```

- **Route conflicts**: Reset and restart
  ```bash
  sudo /usr/local/bin/vpn-manager.sh stop
  sudo /usr/local/bin/vpn-manager.sh start
  ```

### 2. VPN Connects But No Internet

**Check Traffic Routing:**
```bash
# Should return VPN gateway IP
curl ifconfig.me

# Check VPN interface exists
ip addr show tun0

# Check routing table
ip route | grep tun0
```

**Solutions:**
- **Routes missing**: Restart VPN to rebuild routes
  ```bash
  sudo /usr/local/bin/vpn-manager.sh stop
  sudo /usr/local/bin/vpn-manager.sh start
  ```

### 3. Connection Timeouts

**Test Connectivity:**
```bash
# Test VPN server reachability
nc -u -z eyecue-whitelist-vpn-francium-lb-f943a130c03e187d.elb.ap-southeast-2.amazonaws.com 1194
```

**Solutions:**
- **Firewall blocking**: Check customer firewall allows UDP 1194 to VPN endpoint
- **Network issues**: Contact network administrator

### 4. TUN Interface Problems

**Fix TUN Module:**
```bash
sudo modprobe tun
sudo mkdir -p /dev/net
sudo mknod /dev/net/tun c 10 200
sudo chmod 600 /dev/net/tun
```

## Validation Tests

### Basic Connectivity
```bash
# Check primary interface
ip addr show eno2

# Test internet before VPN
curl ifconfig.me

# Test VPN server reachability
nc -u -z eyecue-whitelist-vpn-francium-lb-f943a130c03e187d.elb.ap-southeast-2.amazonaws.com 1194
```

### Manual OpenVPN Test
```bash
# Test configuration manually (for debugging)
sudo openvpn --config /etc/openvpn/client/client.ovpn --verb 4
```

## Log Locations

- **Service logs**: `journalctl -u openvpn-client`
- **OpenVPN logs**: `/var/log/openvpn/client.log`
- **Troubleshooting**: `/var/log/openvpn/pre-start.log`

## Recovery Steps

### Complete Reset
```bash
# Stop and clean up
sudo /usr/local/bin/vpn-manager.sh stop
sudo systemctl stop openvpn-client

# Restart networking
sudo systemctl restart systemd-networkd

# Start fresh
sudo /usr/local/bin/vpn-manager.sh start
```

### Debug Mode
```bash
# Manual connection with verbose logging
sudo openvpn --config /etc/openvpn/client/client.ovpn --verb 6
```

## Getting Help

If issues persist, collect diagnostics:

```bash
# Generate comprehensive report
sudo /usr/local/bin/vpn-troubleshoot.sh > vpn-report.txt

# Get recent logs
sudo journalctl -u openvpn-client --since "1 hour ago" > service-logs.txt
```

Provide these files when requesting support.
