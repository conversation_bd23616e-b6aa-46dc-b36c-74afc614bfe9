# OpenVPN Client Role

Configures OpenVPN client on on-premise servers to route all egress traffic through the enhanced whitelist VPN gateway.

## Purpose

Routes all internet-bound traffic from on-premise servers through a single VPN endpoint, allowing customers to whitelist only one endpoint in their firewalls.

## What It Does

- **Auto-detects primary network interface** (eno1, eno2, enp3s0, etc.)
- **Downloads OpenVPN client config from S3** (cross-account support) or uses local files
- Configures routing to send all egress traffic through VPN tunnel
- Creates management scripts for easy VPN control
- Sets up systemd service for reliable operation
- Preserves local network connectivity and SoftEther VPN access

## Usage

Use the `infra_openvpn_client.yml` playbook:

```bash
ansible-playbook -i hosts.yml playbooks/infra_openvpn_client.yml -e "run_hosts=server-name"

# Override interface detection if needed
ansible-playbook -i hosts.yml playbooks/infra_openvpn_client.yml -e "run_hosts=server-name" -e "openvpn_client_wired_interface=enp3s0"
```

## Interface Detection

The playbook automatically detects the primary network interface using this priority:

1. **Default route interface** - Interface used by the default route
2. **Ansible default interface** - Interface detected by Ansible facts
3. **First physical ethernet interface** - First interface matching `en*` or `eth*` patterns

To override auto-detection, set the interface manually:
```bash
-e "openvpn_client_wired_interface=your-interface-name"
```

## S3 Configuration Download

The role can download client configurations from S3 (for cross-account setups):

```bash
# Enable S3 download
ansible-playbook -i hosts.yml playbooks/infra_openvpn_client.yml \
  -e "run_hosts=server-name" \
  -e "vpn_config_s3_bucket=eyecue-francium-vpn-client-configs" \
  -e "vpn_config_s3_region=ap-southeast-2"
```

**How it works:**
- Downloads `clients/client.ovpn` from the specified S3 bucket
- Uses the server's IAM role for cross-account access
- Falls back to local files if S3 variables not provided

## Management Commands

```bash
# VPN Control
sudo /usr/local/bin/vpn-manager.sh start    # Start VPN
sudo /usr/local/bin/vpn-manager.sh status   # Check status
sudo /usr/local/bin/vpn-manager.sh stop     # Stop VPN

# Service Management
sudo systemctl enable openvpn-client        # Auto-start on boot
sudo systemctl disable openvpn-client       # Disable auto-start

# Troubleshooting
sudo /usr/local/bin/vpn-troubleshoot.sh     # Comprehensive diagnostics
```

## Validation

After setup, verify VPN is working:

```bash
# Should return VPN gateway IP, not server's public IP
curl ifconfig.me

# Check VPN interface exists
ip addr show tun0

# Verify routing through VPN
ip route | grep tun0
```
