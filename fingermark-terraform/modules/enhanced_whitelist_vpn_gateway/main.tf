locals {
  az_count  = length(var.azs)
  base_name = "${var.project_name}-${var.environment}"
}

# --- Base Networking Resources ---

# Generate a short random suffix for resource names
resource "random_string" "suffix" {
  length  = 6
  special = false
  upper   = false
}

resource "aws_vpc" "main" {
  cidr_block           = var.vpc_cidr_block
  enable_dns_support   = true
  enable_dns_hostnames = true

  tags = merge(var.tags, {
    Name = "${local.base_name}-vpc"
  })
}

resource "aws_internet_gateway" "gw" {
  vpc_id = aws_vpc.main.id

  tags = merge(var.tags, {
    Name = "${local.base_name}-igw"
  })
}

# --- Subnets ---
resource "aws_subnet" "public" {
  count = local.az_count

  vpc_id                  = aws_vpc.main.id
  cidr_block              = var.public_subnet_cidrs[count.index]
  availability_zone       = var.azs[count.index]
  map_public_ip_on_launch = true

  tags = merge(var.tags, {
    Name       = "${local.base_name}-public-${var.azs[count.index]}"
    SubnetType = "public"
  })
}

resource "aws_subnet" "private" {
  count = local.az_count

  vpc_id                  = aws_vpc.main.id
  cidr_block              = var.private_subnet_cidrs[count.index]
  availability_zone       = var.azs[count.index]
  map_public_ip_on_launch = false

  tags = merge(var.tags, {
    Name       = "${local.base_name}-private-${var.azs[count.index]}"
    SubnetType = "private"
  })
}

# --- NAT Gateways (Highly Available - one per AZ or single for EIP conservation) ---
resource "aws_eip" "nat" {
  count = var.enable_nat_gateway ? (var.single_nat_gateway ? 1 : local.az_count) : 0

  domain     = "vpc"
  depends_on = [aws_internet_gateway.gw]

  tags = merge(var.tags, {
    Name = var.single_nat_gateway ? "${local.base_name}-nat-eip" : "${local.base_name}-nat-eip-${var.azs[count.index]}"
  })
}

resource "aws_nat_gateway" "nat" {
  count = var.enable_nat_gateway ? (var.single_nat_gateway ? 1 : local.az_count) : 0

  allocation_id = aws_eip.nat[count.index].id
  subnet_id     = var.single_nat_gateway ? aws_subnet.public[0].id : aws_subnet.public[count.index].id

  tags = merge(var.tags, {
    Name = var.single_nat_gateway ? "${local.base_name}-nat-gw" : "${local.base_name}-nat-gw-${var.azs[count.index]}"
  })

  depends_on = [aws_internet_gateway.gw]
}


# --- Route Tables ---

# Public Route Table (Routes to IGW)
resource "aws_route_table" "public" {
  vpc_id = aws_vpc.main.id

  route {
    cidr_block = "0.0.0.0/0"
    gateway_id = aws_internet_gateway.gw.id
  }

  tags = merge(var.tags, {
    Name       = "${local.base_name}-public-rtb"
    SubnetType = "public"
  })
}

resource "aws_route_table_association" "public" {
  count = local.az_count

  subnet_id      = aws_subnet.public[count.index].id
  route_table_id = aws_route_table.public.id
}


# Private Route Tables with NAT Gateway routes
resource "aws_route_table" "private" {
  count = local.az_count

  vpc_id = aws_vpc.main.id

  tags = merge(var.tags, {
    Name       = "${local.base_name}-private-${var.azs[count.index]}-rtb"
    SubnetType = "private"
  })
}

resource "aws_route_table_association" "private" {
  count = local.az_count

  subnet_id      = aws_subnet.private[count.index].id
  route_table_id = aws_route_table.private[count.index].id
}

# Routes from private subnets to NAT Gateways for internet access
resource "aws_route" "private_to_nat" {
  count = var.enable_nat_gateway ? local.az_count : 0

  route_table_id         = aws_route_table.private[count.index].id
  destination_cidr_block = "0.0.0.0/0"
  nat_gateway_id         = var.single_nat_gateway ? aws_nat_gateway.nat[0].id : aws_nat_gateway.nat[count.index].id
}

# --- Elastic IP for Single VPN Instance ---
resource "aws_eip" "single_vpn_instance_eip" {
  count = !var.enable_auto_scaling && var.enable_single_instance_vpn ? 1 : 0

  domain = "vpc"
  tags = merge(var.tags, {
    Name = "${local.base_name}-single-vpn-eip"
  })
}

# --- Single VPN Gateway Instance (if not using Auto Scaling) ---
resource "aws_instance" "single_vpn_instance" {
  count = !var.enable_auto_scaling && var.enable_single_instance_vpn ? 1 : 0

  ami           = var.vpn_gateway_ami
  instance_type = var.vpn_instance_type
  subnet_id     = aws_subnet.public[0].id

  vpc_security_group_ids = [aws_security_group.vpn_gateway.id]
  iam_instance_profile   = aws_iam_instance_profile.vpn_instance_profile.name
  source_dest_check      = false

  # Use the templated user_data script for OpenVPN Community Edition
  user_data = base64encode(
    templatefile("${path.module}/templates/openvpn_userdata.sh.tpl", {
      vpn_client_cidr_block   = var.vpn_client_cidr_block
      vpn_port                = var.vpn_port
      vpc_cidr_block          = var.vpc_cidr_block
      key_s3_bucket           = var.key_s3_bucket
      ca_s3_key               = var.ca_s3_key
      dh_s3_key               = var.dh_s3_key
      client_config_s3_bucket = var.client_config_s3_bucket
      aws_region              = var.aws_region
      enable_load_balancer    = var.enable_load_balancer
      project_name            = var.project_name
      environment             = var.environment
    })
  )

  tags = merge(var.tags, { Name = "${local.base_name}-single-vpn-gateway" })
}

resource "aws_eip_association" "single_vpn_eip_assoc" {
  count = !var.enable_auto_scaling && var.enable_single_instance_vpn ? 1 : 0

  instance_id   = aws_instance.single_vpn_instance[0].id
  allocation_id = aws_eip.single_vpn_instance_eip[0].id
}

# --- Route for On-Prem Clients via Single VPN Instance ---
resource "aws_route" "private_to_onprem_single_instance" {
  count = (!var.enable_auto_scaling && var.enable_single_instance_vpn && length(aws_subnet.private) > 0 && length(aws_instance.single_vpn_instance) > 0) ? length(aws_subnet.private) : 0

  route_table_id         = aws_route_table.private[count.index].id
  destination_cidr_block = var.vpn_client_cidr_block
  network_interface_id   = aws_instance.single_vpn_instance[0].primary_network_interface_id

  depends_on = [aws_instance.single_vpn_instance]
}

# --- VPN Gateway Security Group ---
resource "aws_security_group" "vpn_gateway" {
  name        = "${local.base_name}-vpn-gateway-sg"
  description = "Allow OpenVPN (UDP 1194), NLB Health Checks (TCP 1194), and SSH access"
  vpc_id      = aws_vpc.main.id

  ingress {
    description = "Allow OpenVPN UDP from anywhere (Restrict source IPs if possible)"
    from_port   = var.vpn_port # Typically 1194
    to_port     = var.vpn_port # Typically 1194
    protocol    = "udp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
    description = "Allow NLB Health Checks (TCP on traffic port)"
    from_port   = var.vpn_port # Typically 1194, same as listener/traffic port
    to_port     = var.vpn_port # Typically 1194, same as listener/traffic port
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"] # Restrict to VPC CIDR or NLB subnet CIDRs in production
  }

  ingress {
    description = "Allow HTTPS for OpenVPN AS Web UI and NLB Health Checks"
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"] # Restrict to VPC CIDR or NLB subnet CIDRs in production
  }

  ingress {
    description = "Allow SSH from admin ranges (defined in variable)"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = var.admin_ssh_cidr_blocks
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = merge(var.tags, { Name = "${local.base_name}-vpn-gateway-sg" })
}

# --- IAM Role for OpenVPN Instances ---
resource "aws_iam_role" "vpn_instance_role" {
  name = "${local.base_name}-vpn-instance-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [{
      Action = "sts:AssumeRole"
      Effect = "Allow"
      Principal = {
        Service = "ec2.amazonaws.com"
      }
    }]
  })

  tags = var.tags
}

# Policy for SSM access if enabled
resource "aws_iam_policy" "vpn_ssm_policy" {
  count = var.enable_ssm_access ? 1 : 0

  name        = "${local.base_name}-vpn-ssm-policy"
  description = "Policy for SSM Session Manager access to VPN instances"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "ssm:UpdateInstanceInformation",
          "ssm:ListInstanceAssociations",
          "ssm:DescribeInstanceProperties",
          "ssm:DescribeDocumentParameters",
          "ssmmessages:CreateControlChannel",
          "ssmmessages:CreateDataChannel",
          "ssmmessages:OpenControlChannel",
          "ssmmessages:OpenDataChannel"
        ]
        Effect   = "Allow"
        Resource = "*"
      }
    ]
  })
}

# Policy for enhanced CloudWatch monitoring if enabled
resource "aws_iam_policy" "vpn_cloudwatch_policy" {
  count = var.enable_enhanced_monitoring ? 1 : 0

  name        = "${local.base_name}-vpn-cloudwatch-policy"
  description = "Policy for enhanced CloudWatch monitoring of VPN instances"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "cloudwatch:PutMetricData",
          "ec2:DescribeTags",
          "logs:PutLogEvents",
          "logs:DescribeLogStreams",
          "logs:DescribeLogGroups",
          "logs:CreateLogStream",
          "logs:CreateLogGroup",
          "logs:PutRetentionPolicy"
        ]
        Effect   = "Allow"
        Resource = "*"
      }
    ]
  })
}

# Attach policies to role
resource "aws_iam_role_policy_attachment" "vpn_ssm_attachment" {
  count = var.enable_ssm_access ? 1 : 0

  role       = aws_iam_role.vpn_instance_role.name
  policy_arn = aws_iam_policy.vpn_ssm_policy[0].arn
}

resource "aws_iam_role_policy_attachment" "vpn_cloudwatch_attachment" {
  count = var.enable_enhanced_monitoring ? 1 : 0

  role       = aws_iam_role.vpn_instance_role.name
  policy_arn = aws_iam_policy.vpn_cloudwatch_policy[0].arn
}

# Create instance profile from role
resource "aws_iam_instance_profile" "vpn_instance_profile" {
  name = "${local.base_name}-vpn-instance-profile"
  role = aws_iam_role.vpn_instance_role.name
}

# --- Load Balancer for VPN Traffic ---
resource "aws_lb" "vpn_nlb" {
  count = var.enable_load_balancer ? 1 : 0

  name               = "${local.base_name}-lb"
  internal           = var.nlb_internal
  load_balancer_type = "network"
  subnets            = aws_subnet.public[*].id

  enable_deletion_protection       = false
  enable_cross_zone_load_balancing = true

  tags = merge(var.tags, {
    Name = "${local.base_name}-vpn-nlb"
  })
}

resource "aws_lb_target_group" "vpn_target_group" {
  count = var.enable_load_balancer ? 1 : 0

  name     = "${local.base_name}-tg"
  port     = var.vpn_port # This is the data traffic port (e.g., 1194 UDP)
  protocol = "UDP"        # Protocol for data traffic forwarding
  vpc_id   = aws_vpc.main.id

  health_check {
    enabled             = true
    port                = "443" # For OpenVPN AS TCP service
    protocol            = "TCP"
    healthy_threshold   = 2
    unhealthy_threshold = 3
    interval            = 60
    timeout             = 30
  }

  tags = merge(var.tags, {
    Name = "${local.base_name}-vpn-tg"
  })
}

resource "aws_lb_listener" "vpn_listener" {
  count = var.enable_load_balancer ? 1 : 0

  load_balancer_arn = aws_lb.vpn_nlb[0].arn
  port              = var.vpn_port
  protocol          = "UDP"

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.vpn_target_group[0].arn
  }
}

# --- Auto Scaling Group for VPN Instances ---

# Launch Template for VPN instances
resource "aws_launch_template" "vpn_launch_template" {
  name_prefix   = "${local.base_name}-vpn-lt-"
  image_id      = var.vpn_gateway_ami
  instance_type = var.vpn_instance_type

  iam_instance_profile {
    name = aws_iam_instance_profile.vpn_instance_profile.name
  }

  vpc_security_group_ids = [aws_security_group.vpn_gateway.id]

  monitoring {
    enabled = var.enable_enhanced_monitoring
  }

  # Use the templated user_data script for OpenVPN Community Edition
  user_data = base64encode(
    templatefile("${path.module}/templates/openvpn_userdata.sh.tpl", {
      vpn_client_cidr_block   = var.vpn_client_cidr_block
      vpn_port                = var.vpn_port
      vpc_cidr_block          = var.vpc_cidr_block
      key_s3_bucket           = var.key_s3_bucket
      ca_s3_key               = var.ca_s3_key
      dh_s3_key               = var.dh_s3_key
      client_config_s3_bucket = var.client_config_s3_bucket
      aws_region              = var.aws_region
      enable_load_balancer    = var.enable_load_balancer
      project_name            = var.project_name
      environment             = var.environment
    })
  )

  metadata_options {
    http_endpoint               = "enabled"
    http_tokens                 = "required"
    http_put_response_hop_limit = 1
  }

  instance_initiated_shutdown_behavior = "terminate"

  tag_specifications {
    resource_type = "instance"
    tags = merge(var.tags, {
      Name = "${local.base_name}-vpn-instance"
    })
  }

  lifecycle {
    create_before_destroy = true
  }
}

# Auto Scaling Group
resource "aws_autoscaling_group" "vpn_asg" {
  count = var.enable_auto_scaling ? 1 : 0

  name                = "${local.base_name}-vpn-asg"
  min_size            = var.min_size
  max_size            = var.max_size
  desired_capacity    = var.desired_capacity
  vpc_zone_identifier = var.nlb_internal ? aws_subnet.private[*].id : aws_subnet.public[*].id

  launch_template {
    id      = aws_launch_template.vpn_launch_template.id
    version = "$Latest"
  }

  target_group_arns = var.enable_load_balancer ? [aws_lb_target_group.vpn_target_group[0].arn] : []

  health_check_type         = "ELB"
  health_check_grace_period = 600

  wait_for_capacity_timeout = "10m"

  instance_refresh {
    strategy = "Rolling"
    preferences {
      min_healthy_percentage = 50
    }
  }

  enabled_metrics = [
    "GroupMinSize",
    "GroupMaxSize",
    "GroupDesiredCapacity",
    "GroupInServiceInstances",
    "GroupPendingInstances",
    "GroupStandbyInstances",
    "GroupTerminatingInstances",
    "GroupTotalInstances"
  ]

  lifecycle {
    create_before_destroy = true
    ignore_changes        = [desired_capacity]
  }

  tag {
    key                 = "Name"
    value               = "${local.base_name}-vpn-instance"
    propagate_at_launch = true
  }

  dynamic "tag" {
    for_each = var.tags
    content {
      key                 = tag.key
      value               = tag.value
      propagate_at_launch = true
    }
  }
}

# --- Auto Scaling Policies ---
resource "aws_autoscaling_policy" "scale_up" {
  count = var.enable_auto_scaling ? 1 : 0

  name                   = "${local.base_name}-vpn-scale-up"
  autoscaling_group_name = aws_autoscaling_group.vpn_asg[0].name
  adjustment_type        = "ChangeInCapacity"
  scaling_adjustment     = 1
  cooldown               = 300
}

resource "aws_autoscaling_policy" "scale_down" {
  count = var.enable_auto_scaling ? 1 : 0

  name                   = "${local.base_name}-vpn-scale-down"
  autoscaling_group_name = aws_autoscaling_group.vpn_asg[0].name
  adjustment_type        = "ChangeInCapacity"
  scaling_adjustment     = -1
  cooldown               = 300
}

# --- CloudWatch Alarms for Auto Scaling ---
resource "aws_cloudwatch_metric_alarm" "high_cpu" {
  count = var.enable_auto_scaling && var.create_cloudwatch_alarms ? 1 : 0

  alarm_name          = "${local.base_name}-vpn-high-cpu"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 2
  metric_name         = "CPUUtilization"
  namespace           = "AWS/EC2"
  period              = 60
  statistic           = "Average"
  threshold           = var.scale_up_threshold
  alarm_description   = "This metric triggers when CPU utilization is above ${var.scale_up_threshold}% for 2 consecutive periods"

  dimensions = {
    AutoScalingGroupName = aws_autoscaling_group.vpn_asg[0].name
  }

  alarm_actions = [aws_autoscaling_policy.scale_up[0].arn]
}

resource "aws_cloudwatch_metric_alarm" "low_cpu" {
  count = var.enable_auto_scaling && var.create_cloudwatch_alarms ? 1 : 0

  alarm_name          = "${local.base_name}-vpn-low-cpu"
  comparison_operator = "LessThanOrEqualToThreshold"
  evaluation_periods  = 2
  metric_name         = "CPUUtilization"
  namespace           = "AWS/EC2"
  period              = 60
  statistic           = "Average"
  threshold           = var.scale_down_threshold
  alarm_description   = "This metric triggers when CPU utilization is below ${var.scale_down_threshold}% for 2 consecutive periods"

  dimensions = {
    AutoScalingGroupName = aws_autoscaling_group.vpn_asg[0].name
  }

  alarm_actions = [aws_autoscaling_policy.scale_down[0].arn]
}

# --- CloudWatch Log Group for VPN Logs ---
resource "aws_cloudwatch_log_group" "vpn_logs" {
  count = var.enable_enhanced_monitoring ? 1 : 0

  name              = "${local.base_name}-vpn-logs"
  retention_in_days = 30

  tags = var.tags
}

# --- SNS Topic for Alarms ---
resource "aws_sns_topic" "vpn_alarms" {
  count = var.create_cloudwatch_alarms && var.alarm_notification_email != "" ? 1 : 0

  name = "${local.base_name}-vpn-alarms"
  tags = var.tags
}

resource "aws_sns_topic_subscription" "vpn_alarms_email" {
  count = var.create_cloudwatch_alarms && var.alarm_notification_email != "" ? 1 : 0

  topic_arn = aws_sns_topic.vpn_alarms[0].arn
  protocol  = "email"
  endpoint  = var.alarm_notification_email
}

# --- Additional CloudWatch Alarms ---
resource "aws_cloudwatch_metric_alarm" "instance_health" {
  count = var.create_cloudwatch_alarms && var.alarm_notification_email != "" ? 1 : 0

  alarm_name          = "${local.base_name}-vpn-instance-health"
  comparison_operator = "LessThanThreshold"
  evaluation_periods  = 1
  metric_name         = "HealthyHostCount"
  namespace           = "AWS/NetworkELB"
  period              = 60
  statistic           = "Average"
  threshold           = var.min_size
  alarm_description   = "This metric triggers when the number of healthy VPN instances falls below ${var.min_size}"

  dimensions = {
    LoadBalancer = var.enable_load_balancer ? aws_lb.vpn_nlb[0].arn_suffix : ""
    TargetGroup  = var.enable_load_balancer ? aws_lb_target_group.vpn_target_group[0].arn_suffix : ""
  }

  alarm_actions = var.alarm_notification_email != "" ? [aws_sns_topic.vpn_alarms[0].arn] : []
}
