# Testing Guide: Enhanced Whitelist VPN Gateway

Essential validation steps for the VPN gateway module after deployment.

## Prerequisites

- ✅ Module deployed successfully via Terraform
- ✅ Ansible playbook `infra_openvpn_client` available for client setup

## 1. Infrastructure Validation

### AWS Console Check
Verify these components in the AWS Console:

| Component | Location | Status Check |
|-----------|----------|--------------|
| **EC2 Instances** | EC2 > Instances | All VPN instances running |
| **Load Balancer** | EC2 > Load Balancers | NLB healthy with targets |
| **Auto Scaling Group** | EC2 > Auto Scaling Groups | Desired capacity met |
| **S3 Bucket** | S3 Console | Config bucket contains .ovpn files |

### Success Criteria
- ✅ All EC2 instances in "running" state
- ✅ Load Balancer targets show "healthy" status
- ✅ S3 bucket contains client configurations

## 2. Client Setup and Testing

### Setup On-Premise Server
Use the Ansible playbook to configure OpenVPN on your test server:

```bash
# Run the OpenVPN client playbook
ansible-playbook -i hosts.yml playbooks/infra_openvpn_client.yml -e "run_hosts=test-server"
```

### Verify VPN Connection
On the configured server, test the VPN connection:

```bash
# Check VPN interface exists
ip addr show tun0

# Verify traffic routes through VPN gateway
curl ifconfig.me  # Should return VPN gateway IP, not server's public IP

# Test routing table
ip route | grep tun0  # Should show VPN routes
```

### Success Criteria
- ✅ `tun0` interface exists with VPN IP
- ✅ `curl ifconfig.me` returns VPN gateway IP
- ✅ All egress traffic routes through VPN tunnel

## 3. Connectivity Testing

### Basic Internet Access
```bash
# Test DNS resolution and connectivity
nslookup google.com
ping -c 3 google.com

# Test HTTPS connectivity
curl -I https://www.google.com
```

### Success Criteria
- ✅ DNS resolution works
- ✅ Internet connectivity through VPN
- ✅ HTTPS traffic flows correctly

## 4. High Availability Testing

### Auto Scaling Group (if enabled)
Test failover by terminating one VPN instance in the AWS Console:
1. Go to EC2 > Instances
2. Terminate one VPN instance
3. Verify Auto Scaling Group launches replacement
4. Test VPN connectivity remains stable

### Single Instance Mode
Test instance restart:
1. Stop/start the VPN instance in AWS Console
2. Verify Elastic IP re-associates
3. Test VPN reconnection works

## 5. Troubleshooting

### Common Issues

**VPN Connection Fails**
- Check Load Balancer target health in AWS Console
- Verify security groups allow UDP 1194
- Check VPN instance logs in CloudWatch

**No Internet Access Through VPN**
- Verify NAT Gateway is running
- Check route tables in VPC Console
- Test with `curl ifconfig.me` to confirm traffic routing

**Client Configuration Issues**
- Check S3 bucket contains .ovpn files
- Verify Load Balancer DNS name is correct
- Use Ansible playbook for automated setup

### Validation Commands
```bash
# On the VPN client server, run these to validate:
sudo /usr/local/bin/vpn-troubleshoot.sh  # Comprehensive diagnostics
sudo /usr/local/bin/vpn-manager.sh status  # Check VPN status
```

## Cleanup

When testing is complete:
```bash
terraform destroy
```
