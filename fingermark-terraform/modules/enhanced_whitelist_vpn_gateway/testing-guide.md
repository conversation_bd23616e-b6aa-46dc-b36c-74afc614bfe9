# Testing Guide: Enhanced Whitelist VPN Gateway

Quick validation steps for the VPN gateway module after deployment.

## Prerequisites

- ✅ Module deployed successfully via Terraform
- ✅ OpenVPN client installed ([OpenVPN Connect](https://openvpn.net/client-connect-vpn-for-windows/), [Tunnelblick](https://tunnelblick.net/), etc.)
- ✅ AWS CLI configured with S3 access permissions

## 1. Infrastructure Validation

### Quick Health Check
```bash
# Check VPN instances are running
aws ec2 describe-instances --filters "Name=tag:Name,Values=*vpn*" \
  --query 'Reservations[].Instances[].[InstanceId,State.Name,Tags[?Key==`Name`].Value|[0]]' \
  --output table

# Verify NLB target health (if using Auto Scaling)
aws elbv2 describe-target-health --target-group-arn <target-group-arn>
```

### AWS Console Verification
| Component | Location | Check |
|-----------|----------|-------|
| **VPC** | VPC Dashboard | VPC exists with correct CIDR |
| **Subnets** | VPC > Subnets | Public/private subnets in each AZ |
| **NAT Gateway** | VPC > NAT Gateways | NAT Gateway(s) with Elastic IPs |
| **Auto Scaling Group** | EC2 > Auto Scaling Groups | ASG healthy with desired capacity |
| **Load Balancer** | EC2 > Load Balancers | NLB with healthy targets |
| **S3 Bucket** | S3 Console | Config bucket exists |
| **IAM Role** | IAM > Roles | Instance role with required policies |

### Success Criteria
- ✅ All EC2 instances in "running" state
- ✅ NLB targets show "healthy" status
- ✅ S3 bucket accessible and contains client configs
- ✅ CloudWatch logs receiving data

## 2. VPN Connectivity Test

### Download Client Configuration (.ovpn file)
```bash
aws s3 cp s3://<config-bucket-name>/clients/<client-name>.ovpn ./
```

### Connect and Verify
1. **Import Configuration**: Load the `.ovpn` file into your OpenVPN client
2. **Connect**: Use the NLB DNS name or instance EIP from Terraform outputs
3. **Verify Connection**:
   ```bash
   # Check your VPN-assigned IP (should be from vpn_client_cidr_block)
   ip addr show tun0  # Linux/macOS

   # Verify traffic routes through VPN
   curl -s https://ifconfig.me  # Should show NAT Gateway IP
   ```

### Success Criteria
- ✅ Client receives IP from VPN CIDR range
- ✅ Public IP shows NAT Gateway address
- ✅ OpenVPN client logs show successful connection
- ✅ Can access allowed FQDNs through VPN

## 3. Traffic and FQDN Testing

### Basic Connectivity
```bash
# Test internet access through VPN
ping -c 3 google.com
curl -I https://www.google.com

# Verify routing (optional)
traceroute google.com  # Should show VPN server in path
```

### FQDN Whitelisting Test
> **Note**: FQDN filtering requires additional configuration on VPN instances

If FQDN filtering is configured:
```bash
# Test allowed domain (should succeed)
curl -I https://github.com

# Test blocked domain (should fail if filtering active)
curl -I https://blocked-site.com
```

### Success Criteria
- ✅ Basic internet connectivity works
- ✅ Traffic routes through VPN infrastructure
- ✅ FQDN filtering works as configured (if implemented)

## 4. High Availability Testing

### Auto Scaling Group (if enabled)
```bash
# Test instance replacement
aws autoscaling terminate-instance-in-auto-scaling-group \
  --instance-id <instance-id> --should-decrement-desired-capacity false

# Monitor replacement
aws autoscaling describe-auto-scaling-groups \
  --auto-scaling-group-names <asg-name> \
  --query 'AutoScalingGroups[0].Instances[*].[InstanceId,LifecycleState]'
```

### Single Instance Mode
```bash
# Test instance restart
aws ec2 stop-instances --instance-ids <instance-id>
aws ec2 start-instances --instance-ids <instance-id>

# Verify EIP re-association
aws ec2 describe-addresses --filters "Name=instance-id,Values=<instance-id>"
```

## 5. Monitoring Validation

### CloudWatch Checks
```bash
# Check log groups exist
aws logs describe-log-groups --log-group-name-prefix <project-name>

# Verify metrics are being collected
aws cloudwatch list-metrics --namespace AWS/EC2 \
  --dimensions Name=InstanceId,Value=<instance-id>

# Test alarms (if configured)
aws cloudwatch describe-alarms --alarm-names <alarm-name>
```

### Success Criteria
- ✅ CloudWatch logs receiving VPN server data
- ✅ EC2 and NLB metrics available
- ✅ Alarms configured and functional
- ✅ SNS notifications working (if configured)

## Troubleshooting

### Common Issues
| Issue | Check | Solution |
|-------|-------|----------|
| **Connection fails** | NLB target health | Verify security groups, health checks |
| **No client configs** | S3 bucket contents | Check IAM permissions, server logs |
| **High costs** | Cost Explorer | Enable Spot instances, single NAT Gateway |
| **Scaling issues** | ASG activity | Review CloudWatch metrics, scaling policies |

### Quick Diagnostics
```bash
# Check recent errors
aws logs filter-log-events --log-group-name <log-group> \
  --filter-pattern "ERROR" --start-time $(date -d '1 hour ago' +%s)000
```

## Cleanup

```bash
# Destroy infrastructure when testing complete
terraform destroy

# Verify all resources removed
aws ec2 describe-instances --filters "Name=tag:Name,Values=*vpn*"
```
