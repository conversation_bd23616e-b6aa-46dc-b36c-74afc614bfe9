#!/bin/bash
# Optimized OpenVPN Bootstrap Script - Downloads full setup from S3
set -euo pipefail
exec > >(tee /var/log/openvpn-userdata.log) 2>&1

echo "=== OpenVPN Bootstrap Started at $(date) ==="

# Environment
export DEBIAN_FRONTEND=noninteractive
export AWS_DEFAULT_REGION="${aws_region}"
INSTANCE_ID=$(curl -s http://***************/latest/meta-data/instance-id)
VPN_PORT="${vpn_port}"
VPN_CIDR="${vpn_client_cidr_block}"
PKI_S3_BUCKET="${key_s3_bucket}"
CLIENT_S3_BUCKET="${client_config_s3_bucket}"

echo "Instance: $INSTANCE_ID, Port: $VPN_PORT, CIDR: $VPN_CIDR"

# Install essentials
apt-get update -qq && apt-get install -y -qq openvpn easy-rsa awscli curl wget

# Network setup
echo 'net.ipv4.ip_forward = 1' > /etc/sysctl.d/99-openvpn.conf
sysctl -p /etc/sysctl.d/99-openvpn.conf
iptables -t nat -A POSTROUTING -s $VPN_CIDR -o eth0 -j MASQUERADE
iptables -A FORWARD -s $VPN_CIDR -j ACCEPT
iptables -A FORWARD -d $VPN_CIDR -m state --state RELATED,ESTABLISHED -j ACCEPT
iptables -A INPUT -p udp --dport $VPN_PORT -j ACCEPT
iptables -A INPUT -p tcp --dport 443 -j ACCEPT

# Directory setup
mkdir -p /etc/openvpn/{keys,clients,client-configs,easy-rsa}
chmod 700 /etc/openvpn/keys

# PKI Management with proper Easy-RSA setup
cp -r /usr/share/easy-rsa/* /etc/openvpn/easy-rsa/
cd /etc/openvpn/easy-rsa

# Create vars file for non-interactive operation
cat > vars << EOF
set_var EASYRSA_REQ_COUNTRY    "AU"
set_var EASYRSA_REQ_PROVINCE   "NSW"
set_var EASYRSA_REQ_CITY       "Sydney"
set_var EASYRSA_REQ_ORG        "OpenVPN-Community"
set_var EASYRSA_REQ_EMAIL      "<EMAIL>"
set_var EASYRSA_REQ_OU         "IT"
set_var EASYRSA_KEY_SIZE       2048
set_var EASYRSA_ALGO           rsa
set_var EASYRSA_CA_EXPIRE      3650
set_var EASYRSA_CERT_EXPIRE    365
set_var EASYRSA_BATCH          "yes"
set_var EASYRSA_REQ_CN         "OpenVPN-CA"
EOF

./easyrsa init-pki

CA_DIR=$(dirname "${ca_s3_key}")

if aws s3api head-object --bucket "$PKI_S3_BUCKET" --key "${ca_s3_key}" --region "$AWS_DEFAULT_REGION" >/dev/null 2>&1; then
    echo "Downloading existing PKI..."
    aws s3 cp s3://$PKI_S3_BUCKET/${ca_s3_key} pki/ca.crt --region "$AWS_DEFAULT_REGION"
    aws s3 cp s3://$PKI_S3_BUCKET/${dh_s3_key} pki/dh.pem --region "$AWS_DEFAULT_REGION"
    aws s3 cp s3://$PKI_S3_BUCKET/$CA_DIR/ta.key pki/ta.key --region "$AWS_DEFAULT_REGION"
    aws s3 cp s3://$PKI_S3_BUCKET/$CA_DIR/ca.key pki/private/ca.key --region "$AWS_DEFAULT_REGION"
    
    # Create required Easy-RSA directories that aren't created by init-pki
    mkdir -p pki/issued pki/revoked pki/certs_by_serial pki/renewed
    
    # Download PKI database files - check both expected path and root level
    if aws s3api head-object --bucket "$PKI_S3_BUCKET" --key "$CA_DIR/index.txt" --region "$AWS_DEFAULT_REGION" >/dev/null 2>&1; then
        echo "Downloading PKI database files from $CA_DIR/"
        aws s3 cp s3://$PKI_S3_BUCKET/$CA_DIR/index.txt pki/index.txt --region "$AWS_DEFAULT_REGION"
        aws s3 cp s3://$PKI_S3_BUCKET/$CA_DIR/serial pki/serial --region "$AWS_DEFAULT_REGION"
        # Download index.txt.attr if it exists
        if aws s3api head-object --bucket "$PKI_S3_BUCKET" --key "$CA_DIR/index.txt.attr" --region "$AWS_DEFAULT_REGION" >/dev/null 2>&1; then
            aws s3 cp s3://$PKI_S3_BUCKET/$CA_DIR/index.txt.attr pki/index.txt.attr --region "$AWS_DEFAULT_REGION"
        else
            echo "unique_subject = no" > pki/index.txt.attr
        fi
    elif aws s3api head-object --bucket "$PKI_S3_BUCKET" --key "index.txt" --region "$AWS_DEFAULT_REGION" >/dev/null 2>&1; then
        echo "Downloading PKI database files from root level"
        aws s3 cp s3://$PKI_S3_BUCKET/index.txt pki/index.txt --region "$AWS_DEFAULT_REGION"
        aws s3 cp s3://$PKI_S3_BUCKET/serial pki/serial --region "$AWS_DEFAULT_REGION"
        # Check for ta.key and ca.key at root level too
        if aws s3api head-object --bucket "$PKI_S3_BUCKET" --key "ta.key" --region "$AWS_DEFAULT_REGION" >/dev/null 2>&1; then
            aws s3 cp s3://$PKI_S3_BUCKET/ta.key pki/ta.key --region "$AWS_DEFAULT_REGION"
        fi
        if aws s3api head-object --bucket "$PKI_S3_BUCKET" --key "ca.key" --region "$AWS_DEFAULT_REGION" >/dev/null 2>&1; then
            aws s3 cp s3://$PKI_S3_BUCKET/ca.key pki/private/ca.key --region "$AWS_DEFAULT_REGION"
        fi
        # Create index.txt.attr if it doesn't exist
        if aws s3api head-object --bucket "$PKI_S3_BUCKET" --key "index.txt.attr" --region "$AWS_DEFAULT_REGION" >/dev/null 2>&1; then
            aws s3 cp s3://$PKI_S3_BUCKET/index.txt.attr pki/index.txt.attr --region "$AWS_DEFAULT_REGION"
        else
            echo "unique_subject = no" > pki/index.txt.attr
        fi
        
        # Create required Easy-RSA directories that aren't created by init-pki
        mkdir -p pki/issued pki/revoked pki/certs_by_serial pki/renewed
    else
        # Create PKI database files for existing CA
        echo "Creating PKI database files for existing CA..."
        touch pki/index.txt
        echo "01" > pki/serial
        # Create index.txt.attr file for certificate database
        echo "unique_subject = no" > pki/index.txt.attr
        # Upload the database files to S3 for future use
        aws s3 cp pki/index.txt s3://$PKI_S3_BUCKET/$CA_DIR/index.txt --region "$AWS_DEFAULT_REGION"
        aws s3 cp pki/serial s3://$PKI_S3_BUCKET/$CA_DIR/serial --region "$AWS_DEFAULT_REGION"
        aws s3 cp pki/index.txt.attr s3://$PKI_S3_BUCKET/$CA_DIR/index.txt.attr --region "$AWS_DEFAULT_REGION"
    fi
else
    echo "Generating new PKI..."
    ./easyrsa build-ca nopass
    ./easyrsa gen-dh
    openvpn --genkey --secret pki/ta.key
    
    # Upload all PKI files including database files
    aws s3 cp pki/ca.crt s3://$PKI_S3_BUCKET/${ca_s3_key} --region "$AWS_DEFAULT_REGION"
    aws s3 cp pki/dh.pem s3://$PKI_S3_BUCKET/${dh_s3_key} --region "$AWS_DEFAULT_REGION"
    aws s3 cp pki/ta.key s3://$PKI_S3_BUCKET/$CA_DIR/ta.key --region "$AWS_DEFAULT_REGION"
    aws s3 cp pki/private/ca.key s3://$PKI_S3_BUCKET/$CA_DIR/ca.key --region "$AWS_DEFAULT_REGION"
    aws s3 cp pki/index.txt s3://$PKI_S3_BUCKET/$CA_DIR/index.txt --region "$AWS_DEFAULT_REGION"
    aws s3 cp pki/serial s3://$PKI_S3_BUCKET/$CA_DIR/serial --region "$AWS_DEFAULT_REGION"
    aws s3 cp pki/index.txt.attr s3://$PKI_S3_BUCKET/$CA_DIR/index.txt.attr --region "$AWS_DEFAULT_REGION"
fi

# Generate server cert
./easyrsa gen-req "server-$INSTANCE_ID" nopass
./easyrsa sign-req server "server-$INSTANCE_ID"

# Update PKI database in S3 after generating server cert
aws s3 cp pki/index.txt s3://$PKI_S3_BUCKET/$CA_DIR/index.txt --region "$AWS_DEFAULT_REGION"
aws s3 cp pki/serial s3://$PKI_S3_BUCKET/$CA_DIR/serial --region "$AWS_DEFAULT_REGION"
aws s3 cp pki/index.txt.attr s3://$PKI_S3_BUCKET/$CA_DIR/index.txt.attr --region "$AWS_DEFAULT_REGION"

# Copy certs
cp pki/ca.crt /etc/openvpn/keys/
cp pki/issued/server-$INSTANCE_ID.crt /etc/openvpn/keys/server.crt
cp pki/private/server-$INSTANCE_ID.key /etc/openvpn/keys/server.key
cp pki/dh.pem /etc/openvpn/keys/dh2048.pem
cp pki/ta.key /etc/openvpn/keys/
chmod 600 /etc/openvpn/keys/*

# OpenVPN config
cat > /etc/openvpn/server.conf << EOF
port $VPN_PORT
proto udp
dev tun
ca /etc/openvpn/keys/ca.crt
cert /etc/openvpn/keys/server.crt
key /etc/openvpn/keys/server.key
dh /etc/openvpn/keys/dh2048.pem
tls-auth /etc/openvpn/keys/ta.key 0
server ${split("/", vpn_client_cidr_block)[0]} ${cidrnetmask(vpn_client_cidr_block)}
push "redirect-gateway def1 bypass-dhcp"
push "dhcp-option DNS *******"
push "route ${split("/", vpc_cidr_block)[0]} ${cidrnetmask(vpc_cidr_block)}"
keepalive 10 120
cipher AES-256-GCM
auth SHA256
user nobody
group nogroup
persist-key
persist-tun
status /var/log/openvpn/status.log
log-append /var/log/openvpn/server.log
verb 3
max-clients 100
duplicate-cn
EOF

mkdir -p /var/log/openvpn

# Enhanced health check for NLB
cat > /opt/health.py << 'EOF'
#!/usr/bin/env python3
import subprocess
import socket
import os
from http.server import HTTPServer, BaseHTTPRequestHandler

class HealthCheckHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        try:
            # Check if OpenVPN process is running
            subprocess.check_call(['pgrep', '-f', 'openvpn.*server'], stdout=subprocess.DEVNULL)

            # Check if OpenVPN is listening on the VPN port
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            try:
                sock.bind(('0.0.0.0', 0))  # Just test if we can create a socket
                sock.close()
            except:
                raise Exception("Socket test failed")

            # Check if OpenVPN log shows it's running
            if os.path.exists('/var/log/openvpn/server.log'):
                with open('/var/log/openvpn/server.log', 'r') as f:
                    log_content = f.read()
                    if 'Initialization Sequence Completed' not in log_content:
                        raise Exception("OpenVPN not fully initialized")

            self.send_response(200)
            self.end_headers()
            self.wfile.write(b'OK - OpenVPN is healthy')
        except Exception as e:
            self.send_response(503)
            self.end_headers()
            self.wfile.write(f'FAIL - {str(e)}'.encode())

    def log_message(self, format, *args):
        pass  # Suppress access logs

HTTPServer(('0.0.0.0', 443), HealthCheckHandler).serve_forever()
EOF
chmod +x /opt/health.py

# Generate client - stay in the correct Easy-RSA directory
cd /etc/openvpn/easy-rsa
./easyrsa gen-req "client-$INSTANCE_ID" nopass
./easyrsa sign-req client "client-$INSTANCE_ID"

# Update PKI database in S3 after generating client cert
aws s3 cp pki/index.txt s3://$PKI_S3_BUCKET/$CA_DIR/index.txt --region "$AWS_DEFAULT_REGION"
aws s3 cp pki/serial s3://$PKI_S3_BUCKET/$CA_DIR/serial --region "$AWS_DEFAULT_REGION"
aws s3 cp pki/index.txt.attr s3://$PKI_S3_BUCKET/$CA_DIR/index.txt.attr --region "$AWS_DEFAULT_REGION"

# Get the NLB DNS name for client configuration
# If using load balancer, use NLB DNS name; otherwise use public IP
if [ "${enable_load_balancer}" = "true" ]; then
    # Get NLB DNS name from AWS API
    VPN_ENDPOINT=$(aws elbv2 describe-load-balancers --names "${project_name}-${environment}-lb" --query 'LoadBalancers[0].DNSName' --output text --region "$AWS_DEFAULT_REGION" 2>/dev/null)
    if [ -z "$VPN_ENDPOINT" ] || [ "$VPN_ENDPOINT" = "None" ]; then
        # Fallback to public IP if NLB lookup fails
        VPN_ENDPOINT=$(curl -s http://***************/latest/meta-data/public-ipv4)
    fi
else
    # Use public IP for single instance setup
    VPN_ENDPOINT=$(curl -s http://***************/latest/meta-data/public-ipv4)
fi

echo "Using VPN endpoint: $VPN_ENDPOINT"

cat > /etc/openvpn/client-configs/client-$INSTANCE_ID.ovpn << EOFCLIENT
client
dev tun
proto udp
remote $VPN_ENDPOINT $VPN_PORT
resolv-retry infinite
nobind
persist-key
persist-tun
remote-cert-tls server
cipher AES-256-GCM
auth SHA256
key-direction 1
verb 3
<ca>
$(cat pki/ca.crt)
</ca>
<cert>
$(cat pki/issued/client-$INSTANCE_ID.crt)
</cert>
<key>
$(cat pki/private/client-$INSTANCE_ID.key)
</key>
<tls-auth>
$(cat pki/ta.key)
</tls-auth>
EOFCLIENT

# Upload client config with both instance-specific and generic names
aws s3 cp /etc/openvpn/client-configs/client-$INSTANCE_ID.ovpn s3://$CLIENT_S3_BUCKET/clients/client-$INSTANCE_ID.ovpn --region "$AWS_DEFAULT_REGION"
aws s3 cp /etc/openvpn/client-configs/client-$INSTANCE_ID.ovpn s3://$CLIENT_S3_BUCKET/clients/client.ovpn --region "$AWS_DEFAULT_REGION"

# Create helper script for generating proper client configs
cat > /opt/generate-client-config.sh << 'EOFSCRIPT'
#!/bin/bash
# Script to generate a proper client configuration using the NLB DNS name
set -euo pipefail

AWS_REGION="${aws_region}"
PROJECT_NAME="${project_name}"
ENVIRONMENT="${environment}"
VPN_PORT="${vpn_port}"
CLIENT_S3_BUCKET="${client_config_s3_bucket}"

echo "=== Generating Client Configuration ==="

# Get the NLB DNS name
echo "Getting NLB DNS name..."
if [ "${enable_load_balancer}" = "true" ]; then
    NLB_DNS=$(aws elbv2 describe-load-balancers \
        --names "$${PROJECT_NAME}-$${ENVIRONMENT}-lb" \
        --query 'LoadBalancers[0].DNSName' \
        --output text \
        --region "$AWS_REGION" 2>/dev/null)

    if [ -z "$NLB_DNS" ] || [ "$NLB_DNS" = "None" ]; then
        echo "Warning: Could not get NLB DNS name, using public IP instead"
        NLB_DNS=$(curl -s http://***************/latest/meta-data/public-ipv4)
    fi
else
    NLB_DNS=$(curl -s http://***************/latest/meta-data/public-ipv4)
fi

echo "Using VPN endpoint: $NLB_DNS"
INSTANCE_ID=$(curl -s http://***************/latest/meta-data/instance-id)

# Ensure we're in the Easy-RSA directory
cd /etc/openvpn/easy-rsa

# Check if client cert already exists, if not create it
if [ ! -f "pki/issued/client-nlb.crt" ]; then
    echo "Generating new client certificate..."
    ./easyrsa gen-req "client-nlb" nopass
    ./easyrsa sign-req client "client-nlb"

    # Update PKI database in S3
    CA_DIR=$(dirname "${ca_s3_key}")
    PKI_S3_BUCKET="${key_s3_bucket}"
    aws s3 cp pki/index.txt s3://$PKI_S3_BUCKET/$CA_DIR/index.txt --region "$AWS_REGION"
    aws s3 cp pki/serial s3://$PKI_S3_BUCKET/$CA_DIR/serial --region "$AWS_REGION"
    aws s3 cp pki/index.txt.attr s3://$PKI_S3_BUCKET/$CA_DIR/index.txt.attr --region "$AWS_REGION"
fi

# Generate the client configuration
echo "Generating client configuration..."
cat > /etc/openvpn/client-configs/client-nlb.ovpn << EOFCLIENT
client
dev tun
proto udp
remote $NLB_DNS $VPN_PORT
resolv-retry infinite
nobind
persist-key
persist-tun
remote-cert-tls server
cipher AES-256-GCM
auth SHA256
key-direction 1
verb 3
<ca>
$(cat pki/ca.crt)
</ca>
<cert>
$(cat pki/issued/client-nlb.crt)
</cert>
<key>
$(cat pki/private/client-nlb.key)
</key>
<tls-auth>
$(cat pki/ta.key)
</tls-auth>
EOFCLIENT

# Upload to S3
echo "Uploading client configuration to S3..."
aws s3 cp /etc/openvpn/client-configs/client-nlb.ovpn s3://$CLIENT_S3_BUCKET/clients/client-nlb.ovpn --region "$AWS_REGION"
aws s3 cp /etc/openvpn/client-configs/client-nlb.ovpn s3://$CLIENT_S3_BUCKET/clients/client.ovpn --region "$AWS_REGION"

echo "✅ Client configuration generated successfully!"
echo "📁 Local file: /etc/openvpn/client-configs/client-nlb.ovpn"
echo "☁️  S3 location: s3://$CLIENT_S3_BUCKET/clients/client.ovpn"
echo "🌐 VPN endpoint: $NLB_DNS:$VPN_PORT"
echo ""
echo "To download the client config:"
echo "aws s3 cp s3://$CLIENT_S3_BUCKET/clients/client.ovpn ./client.ovpn --region $AWS_REGION"
EOFSCRIPT

chmod +x /opt/generate-client-config.sh

# Start services
systemctl enable openvpn@server
systemctl start openvpn@server
nohup python3 /opt/health.py >/dev/null 2>&1 &

# Verify
sleep 10
if systemctl is-active --quiet openvpn@server; then
    echo "✅ OpenVPN running"
else
    echo "❌ OpenVPN failed"
    systemctl status openvpn@server
fi

echo "=== Setup completed at $(date) ==="
