# Enhanced Whitelist VPN Gateway

A Terraform module that creates a scalable OpenVPN gateway solution for routing all egress traffic from on-premise servers through a single endpoint, simplifying customer firewall management.

## Purpose

**Problem**: On-premise servers need internet access but customers don't want to whitelist dozens of individual endpoints in their firewalls.

**Solution**: Route all egress traffic through a single VPN gateway endpoint that customers whitelist once.

## Key Features

- **Single Endpoint**: One DNS name for customers to whitelist
- **High Availability**: Auto Scaling Group with Network Load Balancer
- **Cost Effective**: Spot instances and single instance options
- **Secure**: Certificate-based OpenVPN authentication
- **Easy Management**: Automated client config generation via S3

## Quick Start

```hcl
module "vpn_gateway" {
  source = "../../modules/enhanced_whitelist_vpn_gateway"

  project_name = "my-vpn"
  environment  = "prod"
  aws_region   = "ap-southeast-2"

  # VPC Configuration
  vpc_cidr_block       = "*********/16"
  azs                  = ["ap-southeast-2a", "ap-southeast-2b"]
  public_subnet_cidrs  = ["10.10.1.0/24", "10.10.2.0/24"]
  private_subnet_cidrs = ["10.10.101.0/24", "10.10.102.0/24"]

  # VPN Configuration
  vpn_gateway_ami       = "ami-0fa0c4775c5c044bf"  # Ubuntu 22.04 LTS
  vpn_client_cidr_block = "10.200.0.0/16"

  tags = {
    Environment = "production"
  }
}
```

## Architecture

On-premise servers connect via OpenVPN to a Network Load Balancer, which routes traffic to Auto Scaling Group instances. All egress traffic flows through NAT Gateways to the internet.

## Configuration Options

### Development/Testing (Cost Optimized)
```hcl
# Single instance for dev/test
enable_auto_scaling        = false
enable_single_instance_vpn = true
single_nat_gateway        = true
asg_use_spot_instances     = true
```

### Production (High Availability)
```hcl
# Multi-AZ with auto scaling (default)
enable_auto_scaling = true
min_size           = 2
max_size           = 4
desired_capacity   = 2
```

## Required Parameters

| Name | Description |
|------|-------------|
| `aws_region` | AWS Region for deployment |
| `azs` | List of Availability Zones (minimum 2 for HA) |
| `public_subnet_cidrs` | CIDR blocks for public subnets |
| `private_subnet_cidrs` | CIDR blocks for private subnets |
| `vpn_gateway_ami` | Ubuntu 22.04 LTS AMI ID |
| `vpn_client_cidr_block` | VPN client IP pool (must not overlap with VPC) |

## Key Optional Parameters

| Name | Description | Default |
|------|-------------|---------|
| `project_name` | Project name for resource naming | `"eyecue-whitelist-vpn"` |
| `environment` | Environment (dev, prod, etc.) | `"dev"` |
| `vpc_cidr_block` | VPC CIDR block | `"*********/16"` |
| `enable_auto_scaling` | Enable Auto Scaling Group | `true` |
| `enable_single_instance_vpn` | Single instance mode | `false` |
| `single_nat_gateway` | Use single NAT Gateway for cost savings | `false` |
| `asg_use_spot_instances` | Use Spot instances | `false` |

## Key Outputs

| Name | Description |
|------|-------------|
| `load_balancer_dns_name` | **VPN connection endpoint** - Use this in client configs |
| `config_s3_bucket_name` | S3 bucket containing client .ovpn files |
| `vpc_id` | VPC ID for the VPN infrastructure |

## Client Setup

### 1. Get Client Configuration
The module automatically generates client configurations and uploads them to S3. Each EC2 instance gets its own `.ovpn` file named `client-<instance-id>.ovpn`.

### 2. Configure On-Premise Servers
Use the Ansible playbook `infra_openvpn_client` from the `eyecue-server-setup` repository to automatically configure OpenVPN on your servers.

### 3. Validation
```bash
# Test that traffic routes through VPN
curl ifconfig.me  # Should return VPN gateway IP

# Check routing table
ip route | grep tun0  # Should show VPN routes
```

## How It Works

1. **Terraform deploys**: VPC, Load Balancer, Auto Scaling Group, and OpenVPN instances
2. **OpenVPN server**: Automatically configured with certificates and pushes `redirect-gateway` to clients
3. **Client configs**: Generated automatically and uploaded to S3 bucket
4. **On-premise servers**: Connect using Ansible playbook and route all egress traffic through VPN
5. **Customer firewall**: Only needs to whitelist the single Load Balancer DNS name

## Maintenance

- **Scaling**: Adjust Auto Scaling Group size based on client load
- **Updates**: Keep OpenVPN and OS patched via standard update processes
- **Monitoring**: Use CloudWatch logs and metrics for health monitoring

## Troubleshooting

### Common Issues

**VPN Connection Fails**
- Check Load Balancer target health in AWS Console
- Verify OpenVPN service is running on instances
- Check security group allows UDP 1194

**Client Config Issues**
- Configs are auto-generated in S3 bucket (see outputs)
- Each instance creates its own client config
- Use the Load Balancer DNS name as the remote endpoint

**Cost Optimization**
- Enable Spot instances: `asg_use_spot_instances = true`
- Use single instance mode for dev/test: `enable_single_instance_vpn = true`
- Single NAT Gateway: `single_nat_gateway = true`
