# Enhanced Whitelist VPN Gateway

A Terraform module that creates a highly available and cost-effective VPN solution for secure remote access, designed to simplify firewall management for on-premise servers connecting to AWS services and internet resources.

## Business Value

**Problem Solved**: Eliminates the burden of managing 86+ firewall endpoints per store by consolidating to a single VPN endpoint, dramatically reducing manual whitelist configuration and operational overhead.

## Overview

This module deploys a production-grade VPN solution that provides a centralized and secure egress point for traffic from your on-premise network to AWS and the internet.

**Key Features**:
- **High Availability**: Auto Scaling Group across multiple AZs with Network Load Balancer
- **Cost Optimization**: Spot instances, single instance mode, and single NAT Gateway options
- **Security**: Certificate-based authentication, SSM access, and fine-grained IAM roles
- **Simplified Management**: S3-based configuration delivery and FQDN whitelisting support
- **Monitoring**: CloudWatch integration with alarms and notifications

## Quick Start

```hcl
module "vpn_gateway" {
  source = "../../modules/enhanced_whitelist_vpn_gateway"

  project_name = "my-vpn"
  environment  = "prod"
  aws_region   = "ap-southeast-2"

  # VPC Configuration
  vpc_cidr_block       = "*********/16"
  azs                  = ["ap-southeast-2a", "ap-southeast-2b"]
  public_subnet_cidrs  = ["*********/24", "*********/24"]
  private_subnet_cidrs = ["***********/24", "***********/24"]

  # VPN Configuration - Get latest Ubuntu 22.04 LTS AMI
  vpn_gateway_ami       = "ami-0fa0c4775c5c044bf"  # Ubuntu 22.04 LTS
  vpn_client_cidr_block = "**********/16"

  # Allowed domains for whitelisting
  allowed_fqdns = [
    "github.com",
    "*.amazonaws.com"
  ]

  tags = {
    Environment = "production"
  }
}
```

**Get Latest Ubuntu AMI**:
```bash
aws ec2 describe-images --owners 099720109477 \
  --filters "Name=name,Values=ubuntu/images/hvm-ssd/ubuntu-jammy-22.04-amd64-server-*" \
  --query 'Images | sort_by(@, &CreationDate) | [-1].ImageId' --output text
```

## Architecture

On-premise clients connect via OpenVPN tunnel to a Network Load Balancer, which distributes traffic to Auto Scaling Group instances. Traffic routes through NAT Gateways for secure internet access.

## Configuration Options

### Cost Optimization
```hcl
# Single instance for dev/test
enable_auto_scaling        = false
enable_single_instance_vpn = true
single_nat_gateway        = true

# Spot instances for production cost savings
asg_use_spot_instances = true
asg_spot_max_price     = "0.10"
```

### High Availability
```hcl
# Multi-AZ with auto scaling (default)
enable_auto_scaling = true
min_size           = 2
max_size           = 4
desired_capacity   = 2
```

### Security & Monitoring
```hcl
# Secure access and monitoring
enable_ssm_access            = true
admin_ssh_cidr_blocks       = []  # Use SSM instead of SSH
create_cloudwatch_alarms    = true
alarm_notification_email    = "<EMAIL>"
```

## Key Configuration Parameters

### Required Inputs
| Name | Description | Type |
|------|-------------|------|
| `aws_region` | AWS Region for deployment | `string` |
| `azs` | List of Availability Zones (minimum 2 for HA) | `list(string)` |
| `public_subnet_cidrs` | CIDR blocks for public subnets | `list(string)` |
| `private_subnet_cidrs` | CIDR blocks for private subnets | `list(string)` |
| `vpn_gateway_ami` | Ubuntu 22.04 LTS AMI ID | `string` |
| `vpn_client_cidr_block` | VPN client IP pool (must not overlap with VPC) | `string` |

### Important Optional Inputs
| Name | Description | Default |
|------|-------------|---------|
| `project_name` | Project name for resource naming | `"eyecue-whitelist-vpn"` |
| `environment` | Environment (dev, prod, etc.) | `"dev"` |
| `vpc_cidr_block` | VPC CIDR block | `"*********/16"` |
| `vpn_instance_type` | EC2 instance type | `"t3.medium"` |
| `allowed_fqdns` | Domains for whitelisting | `[]` |
| `enable_auto_scaling` | Enable Auto Scaling Group | `true` |
| `enable_single_instance_vpn` | Single instance mode | `false` |
| `single_nat_gateway` | Use single NAT Gateway for cost savings | `false` |
| `asg_use_spot_instances` | Use Spot instances | `false` |
| `enable_ssm_access` | Enable SSM Session Manager | `true` |
| `alarm_notification_email` | Email for CloudWatch alarms | `""` |

> **Note**: For complete parameter list, see `variables.tf`

## Key Outputs

| Name | Description |
|------|-------------|
| `load_balancer_dns_name` | **VPN connection endpoint** - Use this in client configs |
| `config_s3_bucket_name` | S3 bucket containing client .ovpn files |
| `vpc_id` | VPC ID for the VPN infrastructure |
| `vpn_gateway_public_ip` | Public IP for single instance mode |
| `cloudwatch_log_group_name` | CloudWatch log group for monitoring |

> **Note**: For complete output list, see `outputs.tf`

## Client Setup

### 1. Download Client Configuration
```bash
aws s3 cp s3://<config-bucket-name>/clients/<client-name>.ovpn ./
```

### 2. Connect to VPN
1. Import the `.ovpn` file into your OpenVPN client
2. Connect using the NLB DNS name from module outputs
3. Verify connection and traffic routing

### 3. Validation Checklist
- ✅ Configuration downloaded from S3
- ✅ VPN client connects successfully
- ✅ Traffic routes through VPN gateway
- ✅ Access to allowed domains works

## Security & Cost Features

### Security
- **Certificate-based authentication** with OpenVPN
- **SSM Session Manager** for secure instance access (no SSH keys required)
- **Private subnets** with NAT Gateway egress
- **Least-privilege IAM roles** for AWS service access
- **FQDN whitelisting support** via `allowed_fqdns` parameter

### Cost Optimization
- **Spot instances** for up to 90% cost savings
- **Single instance mode** for dev/test environments
- **Single NAT Gateway** option to reduce EIP costs
- **Pay-per-request DynamoDB** for variable workloads
- **Right-sized instances** with network optimization options

## FQDN Whitelisting

The `allowed_fqdns` parameter enables domain-based access control:

```hcl
allowed_fqdns = [
  "github.com",
  "*.amazonaws.com",
  "api.example.com"
]
```

**Implementation Options**:
- Configure transparent proxy (Squid) on VPN instances
- Use host-based firewall with FQDN support
- Integrate with AWS Network Firewall (separate setup)

> **Note**: This module provides the FQDN list; actual filtering requires additional configuration on VPN instances or AWS Network Firewall integration.

## Maintenance

- **Updates**: Keep OpenVPN and OS current with security patches
- **Monitoring**: Review CloudWatch logs and metrics regularly
- **Certificates**: Rotate OpenVPN certificates per security policy
- **Scaling**: Adjust Auto Scaling thresholds based on usage patterns
- **Costs**: Monitor AWS Cost Explorer for optimization opportunities

## Troubleshooting

### Common Issues

**VPN Connection Fails**
```bash
# Check NLB target health
aws elbv2 describe-target-health --target-group-arn <target-group-arn>

# Check instance status
aws ec2 describe-instances --filters "Name=tag:Name,Values=*vpn*"
```

**Client Config Not Found**
```bash
# List available configurations
aws s3 ls s3://<config-bucket>/clients/

# Check CloudWatch logs for errors
aws logs filter-log-events --log-group-name <log-group-name> --filter-pattern "ERROR"
```

**High Costs**
- Enable Spot instances: `asg_use_spot_instances = true`
- Use single instance mode for dev/test
- Enable single NAT Gateway: `single_nat_gateway = true`
