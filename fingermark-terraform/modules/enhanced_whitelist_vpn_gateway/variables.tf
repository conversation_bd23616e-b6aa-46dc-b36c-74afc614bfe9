variable "project_name" {
  description = "A short name for the project, used in resource naming."
  type        = string
  default     = "eyecue-whitelist-vpn"
}

variable "environment" {
  description = "Deployment environment (e.g., dev, prod)."
  type        = string
  default     = "dev"
}

variable "aws_region" {
  description = "AWS Region for deployment (e.g., ap-southeast-2)."
  type        = string
}

# --- Networking Configuration ---
variable "vpc_cidr_block" {
  description = "CIDR block for the new dedicated VPC. IMPORTANT: Must not overlap with on-prem or other peered VPCs."
  type        = string
  default     = "*********/16"
}

variable "azs" {
  description = "List of Availability Zones to deploy resources into (at least 2 recommended)."
  type        = list(string)
  # Example: ["ap-southeast-2a", "ap-southeast-2b", "ap-southeast-2c"]
}

variable "public_subnet_cidrs" {
  description = "List of CIDR blocks for public subnets (one per AZ). Must match the count of AZs."
  type        = list(string)
  # Example: ["*********/24", "*********/24", "*********/24"]
}

variable "private_subnet_cidrs" {
  description = "List of CIDR blocks for private subnets (one per AZ). Must match the count of AZs."
  type        = list(string)
  # Example: ["***********/24", "***********/24", "***********/24"]
}

variable "enable_nat_gateway" {
  description = "Set to true to create NAT Gateways for outbound internet access from private subnets."
  type        = bool
  default     = true
}

variable "single_nat_gateway" {
  description = "If true, only one NAT Gateway will be created (in the first public subnet/AZ) to conserve EIPs. If false, one NAT Gateway per AZ will be created."
  type        = bool
  default     = false
}

# --- VPN Configuration ---
variable "vpn_gateway_ami" {
  description = "AMI ID for the OpenVPN EC2 instance. Use an AMI with OpenVPN Access Server pre-installed. Ensure you are subscribed to this AMI in AWS Marketplace for the target region."
  type        = string
  # Example for ap-southeast-2 OpenVPN Access Server BYOL: "ami-0f87602759d41901c"
}

variable "vpn_instance_type" {
  description = "EC2 instance type for the VPN gateway. For optimal network performance and cost, consider network-optimized instances (e.g., c5n, m5n series). Default is t3.medium."
  type        = string
  default     = "t3.medium" # Choose based on expected load/connections
}

variable "admin_ssh_cidr_blocks" {
  description = "List of CIDR blocks allowed for SSH access to the VPN instance. Empty list [] recommended for production (manage manually or via Bastion/Systems Manager)."
  type        = list(string)
  default     = [] # Secure default. Use ["0.0.0.0/0"] ONLY for temporary initial testing.
}

variable "vpn_client_cidr_block" {
  description = "Virtual IP address pool for VPN clients (on-prem servers). IMPORTANT: Must not overlap with VPC, on-prem, or other routed networks. Must match OpenVPN server config."
  type        = string
  # Example: "**********/16"
}

variable "allowed_fqdns" {
  description = "List of Fully Qualified Domain Names (FQDNs) or wildcard domains (e.g., .example.com) intended for whitelisting outbound traffic from VPN clients. Note: Implementation of FQDN-based filtering requires additional configuration on the VPN instances (e.g., host-based firewall with FQDN support, transparent proxy) or integration with AWS Network Firewall, which is not automatically configured by this module's user_data script. This variable provides the list for such a setup."
  type        = list(string)
  default     = []
}

# --- Auto Scaling Configuration ---
variable "enable_auto_scaling" {
  description = "Whether to enable Auto Scaling for the VPN instances. If true, VPN instances will be managed by an Auto Scaling Group. If false, behavior depends on enable_single_instance_vpn."
  type        = bool
  default     = true
}

variable "min_size" {
  description = "Minimum number of VPN instances in the Auto Scaling Group."
  type        = number
  default     = 2
}

variable "max_size" {
  description = "Maximum number of VPN instances in the Auto Scaling Group."
  type        = number
  default     = 5
}

variable "desired_capacity" {
  description = "Desired number of VPN instances in the Auto Scaling Group."
  type        = number
  default     = 2
}

variable "scale_up_threshold" {
  description = "CPU utilization threshold to scale up VPN instances."
  type        = number
  default     = 70
}

variable "scale_down_threshold" {
  description = "CPU utilization threshold to scale down VPN instances."
  type        = number
  default     = 30
}

variable "asg_use_spot_instances" {
  description = "Set to true to use Spot Instances for the Auto Scaling Group. This can reduce costs but instances may be interrupted."
  type        = bool
  default     = false
}

variable "asg_spot_max_price" {
  description = "Optional: Maximum price per hour you are willing to pay for Spot Instances. If not set, defaults to the On-Demand price."
  type        = string
  default     = null
}

variable "enable_single_instance_vpn" {
  description = "Set to true to deploy a single VPN instance. Only effective if enable_auto_scaling is false. If enable_auto_scaling is true, this variable is ignored."
  type        = bool
  default     = false
}



# --- Load Balancer Configuration ---
variable "enable_load_balancer" {
  description = "Whether to create a Network Load Balancer for distributing VPN traffic."
  type        = bool
  default     = true
}

variable "nlb_internal" {
  description = "Whether the NLB should be internal only (not internet-facing)."
  type        = bool
  default     = false
}

variable "vpn_port" {
  description = "UDP port for OpenVPN traffic."
  type        = number
  default     = 1194
}

# --- Monitoring Configuration ---
variable "enable_enhanced_monitoring" {
  description = "Whether to enable detailed CloudWatch monitoring for VPN instances."
  type        = bool
  default     = true
}

variable "create_cloudwatch_alarms" {
  description = "Whether to create CloudWatch alarms for monitoring VPN instances."
  type        = bool
  default     = true
}

variable "alarm_notification_email" {
  description = "Email address for CloudWatch alarm notifications."
  type        = string
  default     = ""
}

# --- Security Configuration ---
variable "enable_ddos_protection" {
  description = "Whether to enable AWS Shield Standard for DDoS protection."
  type        = bool
  default     = false
}

variable "enable_ssm_access" {
  description = "Whether to enable SSM Session Manager access to VPN instances."
  type        = bool
  default     = true
}

variable "tags" {
  description = "A map of tags to assign to the resources."
  type        = map(string)
  default     = {}
}

# --- OpenVPN Community Edition Configuration ---
variable "key_s3_bucket" {
  description = "S3 bucket for storing OpenVPN key material"
  type        = string
}

variable "ca_s3_key" {
  description = "S3 key path for the CA certificate"
  type        = string
}

variable "dh_s3_key" {
  description = "S3 key path for the Diffie-Hellman parameters"
  type        = string
}

variable "client_config_s3_bucket" {
  description = "S3 bucket for storing generated client configurations"
  type        = string
}

variable "ca_key_s3_key" {
  description = "S3 key path for the CA private key (needed for client certificate generation)"
  type        = string
  default     = "pki/ca.key"
}

# --- S3 Bucket Creation Control ---
variable "create_key_bucket" {
  description = "Whether to create the S3 bucket for OpenVPN keys"
  type        = bool
  default     = false
}

variable "create_client_config_bucket" {
  description = "Whether to create the S3 bucket for client configurations"
  type        = bool
  default     = false
}

# --- Cross-Account Access Configuration ---
variable "on_premise_account_ids" {
  description = "List of AWS account IDs where on-premise servers are registered (for cross-account S3 access to VPN configs)"
  type        = list(string)
  default     = ["************"]
}
