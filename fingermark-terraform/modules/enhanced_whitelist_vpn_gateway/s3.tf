# S3 bucket for OpenVPN key materials (conditional)
resource "aws_s3_bucket" "vpn_keys" {
  count = var.create_key_bucket ? 1 : 0

  bucket = var.key_s3_bucket

  tags = merge(var.tags, {
    Name = "${local.base_name}-vpn-keys"
  })
}

# Block public access to key bucket
resource "aws_s3_bucket_public_access_block" "vpn_keys" {
  count = var.create_key_bucket ? 1 : 0

  bucket                  = aws_s3_bucket.vpn_keys[0].id
  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

# Enable encryption for key bucket
resource "aws_s3_bucket_server_side_encryption_configuration" "vpn_keys" {
  count = var.create_key_bucket ? 1 : 0

  bucket = aws_s3_bucket.vpn_keys[0].id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}

# S3 bucket for client configurations (conditional)
resource "aws_s3_bucket" "client_configs" {
  count = var.create_client_config_bucket ? 1 : 0

  bucket = var.client_config_s3_bucket

  tags = merge(var.tags, {
    Name = "${local.base_name}-client-configs"
  })
}

# Block public access to client config bucket
resource "aws_s3_bucket_public_access_block" "client_configs" {
  count = var.create_client_config_bucket ? 1 : 0

  bucket                  = aws_s3_bucket.client_configs[0].id
  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

# Enable encryption for client config bucket
resource "aws_s3_bucket_server_side_encryption_configuration" "client_configs" {
  count = var.create_client_config_bucket ? 1 : 0

  bucket = aws_s3_bucket.client_configs[0].id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}
