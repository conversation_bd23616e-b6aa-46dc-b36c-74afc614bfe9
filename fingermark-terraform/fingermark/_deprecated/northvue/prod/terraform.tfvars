# ===============================================
# CloudWatch Alarms
# ===============================================
dynamodb_cw_alarms_defaults_tables_us_east_1 = {
  "eyecue-inventory-table"                   = { table_name = "eyecue-inventory-table" },
  "eyecue-provisioning-log"                  = { table_name = "eyecue-provisioning-log" },
  "eyecue-provisioning-table"                = { table_name = "eyecue-provisioning-table" },
  "eyecue-things-shadow"                     = { table_name = "eyecue-things-shadow" },
  "northvue-capture-api-prod"                = { table_name = "northvue-capture-api-prod" },
  "northvue-catch-api-prod"                  = { table_name = "northvue-catch-api-prod" },
  "northvue-catch-api-prod-event-metadata"   = { table_name = "northvue-catch-api-prod-event-metadata" },
  "northvue-catch-api-prod-permission-cache" = { table_name = "northvue-catch-api-prod-permission-cache" },
  "northvue-catch-api-prod-tags"             = { table_name = "northvue-catch-api-prod-tags" },
}
