output "grafana_access_key_id" {
  description = "Access Key ID for <PERSON>ana to publish to SNS"
  value       = module.platform_camera_connectivity_alerts.grafana_access_key_id
}

output "grafana_secret_access_key" {
  description = "Secret Access Key for <PERSON><PERSON> to publish to SNS"
  value       = module.platform_camera_connectivity_alerts.grafana_secret_access_key
  sensitive   = true
}
# Enhanced Whitelist VPN Gateway outputs
output "vpn_vpc_id" {
  description = "VPC ID where the VPN gateway is deployed"
  value       = module.enhanced_whitelist_vpn_gateway.vpc_id
}

output "vpn_security_group_id" {
  description = "Security group ID attached to the VPN gateway instance"
  value       = module.enhanced_whitelist_vpn_gateway.security_group_id
}

output "vpn_instance_id" {
  description = "EC2 instance ID of the VPN gateway"
  value       = module.enhanced_whitelist_vpn_gateway.vpn_instance_id
}

# output "firewall_name" {
#   description = "Name of the Network Firewall"
#   value       = module.enhanced_whitelist_vpn_gateway.firewall_name
# }

# output "firewall_policy_arn" {
#   description = "ARN of the Network Firewall policy"
#   value       = module.enhanced_whitelist_vpn_gateway.firewall_policy_arn
# }

# output "rule_group_arn" {
#   description = "ARN of the Network Firewall rule group"
#   value       = module.enhanced_whitelist_vpn_gateway.rule_group_arn
# }

output "load_balancer_arn" {
  description = "ARN of the Network Load Balancer (if enabled)"
  value       = module.enhanced_whitelist_vpn_gateway.load_balancer_arn
}

output "load_balancer_dns_name" {
  description = "DNS name of the Network Load Balancer (if enabled)"
  value       = module.enhanced_whitelist_vpn_gateway.load_balancer_dns_name
}

output "target_group_arn" {
  description = "ARN of the target group (if enabled)"
  value       = module.enhanced_whitelist_vpn_gateway.target_group_arn
}

output "autoscaling_group_name" {
  description = "Name of the Auto Scaling Group (if enabled)"
  value       = module.enhanced_whitelist_vpn_gateway.autoscaling_group_name
}

output "vpn_instance_role_name" {
  description = "Name of the IAM role attached to the VPN instance"
  value       = module.enhanced_whitelist_vpn_gateway.vpn_instance_role_name
}

# Additional useful outputs
output "vpn_client_cidr_block" {
  description = "CIDR block used for VPN client IP addresses"
  value       = module.enhanced_whitelist_vpn_gateway.vpn_client_cidr_block
}

output "vpn_public_ip" {
  description = "Public IP address of the VPN gateway instance"
  value       = module.enhanced_whitelist_vpn_gateway.vpn_public_ip
}

output "vpn_private_ip" {
  description = "Private IP address of the VPN gateway instance"
  value       = module.enhanced_whitelist_vpn_gateway.vpn_private_ip
}
