module "iam_password_policy" {
  source = "../../../modules/iam_password_policy"
}

module "assume_role" {
  source = "../../../modules/fingermark_users_assume_role"
  roles  = ["AdminAccess", "DevAccess", "PowerAccess", "DeployerAccess"]
}

module "vanta" {
  source = "../../../modules/vanta"
}

module "eyecue_network" {
  source                 = "../../../modules/network"
  vpc_cidr_block         = var.vpc_cidr_block
  vpc_name               = "${var.customer}_${var.product}_${var.env}_${data.aws_region.current.name}_vpc"
  azs                    = var.vpc_azs
  vpc_tags               = merge(var.default_tags, var.vpc_tags)
  public_subnets         = var.public_subnets
  private_subnets        = var.private_subnets
  havelocknorthaccess_sg = "enabled"
  tags                   = merge(var.default_tags, var.tags)
}

# --- VPN Gateway module ---
module "enhanced_whitelist_vpn_gateway" {
  source = "../../../modules/enhanced_whitelist_vpn_gateway"

  # Basic project information
  project_name = "eyecue-whitelist-vpn"
  environment  = "francium"
  aws_region   = data.aws_region.current.name

  # Networking configuration - hardcoded values instead of vars
  vpc_cidr_block       = "*********/16"
  azs                  = ["ap-southeast-2a", "ap-southeast-2b", "ap-southeast-2c"]
  public_subnet_cidrs  = ["*********/24", "*********/24", "*********/24"]
  private_subnet_cidrs = ["***********/24", "***********/24", "***********/24"]
  enable_nat_gateway   = true
  single_nat_gateway   = true # Use a single NAT gateway instead of one per AZ to save EIP

  # VPN configuration
  vpn_gateway_ami       = "ami-09a50a142626e358e" # Ubuntu 22.04 LTS for ap-southeast-2
  vpn_instance_type     = "t2.small"
  admin_ssh_cidr_blocks = [] # Secure production setting - using SSM for access
  vpn_client_cidr_block = "**********/16"

  # OpenVPN Community Edition Configuration
  key_s3_bucket               = "eyecue-francium-vpn-keys"
  ca_s3_key                   = "pki/ca.crt"
  dh_s3_key                   = "pki/dh2048.pem"
  client_config_s3_bucket     = "eyecue-francium-vpn-client-configs"
  create_key_bucket           = true
  create_client_config_bucket = true

  # Auto Scaling configuration - Making the solution highly available
  enable_auto_scaling  = true
  min_size             = 2
  max_size             = 5
  desired_capacity     = 2
  scale_up_threshold   = 70
  scale_down_threshold = 30

  # Load Balancing configuration
  enable_load_balancer = true
  nlb_internal         = false
  vpn_port             = 1194

  # Monitoring & security configuration
  enable_enhanced_monitoring = true
  create_cloudwatch_alarms   = true
  alarm_notification_email   = "<EMAIL>"
  enable_ddos_protection     = false # Can be enabled if AWS Shield is available
  enable_ssm_access          = true

  # Domain whitelist - initially configured to allow all traffic
  # The infrastructure is in place for future FQDN filtering when needed
  allowed_fqdns = [
    # Empty list as we're allowing all traffic for now
    # Can be populated with specific domains when more granular control is needed
  ]

  tags = {
    Environment = "francium"
    Product     = "eyecue"
    Terraform   = "true"
    Project     = "EyecueWhitelistVPN"
    Squad       = "Platform"
  }
}

module "ansible_agent" {
  source         = "git::*****************:fingermarkltd/ansible-agent.git//terraform?ref=master"
  aws_account_id = data.aws_caller_identity.current.account_id
}

# module "kommisjon" {
#   source         = "git::*****************:fingermarkltd/kommisjon-terraform.git/?ref=master"
#   aws_account_id = data.aws_caller_identity.current.account_id
# }

# module "slack_notification" {
#   source                       = "git::*****************:fingermarkltd/slack-notification.git//terraform?ref=master"
#   lambda_env_var_slack_webhook = "*****************************************************************************"
# }

module "platform_camera_connectivity_alerts" {
  source        = "../../../modules/platform-camera-connectivity-alerts"
  environment   = "dev"
  sns_topic_arn = "arn:aws:sns:ap-southeast-2:************:camera-connectivity-alerts-AlertsTopic-iv2CIgc5sDrf"
}

module "aws_config_recorder" {
  source = "../../../modules/aws_config_recorder"

  recorder_name      = "config-recorder"
  enable_recording   = true
  recording_strategy = "INCLUSION_BY_RESOURCE_TYPES"
  include_resource_types = [
    "AWS::IAM::Group",
    "AWS::IAM::Policy",
    "AWS::IAM::Role",
    "AWS::IAM::User",
  ]
  recording_frequency = "CONTINUOUS"

  # Re-use Control Tower deployed S3 Bucket and SNS topic
  s3_bucket_name = "aws-controltower-logs-************-ap-southeast-2"                               # Log Archive account
  s3_key_prefix  = "o-aydhjv9alg"                                                                    # Organization ID
  sns_topic_arn  = "arn:aws:sns:ap-southeast-2:************:aws-controltower-AllConfigNotifications" # Audit account

  tags         = var.tags
  default_tags = var.default_tags
}

# ===============================================
# CloudWatch Alarms
# ===============================================
locals {
  dynamodb_cw_alarms = {
    defaults = {
      ap_southeast_2 = {
        tables_config = merge(
          # Additional DDB tables
          var.dynamodb_cw_alarms_defaults_tables_ap_southeast_2,
        )
        gsis_config = merge(
          # Additional DDB GSIs
          var.dynamodb_cw_alarms_defaults_gsis_ap_southeast_2
        )
      }
      us_east_1 = {
        tables_config = merge(
          # Additional DDB tables
          var.dynamodb_cw_alarms_defaults_tables_us_east_1,
        )
        gsis_config = merge(
          # Additional DDB GSIs
          var.dynamodb_cw_alarms_defaults_gsis_us_east_1
        )
      }
    }
  }
  elb_cw_alarms = {
    defaults = {
      ap_southeast_2 = {
        nlb_config = {
          "eyecue-whitelist-vpn-francium-lb" = { lb_name = "eyecue-whitelist-vpn-francium-lb", tg_names = ["eyecue-whitelist-vpn-francium-tg"] }
        }
      }
    }
  }
}

module "dynamodb_cw_alarms_defaults_ap_southeast_2" {
  source                                 = "../../../modules/dynamodb_cw_alarms"
  sns_topic_arns                         = [var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]]
  cw_alarm_config_ddb_table_consumed_rcu = local.dynamodb_cw_alarms.defaults.ap_southeast_2.tables_config
  cw_alarm_config_ddb_gsi_consumed_rcu   = local.dynamodb_cw_alarms.defaults.ap_southeast_2.gsis_config
  cw_alarm_config_ddb_table_consumed_wcu = local.dynamodb_cw_alarms.defaults.ap_southeast_2.tables_config
  cw_alarm_config_ddb_gsi_consumed_wcu   = local.dynamodb_cw_alarms.defaults.ap_southeast_2.gsis_config

  tags         = var.tags
  default_tags = var.default_tags
}

module "dynamodb_cw_alarms_defaults_us_east_1" {
  providers = {
    aws = aws.us-east-1
  }
  source                                 = "../../../modules/dynamodb_cw_alarms"
  sns_topic_arns                         = [var.cw_alarms_sns_topic_arns_region_lookup["us-east-1"]]
  cw_alarm_config_ddb_table_consumed_rcu = local.dynamodb_cw_alarms.defaults.us_east_1.tables_config
  cw_alarm_config_ddb_gsi_consumed_rcu   = local.dynamodb_cw_alarms.defaults.us_east_1.gsis_config
  cw_alarm_config_ddb_table_consumed_wcu = local.dynamodb_cw_alarms.defaults.us_east_1.tables_config
  cw_alarm_config_ddb_gsi_consumed_wcu   = local.dynamodb_cw_alarms.defaults.us_east_1.gsis_config

  tags         = var.tags
  default_tags = var.default_tags
}

module "ec2_instance_cw_alarms_ap_southeast_2" {
  # providers      = { aws = aws.ap-southeast-2 } # DEFAULT AWS PROVIDER: ap-southeast-2
  source         = "../../../modules/ec2_instance_cw_alarms"
  sns_topic_arns = [var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]]
  cw_alarm_config_ec2_by_tags_cpu_util_high = {
    "foreman-test"    = { instance_tags = { Name = "foreman-test" } }
    "network-openvpn" = { instance_tags = { Name = "network-openvpn" } }
    "openvpnc-conexa" = { instance_tags = { Name = "openvpnc-conexa" } }
  }
  cw_alarm_config_ec2_by_tags_cpu_util_low = {}
}

module "elb_cw_alarms_ap_southeast_2" {
  source                                   = "../../../modules/elb_cw_alarms"
  sns_topic_arns                           = [var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]]
  cw_alarm_config_nlb_healthy_host_count   = local.elb_cw_alarms.defaults.ap_southeast_2.nlb_config
  cw_alarm_config_nlb_unhealthy_host_count = local.elb_cw_alarms.defaults.ap_southeast_2.nlb_config
  tags                                     = var.tags
  default_tags                             = var.default_tags
}

# ==============================================
# VPC Flow Logs
# ==============================================
module "vpc_flow_logs" {
  source          = "../../../modules/vpc_flow_logs"
  log_destination = module.vpc_flow_logs_s3.s3_bucket_arn
}

module "vpc_flow_logs_s3" {
  source = "../../../modules/vpc_flow_logs_s3"

  bucket_name = "fingermark-vpc-logs-dev"
  tags        = merge(var.default_tags, var.tags)
}

module "pentest_iam_user" {
  source   = "../../../modules/pentest_iam_user"
  username = "eyecue-pentest-user"
  tags = {
    Purpose     = "Annual Penetration Testing"
    Temporary   = "true"
    Environment = "Pentest"
    Squad       = "Platform team"
  }
}

# Lambda global error rate monitoring for SOC2 compliance
module "lambda_error_monitoring_ap_southeast_2" {
  source = "../../../modules/lambda_error_monitoring"

  alarm_name              = "SOC2-GlobalLambdaErrorRate"
  alarm_description       = "SOC2 compliance - Monitors the global Lambda error rate across all functions"
  error_threshold_percent = 10 # Alarm when error rate exceeds 10%
  evaluation_periods      = 2  # Require breach for 2 consecutive periods
  period_seconds          = 3600

  sns_topic_arns      = [var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]]
  enable_notification = true

  tags = merge(var.tags, {
    Compliance  = "SOC2"
    Purpose     = "ErrorMonitoring"
    Environment = "Production"
    Squad       = "Platform team"
  })
  default_tags = var.default_tags
}
