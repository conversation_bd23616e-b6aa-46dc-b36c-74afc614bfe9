
# ===============================================
# CloudWatch Alarms
# ===============================================
dynamodb_cw_alarms_defaults_tables_ap_southeast_2 = {
  "KnowledgeBase"                           = { table_name = "KnowledgeBase" },
  "eyecue-inventory-table"                  = { table_name = "eyecue-inventory-table" },
  "eyecue-whitelist-vpn-francium-vpn-users" = { table_name = "eyecue-whitelist-vpn-francium-vpn-users" },
  "camera-connectivity-alerts-dev"          = { table_name = "camera-connectivity-alerts-dev" },
}
dynamodb_cw_alarms_defaults_gsis_ap_southeast_2 = {}
dynamodb_cw_alarms_defaults_tables_us_east_1 = {
  "eyecue-inventory-table" = { table_name = "eyecue-inventory-table" }
}
dynamodb_cw_alarms_defaults_gsis_us_east_1 = {}
