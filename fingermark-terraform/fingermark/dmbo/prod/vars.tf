variable "AWS_REGION" {
  description = "AWS Region"
  type        = string
  default     = "ap-southeast-2"
}

variable "magicinfo_server_name" {
  description = "Magicinfo server's name"
  type        = string
  default     = "fm-dmbo-magicinfo"
}

variable "magicinfo_rdp_access_cidr_blocks" {
  description = "Magicinfo server's RDP access cidr blocks"
  type        = list(string)
  default = [
    "**************/32", // Nilay
    "**************/32"  // Havelock Office
  ]
}

variable "CLIENT_ACRONYM" {
  default = "samsung-dmbo-prod"
}

variable "KEYBASE" {
  type    = string
  default = "keybase:fingermark"
}

variable "CLIENT_NAME" {
  type    = string
  default = "samsung-dmbo-prod"
}

variable "COUNTRY" {
  type    = string
  default = "au"
}

variable "magicinfo_server_instance_type" {
  description = "Magicinfo server's instance type"
  type        = string
  default     = "t3.medium"
}

variable "magicinfo_nic_ip" {
  description = "Magicinfo server's Network Interface IP address"
  type        = string
  default     = "***********"
}

variable "magicinfo_server_securitygroup_name" {
  description = "Magicinfo server's Security Group"
  type        = string
  default     = "fm-dmbo-magicinfo"
}


variable "vpc_cidr_block" {
  description = "AWS VPC cidr block"
  type        = string
  default     = "**********/16"
}

variable "public_subnet_tags" {
  type        = any
  description = "a group of tags to tag vpc"
  default     = {}
}

variable "vpc_tags" {
  description = "Infrastructure - Network VPC Tags"
  type        = map(any)
  default     = {}
}

variable "public_subnets" {
  description = "Infrastructure - Network Public Subnets List"
  type        = list(any)
  default     = ["**********/20", "***********/20", "***********/20"]
}

variable "vpc_azs" {
  description = "Infrastructure - Network Availability Zones"
  type        = list(any)
  default = [
    "ap-southeast-2a",
    "ap-southeast-2b",
    "ap-southeast-2c"
  ]
}

variable "private_subnets" {
  description = "Infrastructure - Network Private Subnets List"
  type        = list(any)
  default     = ["***********/20", "***********/20", "***********/20"]
}

variable "tags" {
  description = "Infrastructure Tags"
  type        = map(any)
  default = {
    Name = "fm-dmbo-magicinfo"
  }
}

variable "default_vpc_tags" {
  description = "Infrastructure VPC Default Tags"
  type        = map(any)
  default = {
    Stack = "network"
  }
}

variable "default_tags" {
  description = "Infrastructure Default Tags"
  type        = map(any)
  default = {
    Terraform   = "true"
    Product     = "samsung-dmbo"
    Environment = "prod"
  }
}

variable "env" {
  default     = "prod"
  description = "Fingermark Environment"
  type        = string
}

variable "product" {
  default     = "samsung_dmbo"
  description = "Fingermark Product"
  type        = string
}

variable "magicinfo_server_alb_name" {
  description = "Magicinfo server's ALB name"
  type        = string
  default     = "fm-dmbo-magicinfo"
}

variable "magicinfo_server_s3_bucket_name" {
  description = "Magicinfo server's s3 bucket name for logs"
  type        = string
  default     = "fm-dmbo-magicinfo-alb-logs"
}

variable "cloudflare_record_name" {
  description = "Cloudflare record name"
  type        = string
  default     = "fm-dmbo-magicinfo"
}

variable "acm_domain_name" {
  description = "Cloudflare record name"
  type        = string
  default     = "fm-dmbo-magicinfo.fingermark.tech"
}

# ===============================================
# CloudWatch Alarms
# ===============================================
variable "cw_alarms_sns_topic_arns_region_lookup" {
  description = <<-DOC
    Map of region names to SNS topic ARNs for CloudWatch alarm notifications. The SNS topic ARNs
    should have already been created in a centralised AWS account (infra #************) using the
    module `modules/cw_alarm_notifications_sns_topic`.
  DOC
  type        = map(string)
  default = {
    "ap-southeast-2" = "arn:aws:sns:ap-southeast-2:************:cloudwatch-alarms-org"
    "ca-central-1"   = "arn:aws:sns:ca-central-1:************:cloudwatch-alarms-org"
    "us-east-1"      = "arn:aws:sns:us-east-1:************:cloudwatch-alarms-org"
    "us-west-1"      = "arn:aws:sns:us-west-1:************:cloudwatch-alarms-org"
    "us-west-2"      = "arn:aws:sns:us-west-2:************:cloudwatch-alarms-org"
  }
}
