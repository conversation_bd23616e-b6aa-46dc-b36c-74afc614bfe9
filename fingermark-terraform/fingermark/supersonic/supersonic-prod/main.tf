module "assume_role" {
  source = "../../../modules/fingermark_users_assume_role"
  roles  = ["AdminAccess", "PowerAccess", "DevAccess", "DeployerAccess", "StorageAccess"]
}

module "vanta" {
  source = "../../../modules/vanta"
}

module "network" {
  source                 = "../../../modules/network"
  vpc_cidr_block         = var.vpc_cidr_block
  vpc_name               = "${var.customer}_${var.product}_${var.env}_${var.AWS_REGION}_vpc"
  azs                    = var.vpc_azs
  vpc_tags               = merge(var.default_tags, var.vpc_tags)
  public_subnets         = var.public_subnets
  private_subnets        = var.private_subnets
  havelocknorthaccess_sg = "enabled"
  tags                   = merge(var.default_tags, var.tags)
}