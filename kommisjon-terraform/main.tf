module "global" {
  # IAM + S3 + Lambda
  source = "./modules/global"
}

module "ap_southeast_2" {
  source                        = "./modules/regional"
  kommisjon_nodes_s3_name       = module.global.kommisjon_s3_bucket_id
  kommisjon_run_command_role_id = module.global.kommisjon_run_command_role_id
  providers = {
    aws = aws.ap-southeast-2
  }
}

module "us_east_1" {
  source                        = "./modules/regional"
  kommisjon_nodes_s3_name       = module.global.kommisjon_s3_bucket_id
  kommisjon_run_command_role_id = module.global.kommisjon_run_command_role_id
  providers = {
    aws = aws.us-east-1
  }
}

module "us_west_2" {
  source                        = "./modules/regional"
  kommisjon_nodes_s3_name       = module.global.kommisjon_s3_bucket_id
  kommisjon_run_command_role_id = module.global.kommisjon_run_command_role_id
  providers = {
    aws = aws.us-west-2
  }
}

module "us_west_1" {
  source                        = "./modules/regional"
  kommisjon_nodes_s3_name       = module.global.kommisjon_s3_bucket_id
  kommisjon_run_command_role_id = module.global.kommisjon_run_command_role_id
  providers = {
    aws = aws.us-west-1
  }
}

module "me_south_1" {
  source                        = "./modules/regional"
  kommisjon_nodes_s3_name       = module.global.kommisjon_s3_bucket_id
  kommisjon_run_command_role_id = module.global.kommisjon_run_command_role_id
  providers = {
    aws = aws.me-south-1
  }
}