data "aws_caller_identity" "current" {}
data "aws_region" "current" {}

data "archive_file" "kommisjon_delete_activation" {
  type        = "zip"
  source_dir  = "${path.module}/${var.lambdas_code_path}/kommisjon_delete_activation"
  output_path = "${path.module}/${var.lambdas_packages_path}/kommisjon_delete_activation.zip"
}

data "archive_file" "kommisjon_register_node" {
  type        = "zip"
  source_dir  = "${path.module}/${var.lambdas_code_path}/kommisjon_register_node"
  output_path = "${path.module}/${var.lambdas_packages_path}/kommisjon_register_node.zip"
}

data "archive_file" "kommisjon_tag_resource" {
  type        = "zip"
  source_dir  = "${path.module}/${var.lambdas_code_path}/kommisjon_tag_resource"
  output_path = "${path.module}/${var.lambdas_packages_path}/kommisjon_tag_resource.zip"
}

data "archive_file" "kommisjon_update_node_status" {
  type        = "zip"
  source_dir  = "${path.module}/${var.lambdas_code_path}/kommisjon_update_node_status"
  output_path = "${path.module}/${var.lambdas_packages_path}/kommisjon_update_node_status.zip"
}
