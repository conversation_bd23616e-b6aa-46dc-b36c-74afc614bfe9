#################################################

resource "aws_iam_role" "kommisjon_delete_activation" {
  name               = "KommisjonDeleteActivation-${data.aws_region.current.name}"
  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": "sts:AssumeRole",
      "Principal": {
        "Service": "lambda.amazonaws.com"
      },
      "Effect": "Allow",
      "Sid": ""
    }
  ]
}
EOF
}

resource "aws_iam_role_policy" "kommisjon_delete_activation" {
  name   = "KommisjonDeleteActivation-${data.aws_region.current.name}"
  role   = aws_iam_role.kommisjon_delete_activation.id
  policy = templatefile("${path.module}/${var.lambdas_code_path}/kommisjon_delete_activation/iam_policy.tftpl", { account_id = data.aws_caller_identity.current.account_id })
}

resource "aws_lambda_function" "kommisjon_delete_activation" {
  filename         = data.archive_file.kommisjon_delete_activation.output_path
  function_name    = "kommisjon-delete-activation"
  role             = aws_iam_role.kommisjon_delete_activation.arn
  handler          = "main.lambda_handler"
  source_code_hash = data.archive_file.kommisjon_delete_activation.output_base64sha256
  runtime          = "python3.9"
  architectures    = ["arm64"]
  timeout          = var.lambda_timeout
  tags             = var.default_tags
}

###############################

resource "aws_iam_role" "kommisjon_register_node" {
  name               = "KommisjonRegisterNode-${data.aws_region.current.name}"
  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": "sts:AssumeRole",
      "Principal": {
        "Service": "lambda.amazonaws.com"
      },
      "Effect": "Allow",
      "Sid": ""
    }
  ]
}
EOF
}

resource "aws_iam_role_policy" "kommisjon_register_node" {
  name   = "KommisjonRegisterNode-${data.aws_region.current.name}"
  role   = aws_iam_role.kommisjon_register_node.id
  policy = templatefile("${path.module}/${var.lambdas_code_path}/kommisjon_register_node/iam_policy.tftpl", { account_id = data.aws_caller_identity.current.account_id })
}

resource "aws_lambda_function" "kommisjon_register_node" {
  filename         = data.archive_file.kommisjon_register_node.output_path
  function_name    = "kommisjon-register-node"
  role             = aws_iam_role.kommisjon_register_node.arn
  handler          = "main.lambda_handler"
  source_code_hash = data.archive_file.kommisjon_register_node.output_base64sha256
  runtime          = "python3.9"
  architectures    = ["arm64"]
  timeout          = var.lambda_timeout
  tags             = var.default_tags
}

################################################################

resource "aws_iam_role" "kommisjon_tag_resource" {
  name               = "KommisjonTagResource-${data.aws_region.current.name}"
  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": "sts:AssumeRole",
      "Principal": {
        "Service": "lambda.amazonaws.com"
      },
      "Effect": "Allow",
      "Sid": ""
    }
  ]
}
EOF
}

resource "aws_iam_role_policy" "kommisjon_tag_resource" {
  name   = "KommisjonTagResouce-${data.aws_region.current.name}"
  role   = aws_iam_role.kommisjon_tag_resource.id
  policy = templatefile("${path.module}/${var.lambdas_code_path}/kommisjon_tag_resource/iam_policy.tftpl", { account_id = data.aws_caller_identity.current.account_id })
}

resource "aws_lambda_function" "kommisjon_tag_resource" {
  filename         = data.archive_file.kommisjon_tag_resource.output_path
  function_name    = "kommisjon-tag-resource"
  role             = aws_iam_role.kommisjon_tag_resource.arn
  handler          = "main.lambda_handler"
  source_code_hash = data.archive_file.kommisjon_tag_resource.output_base64sha256
  runtime          = "python3.9"
  architectures    = ["arm64"]
  timeout          = var.lambda_timeout
  tags             = var.default_tags
}

#################################################################

resource "aws_iam_role" "kommisjon_update_node_status" {
  name               = "KommisjonUpdateNodeStatus-${data.aws_region.current.name}"
  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": "sts:AssumeRole",
      "Principal": {
        "Service": "lambda.amazonaws.com"
      },
      "Effect": "Allow",
      "Sid": ""
    }
  ]
}
EOF
}

resource "aws_iam_role_policy" "kommisjon_update_node_status" {
  name   = "KommisjonUpdateNodeStatus-${data.aws_region.current.name}"
  role   = aws_iam_role.kommisjon_update_node_status.id
  policy = templatefile("${path.module}/${var.lambdas_code_path}/kommisjon_update_node_status/iam_policy.tftpl", { aws_region = data.aws_region.current.name })
}

# I had to manually add the trigger on the lambda function. Even though it's present on EventBridge, it doesn't get activated
resource "aws_lambda_function" "kommisjon_update_node_status" {
  filename         = data.archive_file.kommisjon_update_node_status.output_path
  function_name    = "kommisjon-update-node-status"
  role             = aws_iam_role.kommisjon_update_node_status.arn
  handler          = "main.lambda_handler"
  source_code_hash = data.archive_file.kommisjon_update_node_status.output_base64sha256
  runtime          = "python3.9"
  architectures    = ["arm64"]
  timeout          = var.lambda_timeout
  tags             = var.default_tags
}

###############################

resource "aws_cloudwatch_event_rule" "kommisjon_update_after_provision" {
  name        = "kommisjon-update-after-provision"
  description = "Triggers the kommisjon-update-node-status lambda function after the node has been provisioned with SSM Distributor (deployed the Ansible Pull Agent)"

  event_pattern = <<EOF
{
  "source": ["aws.ssm"],
  "detail-type": ["EC2 State Manager Instance Association State Change"],
  "detail": {
    "status": ["Success"]
  }
}
EOF
  tags          = var.default_tags
}


resource "aws_cloudwatch_event_target" "kommisjon_update_after_provision" {
  rule      = aws_cloudwatch_event_rule.kommisjon_update_after_provision.name
  target_id = aws_lambda_function.kommisjon_update_node_status.id
  arn       = aws_lambda_function.kommisjon_update_node_status.arn
}


resource "aws_ssm_document" "session_manager_prefs" {
  name            = "SSM-SessionManagerRunShell"
  document_type   = "Session"
  document_format = "JSON"

  content = <<EOF
{
  "schemaVersion": "1.0",
  "description": "Document to hold regional settings for Session Manager",
  "sessionType": "Standard_Stream",
  "inputs": {
    "s3BucketName": "",
    "s3KeyPrefix": "",
    "s3EncryptionEnabled": true,
    "cloudWatchLogGroupName": "",
    "cloudWatchEncryptionEnabled": true,
    "idleSessionTimeout": "20",
    "maxSessionDuration": "",
    "cloudWatchStreamingEnabled": true,
    "runAsEnabled": false,
    "runAsDefaultUser": "",
    "shellProfile": {
      "windows": "",
      "linux": ""
    }
  }
}
EOF
}
