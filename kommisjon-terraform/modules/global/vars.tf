variable "default_tags" {
  type = map(any)
  default = {
    "Application" = "Kommisjon"
    "Stack"       = "Provisioning"
    "Terraform"   = "True"
    "Squad"       = "Infra"
    "Environment" = "Production"
  }
}

variable "kommisjon_regional_bucket_name_pattern" {
  type    = string
  default = "kommisjon-nodes"
}

variable "kommisjon_nodes_s3_name" {
  type    = string
  default = "kommisjon-nodes"
}

variable "lambdas_packages_path" {
  type    = string
  default = "./lambdas/packages"
}

variable "lambdas_code_path" {
  type    = string
  default = "./lambdas/code"
}

variable "lambda_timeout" {
  type    = number
  default = 15
}

variable "kommisjon_sns_topic_name" {
  type    = string
  default = "kommisjon-upload-server-definition"
}
