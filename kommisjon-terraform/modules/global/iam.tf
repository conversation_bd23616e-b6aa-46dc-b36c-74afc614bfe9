# IAM

resource "aws_iam_user" "kommisjon" {
  name = "kommisjon"
  path = "/fingermark/eyecue/kommisjon/"

  tags = var.default_tags
}

resource "aws_iam_access_key" "kommisjon" {
  user = aws_iam_user.kommisjon.name
}

resource "aws_iam_user_policy" "kommisjon" {
  name = "KommisjonAccessPolicy"
  user = aws_iam_user.kommisjon.name

  policy = templatefile("${path.module}/iam_policy.tftpl", { bucket_name = var.kommisjon_regional_bucket_name_pattern })
}


resource "aws_iam_role" "run_command_role" {
  name               = "AmazonEC2RunCommandRoleForManagedInstances"
  assume_role_policy = <<EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Principal": {
                "Service": "ssm.amazonaws.com"
            },
            "Action": "sts:AssumeRole"
        }
    ]
}
EOF
  tags               = var.default_tags
}

resource "aws_iam_role_policy_attachment" "ssm_managed_instance_core" {
  role       = aws_iam_role.run_command_role.id
  policy_arn = "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore"
}

resource "aws_iam_role_policy_attachment" "ssm_directory_service_access" {
  role       = aws_iam_role.run_command_role.id
  policy_arn = "arn:aws:iam::aws:policy/AmazonSSMDirectoryServiceAccess"
}

resource "aws_iam_policy" "ssm_kms_access" {
  name        = "KommisjonKMSAccessforSSM"
  path        = "/kommisjon/"
  description = "It give KMS access to SSM Session Manager"
  policy      = <<EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "ssmmessages:CreateControlChannel",
                "ssmmessages:CreateDataChannel",
                "ssmmessages:OpenControlChannel",
                "ssmmessages:OpenDataChannel"
            ],
            "Resource": "*"
        },
        {
            "Effect": "Allow",
            "Action": [
                "s3:GetEncryptionConfiguration"
            ],
            "Resource": "*"
        },
        {
            "Effect": "Allow",
            "Action": [
                "kms:Decrypt"
            ],
            "Resource": "*"
        }
    ]
}
EOF
}

resource "aws_iam_role_policy_attachment" "ssm_kms_access" {
  role       = aws_iam_role.run_command_role.id
  policy_arn = aws_iam_policy.ssm_kms_access.arn
}

# Policy for cross-account VPN configuration access
resource "aws_iam_policy" "vpn_config_access" {
  name        = "VPNConfigCrossAccountAccess"
  path        = "/kommisjon/"
  description = "Allows access to VPN configuration files in cross-account S3 bucket"
  policy      = <<EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "s3:GetObject"
            ],
            "Resource": [
                "arn:aws:s3:::eyecue-*-vpn-client-configs/clients/*.ovpn"
            ]
        },
        {
            "Effect": "Allow",
            "Action": [
                "s3:ListBucket"
            ],
            "Resource": [
                "arn:aws:s3:::eyecue-*-vpn-client-configs"
            ],
            "Condition": {
                "StringLike": {
                    "s3:prefix": "clients/*"
                }
            }
        }
    ]
}
EOF
}

resource "aws_iam_role_policy_attachment" "vpn_config_access" {
  role       = aws_iam_role.run_command_role.id
  policy_arn = aws_iam_policy.vpn_config_access.arn
}


resource "aws_iam_policy" "kommisjon_slack_bot" {
  name = "KommisjonSlackBot"
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "s3:PutObject",
          "s3:ListBucket",
          "s3:GetObject"
        ]
        Effect   = "Allow"
        Resource = ["${aws_s3_bucket.kommisjon_nodes.arn}", "${aws_s3_bucket.kommisjon_nodes.arn}/*"]
      }
    ]
  })
  tags_all = var.default_tags
  tags = var.default_tags
}

resource "aws_iam_role" "kommisjon_slack_bot" {
  name = "KommisjonSlackBot"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          AWS = "arn:aws:iam::055313672806:root"
        }
      }
    ]
  })
  tags = var.default_tags
}

resource "aws_iam_role_policy_attachment" "kommisjon_slack_bot" {
  policy_arn = aws_iam_policy.kommisjon_slack_bot.arn
  role       = aws_iam_role.kommisjon_slack_bot.name
}
