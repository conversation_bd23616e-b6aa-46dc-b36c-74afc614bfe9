data "aws_caller_identity" "current" {}
data "aws_region" "current" {}

data "archive_file" "kommisjon_update_dynamodb" {
  type        = "zip"
  source_dir  = "${path.module}/${var.lambdas_code_path}/kommisjon_update_dynamodb"
  output_path = "${path.module}/${var.lambdas_packages_path}/kommisjon_update_dynamodb.zip"
}

data "archive_file" "kommisjon_update_ansible_inventory" {
  type        = "zip"
  source_dir  = "${path.module}/${var.lambdas_code_path}/kommisjon_update_ansible_inventory"
  output_path = "${path.module}/${var.lambdas_packages_path}/kommisjon_update_ansible_inventory.zip"
  depends_on = [ null_resource.install_python_dependencies ]
}