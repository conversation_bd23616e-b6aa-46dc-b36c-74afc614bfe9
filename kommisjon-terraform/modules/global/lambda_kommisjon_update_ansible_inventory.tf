## TODO: 
## - Even though SNS adds Lambdas subscriptions, Lambda trigers need to be added manually

resource "null_resource" "install_python_dependencies" {
  triggers = {
    files_sha1 = sha1(join("", [for f in fileset("${path.module}/lambdas/code/kommisjon_update_ansible_inventory", "**") : filesha1("${path.module}/lambdas/code/kommisjon_update_ansible_inventory/${f}")]))
  }
  provisioner "local-exec" {
    command = "bash -C ${path.module}/lambdas/install-dependencies.sh"
    environment = {
      path          = "${path.module}/lambdas/code/kommisjon_update_ansible_inventory"
      build_path    = "../../build/kommisjon_update_ansible_inventory" # The build path is relative to the $path variable. In this case it will sit under "${path.module}/lambdas
      function_name = "kommisjon-update-ansible-inventory"
      runtime       = "python3.10"
    }
  }
}

resource "aws_iam_role" "kommisjon_update_ansible_inventory" {
  name               = "KommisjonUpdateAnsibleInventory-${data.aws_region.current.name}"
  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": "sts:AssumeRole",
      "Principal": {
        "Service": "lambda.amazonaws.com"
      },
      "Effect": "Allow",
      "Sid": ""
    }
  ]
}
EOF
}

resource "aws_iam_role_policy" "kommisjon_update_ansible_inventory" {
  name   = "KommisjonUpdateAnsibleInventory-${data.aws_region.current.name}"
  role   = aws_iam_role.kommisjon_update_ansible_inventory.id
  policy = templatefile("${path.module}/${var.lambdas_code_path}/kommisjon_update_ansible_inventory/iam_policy.tftpl", { account_id = data.aws_caller_identity.current.account_id })
}

resource "aws_lambda_function" "kommisjon_update_ansible_inventory" {
  filename                        = data.archive_file.kommisjon_update_ansible_inventory.output_path
  function_name                   = "kommisjon-update-ansible-inventory"
  role                            = aws_iam_role.kommisjon_update_ansible_inventory.arn
  handler                         = "main.lambda_handler"
  source_code_hash                = data.archive_file.kommisjon_update_ansible_inventory.output_base64sha256
  runtime                         = "python3.10"
  architectures                   = ["x86_64"] # The Git Lambda Layer is not compatible with ARM
  timeout                         = 60
  reserved_concurrent_executions  = 1  # Set reserved concurrency to 1
  layers = [
    "arn:aws:lambda:${data.aws_region.current.name}:************:layer:git-lambda2:5"
  ]

  environment {
    variables = {
      BITBUCKET_PROJECT_KEY     = "fingermarkltd"
      BITBUCKET_REPOSITORY_SLUG = "eyecue-server-setup"
    }
  }
  tags = var.default_tags
}

resource "aws_lambda_permission" "kommisjon_update_ansible_inventory" {
  statement_id  = "AllowExecutionFromKommisjonNodes"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.kommisjon_update_ansible_inventory.arn
  principal     = "s3.amazonaws.com"
  source_arn    = aws_s3_bucket.kommisjon_nodes.arn
}

resource "aws_ssm_parameter" "bitbucket_app_passwd" {
  name        = "BITBUCKET_APP_PASSWD"
  description = "https://bitbucket.org/account/settings/app-passwords/"
  type        = "SecureString"
  value       = "not-the-real-value"
  lifecycle {
    ignore_changes = [value]
  }
  tags = var.default_tags
}

resource "aws_ssm_parameter" "bitbucket_repository_token" {
  name  = "BITBUCKET_REPOSITORY_TOKEN"
  type  = "SecureString"
  value = "not-the-real-value"
  lifecycle {
    ignore_changes = [value]
  }
  tags = var.default_tags
}
