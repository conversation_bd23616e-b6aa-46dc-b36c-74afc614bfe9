#  terraform output -json | jq '.["kommisjon_access_key"]["value"]' | sed -e s/\"//g
output "kommisjon_access_key" {
  value = aws_iam_access_key.kommisjon.id
}

#  terraform output -json | jq '.["kommisjon_secret_key"]["value"]' | sed -e s/\"//g
output "kommisjon_secret_key" {
  value     = aws_iam_access_key.kommisjon.secret
  sensitive = true
}

output "kommisjon_s3_bucket_id" {
  value = aws_s3_bucket.kommisjon_nodes.id
}

output "kommisjon_run_command_role_id" {
  value = aws_iam_role.run_command_role.id
}
