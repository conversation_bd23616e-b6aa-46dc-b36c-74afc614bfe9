# S3

resource "aws_s3_bucket" "kommisjon_nodes" {
  bucket = "${var.kommisjon_nodes_s3_name}-${data.aws_region.current.name}-${data.aws_caller_identity.current.account_id}"
}

resource "aws_s3_bucket_public_access_block" "kommisjon_nodes" {
  bucket = aws_s3_bucket.kommisjon_nodes.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

resource "aws_s3_bucket_versioning" "kommisjon_nodes" {
  bucket = aws_s3_bucket.kommisjon_nodes.id
  versioning_configuration {
    status = "Disabled"
  }
}

resource "aws_s3_bucket_notification" "kommisjon_nodes" {
  bucket = aws_s3_bucket.kommisjon_nodes.id

  topic {
    topic_arn = aws_sns_topic.kommisjon_upload_server_definition.arn
    events    = ["s3:ObjectCreated:Put"]
  }
}
