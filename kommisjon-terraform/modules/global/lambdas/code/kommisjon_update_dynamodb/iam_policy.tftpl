{"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": "logs:CreateLogGroup", "Resource": "arn:aws:logs:*:${account_id}:*"}, {"Effect": "Allow", "Action": ["logs:CreateLogStream", "logs:PutLogEvents"], "Resource": ["arn:aws:logs:*:${account_id}:log-group:/aws/lambda/kommisjon-update-dynamodb:*"]}, {"Effect": "Allow", "Action": ["ssm:Describe*", "ssm:Get*", "ssm:List*"], "Resource": "arn:aws:ssm:*:${account_id}:managed-instance/*"}, {"Effect": "Allow", "Action": ["dynamodb:Get*", "dynamodb:PutItem", "dynamodb:List*", "dynamodb:UpdateItem"], "Resource": ["arn:aws:dynamodb:ap-southeast-2:${account_id}:table/eyecue-inventory-table"]}, {"Effect": "Allow", "Action": ["s3:GetObject"], "Resource": "arn:aws:s3:::kommisjon-nodes-*/*"}]}