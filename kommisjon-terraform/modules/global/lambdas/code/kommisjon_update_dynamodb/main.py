# This Lambda function isn't really part of Kommisjon, but it has been created to keep backward compatibility with the old provisioning process

import boto3
import json
from datetime import datetime

def get_node_from_bucket(bucket_name, node_id, aws_region='ap-southeast-2'):
    client = boto3.Session().client('s3')
    try:
        response = client.get_object(
            Bucket=bucket_name,
            Key=f'{node_id}'
        )
        print(f'Object found: {response}')
        return response
    except:
        print(f'Object NOT found: {response}')
        return None
    
def put_item_into_dynamodb(server_definition=None, table_name='eyecue-inventory-table'):
    # Add/ Update entry in dynamodb
    item = dict()
    dynamodb_client = boto3.Session().client('dynamodb', region_name='ap-southeast-2')
    for key, value in server_definition.items():
        if key == 'site_id':
            item['hostname'] = {'S': value }
        else:
            if key == 'timezone':
                item['time_zone'] = {'S': value }
            else: 
                item[key] = {'S': value }
    item['provision_date'] = { 'S': str(datetime.now())}
    print(f'Item for DyanamoDB: {item}')
    # put_item creates or replaces the item.
    db_response = dynamodb_client.put_item(
        TableName=table_name,
        Item=item
    )

    return { 'response': db_response }

def lambda_handler(event, context):
    print(f'EVENT: {event}')
    if event['Records'][0]['EventSource'] == 'aws:sns':
        s3_event = json.loads(event['Records'][0]['Sns']['Message'])
    else:
        if event['Records'][0]['EventSource'] == 'aws:s3':
            s3_event = event['Records'][0]['s3']
    bucket_name = s3_event['Records'][0]['s3']['bucket']['name']
    node_id = s3_event['Records'][0]['s3']['object']['key']
    print(f'{bucket_name}: {node_id}')
    response = get_node_from_bucket(bucket_name, node_id )
    if response is not None:
        server_definition = json.loads(response['Body'].read())
        print(server_definition)
        response = put_item_into_dynamodb(server_definition)
        print(response)
        return {
            'statusCode': 200,
            'body': json.dumps(response)
        }
    else:
        return {
            'statusCode': 404,
            'body': 'Object not found'
        }
