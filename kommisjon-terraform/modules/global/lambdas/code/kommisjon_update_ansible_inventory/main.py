import os
import json
import boto3
import yaml
import yaml_custom_tag
from git_workflow import git_clone, git_commit, git_cleanup
import uuid

def get_node_from_bucket(bucket_name, node_id, aws_region='ap-southeast-2'):
    client = boto3.Session().client('s3')
    try:
        response = client.get_object(
            Bucket=bucket_name,
            Key=f'{node_id}'
        )
        print(f'Object found: {response}')
        return response
    except Exception as e:
        print(f'Object NOT found: {node_id}, Error: {e}')
        return None


def get_inventory_name(customer:str, country:str) -> str:
    inventory_mapping = {
        ('tst', '*'): 'tst_hosts.yml',
        ('dev', '*'): 'dev_hosts.yml',
        ('infra', '*'): 'infra_hosts.yml',
        ('nod', '*'): 'nod_hosts.yml',
        ('poc', '*'): 'poc_hosts.yml',
        ('qa', '*'): 'qa_hosts.yml',

        ('bkg', 'nzl'): 'bkg_hosts.yml',
        ('cfa', 'usa'): 'cfa_hosts.yml',
        ('cul', 'usa'): 'cul_hosts.yml',
        ('czp', 'usa'): 'czp_hosts.yml',
        ('elj', 'aus'): 'elj_hosts.yml',
        ('kfc', 'aus'): 'kfc_hosts.yml',
        ('mcd', 'aus'): 'mcd_hosts.yml',
        ('mnz', 'nzl'): 'mnz_hosts.yml',
        ('pop', 'nzl'): 'pop_hosts.yml',
        ('ptl', 'usa'): 'ptl_hosts.yml',
        ('stb', 'usa'): 'stb_hosts.yml',
        ('zmb', 'aus'): 'zmb_hosts.yml',
    }

    customer = customer.lower()
    country = country.lower()

    inventory_name = inventory_mapping.get((customer, country))
    if inventory_name:
        return inventory_name
    else:
        # If no exact match, check for a wildcard match
        for (mapped_customer, mapped_country), mapped_inventory_name in inventory_mapping.items():
            if mapped_customer == customer and mapped_country == '*':
                return mapped_inventory_name
        # If no wildcard match, return a standardised inventory name
        return f"{customer}-{country}_hosts.yml"


def add_host_to_inventory(host, repo_local_path):
    inventory_name = get_inventory_name(host['customer'], host['country'])
    inventory_path = f'{repo_local_path}/{inventory_name}'

    try:
        with open(inventory_path, 'r') as inventory_file:
            inventory = yaml.safe_load(inventory_file)
        new_inventory_group_name = host['display_name'].replace(' ', '_').lower()
        
        print(f'Creating a new entry for {new_inventory_group_name}')
        inventory_item = dict()
        inventory_item['hosts'] = dict()
        inventory_item['vars'] = dict()
        for key, value in host.items():
            inventory_item['vars'][key] = value
        inventory_item['hosts'][f'{host["site_id"]}.eyeq.vpn'] = dict()
        inventory['all']['children']['production']['children'][new_inventory_group_name] = inventory_item
        print(f'Item:\n{inventory_item}\nhas been added')

        with open(inventory_path, 'w') as output_file:
            yaml.dump(inventory, output_file)
        return inventory_path
    except Exception as e:
        print(f'ERROR: {e}')
        return None

def lambda_handler(event, context):
    '''
    When a new server definition is pushed to S3, the event will trigger this lambda function to:
      - Get the full server definition object
      - Clone the eyecue-server-setup repo
      - Parse the Ansible inventory into memory
      - Add the new host, based on the SDO
      - Version the new inventory
    '''
    print(f'EVENT: {event}')
    if event['Records'][0]['EventSource'] == 'aws:sns':
        s3_event = json.loads(event['Records'][0]['Sns']['Message'])
    else:
        if event['Records'][0]['EventSource'] == 'aws:s3':
            s3_event = event
    bucket_name = s3_event['Records'][0]['s3']['bucket']['name']
    node_id = s3_event['Records'][0]['s3']['object']['key']
    print(f'{bucket_name}: {node_id}')
    response = get_node_from_bucket(bucket_name, node_id)
    if response is not None:
        server_definition = json.loads(response['Body'].read())
        print(f'Server definition: {server_definition}')
        
        unique_id = uuid.uuid4().hex
        repo_local_path = f'/tmp/ansible_{unique_id}'
        
        repo = git_clone(repository_local_path=repo_local_path)
        inventory_path = add_host_to_inventory(server_definition, repo_local_path)
    
        if inventory_path is not None and repo is not None:
            git_commit(repo=repo)
            git_cleanup(repo, repository_local_path=repo_local_path)
            return {
                "statusCode": 200,
            }
        else:
            print(f'There was an error and the inventory couldn\'t be versioned')
    else:
        print(f'Object {node_id} not found')

if __name__ == '__main__':
    '''
    TEST:
    export BITBUCKET_PROJECT_KEY=fingermarkltd
    export BITBUCKET_REPOSITORY_SLUG=eyecue-server-setup
    export BITBUCKET_REPOSITORY_TOKEN=<TOKEN>
    export BITBUCKET_APP_PASSWD=<APP_PASSWD>
    '''
    event = {
        'Records': [
            {
                'EventSource': 'aws:s3',
                's3': {
                    'bucket': {
                        'name': 'kommisjon-nodes-ap-southeast-2-613615422941'
                    },
                    'object': {
                        'key': 'PC2906GB.json'
                    }
                }
            }
        ]
    }
    lambda_handler(event, None)
