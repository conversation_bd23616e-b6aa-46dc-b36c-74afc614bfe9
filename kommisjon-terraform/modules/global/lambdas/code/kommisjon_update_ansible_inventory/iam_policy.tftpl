{"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": "logs:CreateLogGroup", "Resource": "arn:aws:logs:*:${account_id}:*"}, {"Effect": "Allow", "Action": ["logs:CreateLogStream", "logs:PutLogEvents"], "Resource": ["arn:aws:logs:*:${account_id}:log-group:/aws/lambda/kommisjon-update-ansible-inventory:*"]}, {"Effect": "Allow", "Action": ["ssm:Describe*", "ssm:Get*", "ssm:List*"], "Resource": "arn:aws:ssm:*:${account_id}:managed-instance/*"}, {"Effect": "Allow", "Action": ["s3:GetObject"], "Resource": "arn:aws:s3:::kommisjon-nodes-*/*"}, {"Effect": "Allow", "Action": ["ssm:GetParameter", "ssm:GetParameters", "ssm:GetParametersByPath"], "Resource": ["arn:aws:ssm:*:${account_id}:parameter/*"]}]}