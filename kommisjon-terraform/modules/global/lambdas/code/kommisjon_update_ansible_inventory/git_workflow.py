import os
import boto3
from git import Repo, GitCommandError
import requests
import json
from datetime import datetime

def get_secret_from_parameter_store(parameter_name):
    try:
        ssm_client = boto3.Session().client('ssm')
        response = ssm_client.get_parameter(Name=parameter_name, WithDecryption=True)
        return response['Parameter']['Value']

    except Exception as e:
        print(f'ERROR: {e}')
        return None

BITBUCKET_REPOSITORY_TOKEN = os.getenv('BITBUCKET_REPOSITORY_TOKEN', get_secret_from_parameter_store('BITBUCKET_REPOSITORY_TOKEN'))
BITBUCKET_APP_PASSWD = os.getenv('BITBUCKET_APP_PASSWD', get_secret_from_parameter_store('BITBUCKET_APP_PASSWD')) # https://bitbucket.org/account/settings/app-passwords/
WORKSPACE = os.getenv('BITBUCKET_WORKSPACE', 'fingermarkltd')
REPOSITORY_SLUG = os.getenv('BITBUCKET_REPOSITORY_SLUG', 'eyecue-server-setup')

def bitbucket_create_pr(pull_request_title=None, pull_request_description=None, pull_request_branch_name=None):
    '''
    https://developer.atlassian.com/cloud/bitbucket/rest/api-group-pullrequests/#api-repositories-workspace-repo-slug-pullrequests-post
    '''

    bb_username = os.getenv('BITBUCKET_USERNAME', 'eyecue-deployer')
    bb_password = BITBUCKET_APP_PASSWD
    basic_auth = requests.auth.HTTPBasicAuth(bb_username, bb_password)
    baseurl = 'api.bitbucket.org'
    url = f'https://{baseurl}/2.0/repositories/{WORKSPACE}/{REPOSITORY_SLUG}'
    headers = {
        "Accept": "application/json",
        "Content-Type": "application/json",
    }
    try:
        print(f'INFO: Getting default reviewers...')
        path = '/default-reviewers'
        default_reviewers = requests.get(
            url=f'{url}{path}',
            auth=basic_auth,
            headers=headers,
        ).json()
        print(f'DEBUG: Default reviewers: {default_reviewers["values"]}')
        path = '/pullrequests'
        payload = json.dumps({
            "title": pull_request_title,
            "description": pull_request_description,
            "source": {            
                "branch": {
                    "name": pull_request_branch_name,
                }
            },
            "reviewers": default_reviewers['values'],
            "close_source_branch": True,
        })
        print(f'INFO: Creating Bitbucket pull request')
        response = requests.post(
            url=f'{url}{path}',
            auth=basic_auth,
            headers=headers,
            data=payload
        )
        print(f'INFO: Pull request created')
        return response
    except Exception as e:
        print(f'ERROR: There was an error creating the Bitbucket PR: {e}')
        return None

def git_clone(bitbucket_repository_url=None, repository_local_path='/tmp/ansible'):
    if bitbucket_repository_url is None:
        bitbucket_repository_url = f'https://x-token-auth:{BITBUCKET_REPOSITORY_TOKEN}@bitbucket.org/{WORKSPACE}/{REPOSITORY_SLUG}.git'
    try:
        print(f'INFO: Cloning the repository onto {repository_local_path}...')
        repo = Repo.clone_from(bitbucket_repository_url, repository_local_path)           
        print(f'INFO: Repository cloned')
        return repo
    except Exception as e:
        print(f'ERROR: {e}')
        return None

def git_commit(repo=None):
    print(f'INFO: Working on https://bitbucket.org/{WORKSPACE}/{REPOSITORY_SLUG}/src/master/')
    try:
        commit_message = 'Updating inventory file'
        new_branch_name = f'kommisjon-{datetime.now().strftime("%d%m%y%H%M%S")}'

        print(f'INFO: Creating new branch {new_branch_name}')
        new_branch = repo.create_head(new_branch_name)
        new_branch.checkout()
        print(f'INFO: Branch {new_branch_name} created')
        print(f'INFO: Staging file...')
        repo.index.add(['*_hosts.yml'])

        # Commit the changes
        repo.index.commit(commit_message)

        # Push the new branch to the remote repository
        origin = repo.remote('origin')
        origin.push(new_branch)

        # Create a pull request
        pull_request_title = 'Updating Ansible Inventory'
        pull_request_description = f'This PR was automatically created by Kommisjon using AWS Lambda.'
        bb_response = bitbucket_create_pr(pull_request_title, pull_request_description, new_branch_name)
        if bb_response and bb_response.status_code not in (200, 201, 202):
            print(f'ERROR: Status Code: {bb_response.status_code}: {bb_response}')
    except GitCommandError as e:
        print(f'ERROR: There was an error during git operations: {e}')
    except Exception as e:
        print(f'ERROR: There was an error versioning your file: {e}')

def git_cleanup(repo, repository_local_path='/tmp/ansible'):
    try:
        repo.close()
        if os.path.exists(repository_local_path):
            for root, dirs, files in os.walk(repository_local_path, topdown=False):
                for name in files:
                    os.remove(os.path.join(root, name))
                for name in dirs:
                    os.rmdir(os.path.join(root, name))
            os.rmdir(repository_local_path)
        print(f'INFO: Repository cleaned up')
    except Exception as e:
        print(f'ERROR: Error during cleanup: {e}')
