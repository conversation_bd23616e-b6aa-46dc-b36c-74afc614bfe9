import yaml

class VaultTag(yaml.YAMLObject):
    yaml_tag = u'!vault'

    def __init__(self, env_var):
        self.env_var = env_var

    def __repr__(self):
        return f'VaultTag({self.env_var})'

    @classmethod
    def from_yaml(cls, loader, node):
        return VaultTag(node.value)

    @classmethod
    def to_yaml(cls, dumper, data):
        dumped = dumper.represent_scalar(cls.yaml_tag, data.env_var, style='|')
        return dumped
yaml.SafeLoader.add_constructor('!vault', VaultTag.from_yaml)
yaml.SafeDumper.add_multi_representer(VaultTag, VaultTag.to_yaml)