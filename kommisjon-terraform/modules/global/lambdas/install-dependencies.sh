#!/bin/bash

# Move to the lambda directory
# Check if a virtual environment already exists, if it does, then delete it
# Check if a build directory already exists, if it does, then delete it
# Create an activate the virtualenvironment within the lambda directory

cd $path
echo "On $PWD"

VENV_PATH="./.venv"

if [ -d "$VENV_PATH" ]; then
    echo "Found an environment. Cleaning it up..."
    rm -r "$VENV_PATH"
fi

if [ -d "$build_path" ]; then
    echo "Found a previous build. Cleaning it up..."
    rm -r "$build_path"
fi
mkdir -p $build_path

# Create and activate virtual environment...
python3 -m pip install virtualenv
python3 -m virtualenv -p $runtime $VENV_PATH
source $VENV_PATH/bin/activate

python -m pip install -r ./requirements.txt

echo "Copying source code from $VENV_PATH/lib/$runtime/site-packages/ to $build_path"
cp -r $VENV_PATH/lib/$runtime/site-packages/* $build_path
cp -r ./*.py $build_path

deactivate
rm -r $VENV_PATH