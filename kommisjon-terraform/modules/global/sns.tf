resource "aws_sns_topic" "kommisjon_upload_server_definition" {
  name = var.kommisjon_sns_topic_name
  tags = var.default_tags
  policy = templatefile(
    "${path.module}/sns_access_policy.tftpl", {
      kommisjon_sns_topic_name = var.kommisjon_sns_topic_name,
      bucket_name              = var.kommisjon_regional_bucket_name_pattern
      aws_region               = data.aws_region.current.name
    }
  )
}

resource "aws_sns_topic_subscription" "kommisjon_update_dynamodb" {
  topic_arn = aws_sns_topic.kommisjon_upload_server_definition.arn
  protocol  = "lambda"
  endpoint  = aws_lambda_function.kommisjon_update_dynamodb.arn
}

resource "aws_sns_topic_subscription" "kommisjon_update_ansible_inventory" {
  topic_arn = aws_sns_topic.kommisjon_upload_server_definition.arn
  protocol  = "lambda"
  endpoint  = aws_lambda_function.kommisjon_update_ansible_inventory.arn
}
