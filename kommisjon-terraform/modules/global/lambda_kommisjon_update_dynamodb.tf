##################################################################

resource "aws_iam_role" "kommisjon_update_dynamodb" {
  name               = "KommisjonUpdateDynamoDb-${data.aws_region.current.name}"
  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": "sts:AssumeRole",
      "Principal": {
        "Service": "lambda.amazonaws.com"
      },
      "Effect": "Allow",
      "Sid": ""
    }
  ]
}
EOF
}

resource "aws_iam_role_policy" "kommisjon_update_dynamodb" {
  name   = "KommisjonUpdateDynamoDb-${data.aws_region.current.name}"
  role   = aws_iam_role.kommisjon_update_dynamodb.id
  policy = templatefile("${path.module}/${var.lambdas_code_path}/kommisjon_update_dynamodb/iam_policy.tftpl", { account_id = data.aws_caller_identity.current.account_id })
}

resource "aws_lambda_function" "kommisjon_update_dynamodb" {
  filename         = data.archive_file.kommisjon_update_dynamodb.output_path
  function_name    = "kommisjon-update-dynamodb"
  role             = aws_iam_role.kommisjon_update_dynamodb.arn
  handler          = "main.lambda_handler"
  source_code_hash = data.archive_file.kommisjon_update_dynamodb.output_base64sha256
  runtime          = "python3.9"
  architectures    = ["arm64"]
  timeout          = var.lambda_timeout
  tags             = var.default_tags
}

resource "aws_lambda_permission" "allow_kommisjon_nodes" {
  statement_id  = "AllowExecutionFromKommisjonNodes"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.kommisjon_update_dynamodb.arn
  principal     = "s3.amazonaws.com"
  source_arn    = aws_s3_bucket.kommisjon_nodes.arn
}
