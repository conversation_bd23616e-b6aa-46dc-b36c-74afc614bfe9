# Kommisjon
Project to commision and register Eyecue servers on AWS Systems Manager

## Installing the SSM agent

NOTE: This repository is a submodule if the eyecue-golden-image repository

## Architecture
![alt text](kommisjon-architecture.png)



## Terraform Docs
## Requirements

| Name | Version |
|------|---------|
| <a name="requirement_aws"></a> [aws](#requirement\_aws) | >= 2.7.0 |

## Providers

| Name | Version |
|------|---------|
| <a name="provider_aws"></a> [aws](#provider\_aws) | >= 2.7.0 |

## Modules

| Name | Source | Version |
|------|--------|---------|
| <a name="module_ap_southeast_2"></a> [ap\_southeast\_2](#module\_ap\_southeast\_2) | ./modules/regional | n/a |
| <a name="module_global"></a> [global](#module\_global) | ./modules/global | n/a |
| <a name="module_us_east_1"></a> [us\_east\_1](#module\_us\_east\_1) | ./modules/regional | n/a |
| <a name="module_us_west_1"></a> [us\_west\_1](#module\_us\_west\_1) | ./modules/regional | n/a |
| <a name="module_us_west_2"></a> [us\_west\_2](#module\_us\_west\_2) | ./modules/regional | n/a |

## Resources

| Name | Type |
|------|------|
| [aws_caller_identity.current](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/caller_identity) | data source |
| [aws_region.current](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/region) | data source |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_aws_account_id"></a> [aws\_account\_id](#input\_aws\_account\_id) | n/a | `string` | n/a | yes |
| <a name="input_default_tags"></a> [default\_tags](#input\_default\_tags) | n/a | `map(any)` | <pre>{<br>  "Application": "Kommisjon",<br>  "Environment": "Production",<br>  "Squad": "Infra",<br>  "Stack": "Provisioning",<br>  "Terraform": "True"<br>}</pre> | no |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_kommisjon_access_key"></a> [kommisjon\_access\_key](#output\_kommisjon\_access\_key) | terraform output -json \| jq '.["kommisjon\_access\_key"]["value"]' \| sed -e s/\"//g |
| <a name="output_kommisjon_secret_key"></a> [kommisjon\_secret\_key](#output\_kommisjon\_secret\_key) | terraform output -json \| jq '.["kommisjon\_secret\_key"]["value"]' \| sed -e s/\"//g |


You can also use the config.sh script to create this file. It requires access to Terraform

## Deploying Kommisjon

1. Set up this project as a Terraform module:
```
module "kommisjon" {
  source = "git::*****************:fingermarkltd/eyecue-kommisjon.git//terraform?ref=master"
  customer_short          = var.CLIENT_NAME
}

output "kommisjon_access_key" {
  value = module.kommisjon.kommisjon_access_key
}

output "kommisjon_secret_key" {
  value     = module.kommisjon.kommisjon_secret_key
  sensitive = true
}
```
2. Deploy the module using Terraform
```
terraform init
terraform plan -out=plan
terraform apply plan
```
3. Get the IAM credentials from TF
```
terraform output -json | jq '.["kommisjon_access_key"]["value"]'
terraform output -json | jq '.["kommisjon_secret_key"]["value"]'
```

4. Generate the config.ini file using this credentials

```
[CREDENTIALS]
AWS_ACCESS_KEY_ID = AAAABBBBCCCCDDD
AWS_SECRET_ACCESS_KEY = /123456789xzyxxxaxaxa

[SLACK]
SLACK_WEB_HOOK = https://hooks.slack.com/services/ACACACAC/BABABABABA/5465asf6a54sf65465
SLACK_CHANNEL = #channel

[KOMMISJON]
AUTO_REGISTER = -n
```

5. Now deploy this application using the Kommisjon Ansible playbook and pass it the config.ini file before running it

6. Servers should register against AWS using SSM

7. Next steps are:
 - Deploying the ansible-agent application. See https://bitbucket.org/fingermarkltd/ansible-agent/src/master/


 

# Manually installing the SSM agent (no need to, this is included into the Golden Image)

```
#!/bin/bash

# https://docs.aws.amazon.com/systems-manager/latest/userguide/agent-install-ubuntu-64-snap.html

SSM_ACTIVATION_CODE=""
SSM_ACTIVATION_ID=""


sudo snap install amazon-ssm-agent --classic
sudo systemctl stop snap.amazon-ssm-agent.amazon-ssm-agent.service
sudo /snap/amazon-ssm-agent/current/amazon-ssm-agent -register -code $SSM_ACTIVATION_CODE -id $SSM_ACTIVATION_ID -region "ap-southeast-2" 
sudo systemctl start snap.amazon-ssm-agent.amazon-ssm-agent.service

/snap/amazon-ssm-agent/current/amazon-ssm-agent -register -code $SSM_ACTIVATION_CODE -id $SSM_ACTIVATION_ID -region "ap-southeast-2"




sudo /snap/amazon-ssm-agent/current/amazon-ssm-agent -register -code "" -id "" -region "ap-southeast-2" -tags "Key=Serial,Value=1234" 
```




## Docs

- https://www.amazonaws.cn/en/systems-manager/
- https://docs.aws.amazon.com/systems-manager/latest/userguide/distributor-working-with-packages-create.html#distributor-working-with-packages-create-simple
- https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_UpdateDocument.html#API_UpdateDocument_RequestSyntax
- https://docs.aws.amazon.com/systems-manager/latest/userguide/distributor-working-with-packages-version.html#add-pkg-version-cli

## Price
https://aws.amazon.com/systems-manager/pricing/


