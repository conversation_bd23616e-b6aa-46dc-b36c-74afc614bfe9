## Terraform backend access role
resource "aws_iam_role" "terraform_backend_access" {
  name                 = "TerraformBackendAccess"
  path                 = "/"
  max_session_duration = 3600
  assume_role_policy   = <<POLICY
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "AWS": "arn:aws:iam::${var.trusted_aws_account_id}:root"
      },
      "Action": "sts:AssumeRole",
      "Condition": {}
    },
    {
      "Effect": "Allow",
      "Principal": {
          "AWS": "arn:aws:iam::************:root"
      },
      "Action": "sts:AssumeRole",
      "Condition": {}
    },
    {
			"Effect": "Allow",
			"Principal": {
				"AWS": "arn:aws:iam::************:role/kubeiam-atlantis"
			},
			"Action": "sts:AssumeRole",
			"Condition": {}
		}
  ]
}
POLICY
}

data "aws_iam_policy_document" "terraform_backend_access_policy" {
  statement {
    sid = "S3List"

    actions = [
      "s3:ListBucket",
    ]
    resources = [
      "arn:aws:s3:::fingermark-data-terraform",
    ]
  }
  statement {
    sid = "S3Put"

    actions = [
      "s3:GetObject",
      "s3:PutObject"
    ]
    resources = [
      "arn:aws:s3:::fingermark-data-terraform/*",
    ]
  }
  statement {
    sid = "DynamoDBReadWrite"

    actions = [
      "dynamodb:GetItem",
      "dynamodb:PutItem",
      "dynamodb:DeleteItem"
    ]
    resources = [
      "arn:aws:dynamodb:ap-southeast-2:047783385012:table/terraform-state",
    ]
  }
}


resource "aws_iam_policy" "terraform_backend_access_policy" {
  name   = "TerraformBackendAccessPolicy"
  path   = "/"
  policy = data.aws_iam_policy_document.terraform_backend_access_policy.json
}

resource "aws_iam_policy_attachment" "terraform_backend_access_policy" {
  name       = "TerraformBackendAccessPolicy"
  roles      = [aws_iam_role.terraform_backend_access.name]
  policy_arn = aws_iam_policy.terraform_backend_access_policy.arn
}
