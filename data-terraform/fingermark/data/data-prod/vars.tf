###  Destination AWS Account  ###

variable "APP_STAGE" {
  description = "The environment indicator. Options [dev, prod]"
  default     = "prod"
}

variable "trusted_aws_account_id" {
  description = "This should be the aws account ID of the staff account"
  default     = "************"
}

variable "stb_replication_bucket_arn" {
  description = "STB Replication bucket arn"
  default     = "arn:aws:s3:::fm-datalake-customer-stb"
}

variable "destination_stb_kms_key_arn" {
  description = "Destination - STB KMS KEY"
  default     = "arn:aws:kms:us-west-2:************:key/e19474d7-38d6-43ac-bed4-76306efd9773"
}

variable "BASTION_US_S3_BUCKET_NAME" {
  description = "S3 bucket name USA"
  default     = "data-prod-us-bastion-v2"
}

variable "BASTION_AU_S3_BUCKET_NAME" {
  description = "S3 bucket name AU"
  default     = "data-prod-au-bastion-v2"
}

variable "VPN_AU_S3_BUCKET_NAME" {
  description = "S3 bucket name AU"
  default     = "data-prod-au-vpn"
}

variable "VPN_US_E1_S3_BUCKET_NAME" {
  description = "S3 bucket name AU"
  default     = "data-prod-us-es1-vpn"
}

variable "ADHOC_S3_ARN" {
  description = "Adhoc datalake s3 arn"
  default     = "arn:aws:s3:::fm-prod-adhoc-datalake-ap-southeast-2"
}

variable "DYNAMODB_S3_ARN" {
  description = "Dynamodb export s3 arn"
  default     = "arn:aws:iam::************:role/infra-dynamodbs3-export-role"
}

variable "cross_account_env_roles" {
  type = map(string)
  default = {
    "************" = "redshift_stream_aaccess_role"
  }
}

#************ CFA
#************ CUL
#************ PTL
#************ NorthVue
#************ BKG-USA
#************ POP-USA
variable "cross_account_kinesis_roles_us_e1" {
  type = list(object({
    account_id = string
    role_name  = string
  }))

  default = [
    { account_id = "************", role_name = "redshift_stream_aaccess_role" }, # CUL
    { account_id = "************", role_name = "redshift_stream_aaccess_role" }, # PTL
    { account_id = "************", role_name = "redshift_stream_aaccess_role" }, # CFA
    { account_id = "************", role_name = "redshift_stream_aaccess_role" }, # BKG-USA
    { account_id = "************", role_name = "redshift_stream_access_role" },  # POP-USA
    { account_id = "************", role_name = "redshift_stream_aaccess_role" }  # NorthVue
  ]
}

variable "cross_account_iot_kinesis_failure_lambda_roles_us_e1" {
  default = {
    "************" = "eyecue-iot-kinesis-fallback-lambda-role"
    "************" = "eyecue-iot-kinesis-fallback-lambda-role"
    "************" = "eyecue-iot-kinesis-fallback-lambda-role"
  }
}

# "************" = "eyecue-iot-kinesis-fallback-lambda-role"
# "************" = "eyecue-iot-kinesis-fallback-lambda-role"
# "************" = "eyecue-iot-kinesis-fallback-lambda-role"

variable "cross_account_kinesis_roles_us_w1" {
  type = list(object({
    account_id = string
    role_name  = string
  }))

  default = [
    { account_id = "************", role_name = "redshift_stream_aaccess_role" },
    { account_id = "************", role_name = "redshift_stream_aaccess_role" },
    { account_id = "************", role_name = "redshift_stream_access_role" }
  ]
}

variable "cross_account_iot_kinesis_failure_lambda_roles_us_w1" {
  default = {
    "************" = "eyecue-iot-kinesis-fallback-lambda-role"
    "************" = "eyecue-iot-kinesis-fallback-lambda-role"
  }
}


# variable "bastion_s3_bucket_name" {
#   description = "S3 bucket name use"
#   default     = "data-dev-bastion-v2-ap-southeast-2"
# }

variable "bastion_us_e1_s3_bucket_name" {
  description = "S3 bucket name use"
  default     = "data-prod-bastion-v2-us-east-1"
}

variable "bastion_us_w2_s3_bucket_name" {
  description = "S3 bucket name use"
  default     = "data-prod-bastion-v2-us-west-2"
}

variable "bastion_ca_c1_s3_bucket_name" {
  description = "S3 bucket name use"
  default     = "data-prod-bastion-v2-ca-central-1"
}

variable "usw1_ami_id" {
  default = "ami-09749bb84533ed3d5"
}

variable "use1_ami_id" {
  default = "ami-03c7d01cf4dedc891"
}

variable "usw2_ami_id" {
  default = "ami-093467ec28ae4fe03"
}

variable "aps2_ami_id" {
  default = "ami-0578ad653d72d7de4"
}

variable "redshift_stream_role_name" {
  default = "redshift_stream_role"
}

#************ - MCD-AUS
#************ - MCD-NZD
#************ - POC
#************ ELJ-AUS
#************  POP
#************ STB NZL
#************ BKG-NZL
#************ ZMB-AUS

variable "cross_account_kinesis_roles_ap_se2" {
  type = list(object({
    account_id = string
    role_name  = string
  }))

  default = [
    { account_id = "************", role_name = "redshift_stream_aaccess_role" },
    { account_id = "************", role_name = "redshift_stream_aaccess_role" },
    { account_id = "************", role_name = "redshift_stream_aaccess_role" },
    { account_id = "************", role_name = "redshift_stream_aaccess_role" },
    { account_id = "************", role_name = "redshift_stream_aaccess_role" },
    { account_id = "************", role_name = "redshift_stream_aaccess_role" },
    { account_id = "************", role_name = "redshift_stream_access_role" },
    { account_id = "************", role_name = "redshift_stream_aaccess_role" },
    { account_id = "************", role_name = "redshift_stream_aaccess_role" }
  ]
}
# ************ TIM
# ************ MCD-CAN

variable "cross_account_kinesis_roles_ca_c1" {
  type = list(object({
    account_id = string
    role_name  = string
  }))

  default = [
    { account_id = "************", role_name = "redshift_stream_aaccess_role" },
    { account_id = "************", role_name = "redshift_stream_aaccess_role" }
  ]
}

variable "cross_account_roles" {
  type = list(string)
  default = ["arn:aws:iam::************:root",
    "arn:aws:iam::************:root",
    "arn:aws:iam::************:root"
  ]
}

#************ STB-USA
variable "cross_account_kinesis_roles_us_w2" {
  type = list(object({
    account_id = string
    role_name  = string
  }))
  default = [
    { account_id = "************", role_name = "redshift_stream_aaccess_role" },
    { account_id = "************", role_name = "redshift_stream_aaccess_role" }
  ]
}
variable "au_existing_security_group_id" {
  description = "The ID of the existing security group to modify"
  default     = "sg-0c998d69466f5910e"
}
variable "au_additional_security_group_id" {
  description = "The ID of the additional security group to allow traffic from"
  default     = "sg-0ee3e006d29776835"
}


variable "ca_c1_ssh_public_key_full_path" {
  description = "The full path to the SSH public key file"
  default     = "users/admin_public_keys/rusiru.bulathgamage"

}

variable "ap_se2_ssh_public_key_full_path" {
  description = "The full path to the SSH public key file"
  default     = "users/admin_public_keys/rusiru.bulathgamage"

}

variable "us_e1_ssh_public_key_full_path" {
  description = "The full path to the SSH public key file"
  default     = "users/admin_public_keys/rusiru.bulathgamage"

}
variable "us_ssh_public_key_full_path" {
  description = "The full path to the SSH public key file"
  default     = "users/admin_public_keys/rusiru.bulathgamage"

}

variable "iot_to_kinesis_failure_bucket_name" {
  default = "fm-data-eyecue-kinesis-failure"
}

variable "vpc_flow_logs_destination" {
  description = "Arn of the S3 bucket to store VPC flow logs."
  default     = "arn:aws:s3:::fingermark-vpc-logs"
}

# ===============================================
# CloudWatch Alarms
# ===============================================
variable "cw_alarms_sns_topic_arns_region_lookup" {
  description = <<-DOC
    Map of region names to SNS topic ARNs for CloudWatch alarm notifications. The SNS topic ARNs
    should have already been created in a centralised AWS account (infra #************) using the
    module `modules/cw_alarm_notifications_sns_topic`.
  DOC
  type        = map(string)
  default = {
    "ap-southeast-2" = "arn:aws:sns:ap-southeast-2:************:cloudwatch-alarms-org"
    "ca-central-1"   = "arn:aws:sns:ca-central-1:************:cloudwatch-alarms-org"
    "us-east-1"      = "arn:aws:sns:us-east-1:************:cloudwatch-alarms-org"
    "us-west-1"      = "arn:aws:sns:us-west-1:************:cloudwatch-alarms-org"
    "us-west-2"      = "arn:aws:sns:us-west-2:************:cloudwatch-alarms-org"
  }
}
