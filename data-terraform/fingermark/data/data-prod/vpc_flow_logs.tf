module "vpc_flow_logs_ap_se2" {
  source          = "git::*****************:fingermarkltd/fingermark-terraform.git//modules/vpc_flow_logs?ref=master"
  tags            = local.common_tags
  log_destination = var.vpc_flow_logs_destination
  providers = {
    aws = aws
  }
}

module "vpc_flow_logs_us_e1" {
  source          = "git::*****************:fingermarkltd/fingermark-terraform.git//modules/vpc_flow_logs?ref=master"
  tags            = local.common_tags
  log_destination = var.vpc_flow_logs_destination
  providers = {
    aws = aws.us_e1
  }
}

module "vpc_flow_logs_us_e2" {
  source          = "git::*****************:fingermarkltd/fingermark-terraform.git//modules/vpc_flow_logs?ref=master"
  tags            = local.common_tags
  log_destination = var.vpc_flow_logs_destination
  providers = {
    aws = aws.us_e2
  }
}

module "vpc_flow_logs_us_w1" {
  source          = "git::*****************:fingermarkltd/fingermark-terraform.git//modules/vpc_flow_logs?ref=master"
  tags            = local.common_tags
  log_destination = var.vpc_flow_logs_destination
  providers = {
    aws = aws.usw1
  }
}

module "vpc_flow_logs_us_w2" {
  source          = "git::*****************:fingermarkltd/fingermark-terraform.git//modules/vpc_flow_logs?ref=master"
  tags            = local.common_tags
  log_destination = var.vpc_flow_logs_destination
  providers = {
    aws = aws.us_w2
  }
}
