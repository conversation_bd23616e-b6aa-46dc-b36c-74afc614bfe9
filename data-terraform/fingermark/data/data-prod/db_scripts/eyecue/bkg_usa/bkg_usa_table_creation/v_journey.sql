CREATE
OR REPLACE VIEW "eyecue_bkg_usa"."v_journey" AS
SELECT
    "mv_journey"."meta_kinesis_datastream_arrival_datetime_utc" AS "meta_kinesis_datastream_arrival_datetime_utc",
    "mv_journey"."meta_redshift_refresh_datetime_utc" AS "meta_redshift_refresh_datetime_utc",
    "mv_journey"."partition_key" AS "partition_key",
    "mv_journey"."shard_id" AS "shard_id",
    "mv_journey"."sequence_number" AS "sequence_number",
    "mv_journey"."payload" AS "payload"
FROM
    "eyecue_bkg_usa"."mv_journey" AS "mv_journey"
WHERE
    (
        JSON_SIZE("mv_journey"."payload") < CAST(65000 AS INT4)
    )
    AND (
        "mv_journey"."payload"."event_type" = CAST('journey' AS VARCHAR)
    );