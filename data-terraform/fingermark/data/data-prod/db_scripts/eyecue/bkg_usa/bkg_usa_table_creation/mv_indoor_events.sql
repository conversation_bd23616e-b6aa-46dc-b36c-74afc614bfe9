CREATE MATERIALIZED VIEW eyecue_bkg_usa.mv_indoor_events AUTO REFRESH YES AS
SELECT
    approximate_arrival_timestamp meta_kinesis_datastream_arrival_datetime_utc,
    refresh_time meta_redshift_refresh_datetime_utc,
    partition_key,
    shard_id,
    sequence_number,
    json_parse(KINESIS_DATA) as payload
FROM
    stream_bkg_usa."ds-bkg-usa-eyecue-indoor-events"
where
    CAN_JSON_PARSE(kinesis_data);