CREATE TABLE eyecue_bkg_usa.dim_restaurant (
    dim_restaurant_id integer identity(1, 1) ENCODE az64,
    fingermark_id character varying(20) ENCODE lzo,
    restaurant_name character varying(255) ENCODE lzo,
    restaurant_id integer ENCODE az64,
    time_zone character varying(50) ENCODE lzo,
    address character varying(255) ENCODE lzo,
    suburb character varying(255) ENCODE lzo,
    city character varying(255) ENCODE lzo,
    state character varying(255) ENCODE lzo,
    post_code character varying(20) ENCODE lzo,
    country character varying(100) ENCODE lzo,
    latitude numeric(10, 7) ENCODE az64,
    longitude numeric(10, 7) ENCODE az64,
    num_order_lanes integer ENCODE az64,
    pf_blocks_lane boolean ENCODE raw,
    record_creation_date_utc timestamp without time zone NOT NULL ENCODE raw,
    record_expiry_date_utc timestamp without time zone ENCODE raw,
    current boolean NOT NULL ENCODE raw
) DISTSTYLE AUTO;