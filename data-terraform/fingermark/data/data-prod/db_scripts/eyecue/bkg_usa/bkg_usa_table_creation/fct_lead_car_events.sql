CREATE TABLE eyecue_bkg_usa.fct_lead_car_events (
    hash_id character varying(32) NOT NULL ENCODE lzo,
    meta_kinesis_datastream_arrival_datetime_utc timestamp with time zone ENCODE az64,
    fingermark_id character varying(255) <PERSON>NCOD<PERSON> bytedict
    distkey
,
        camera_id character varying(255) ENCODE bytedict,
        event_type character varying(100) ENCODE lzo,
        roi_type character varying(100) ENCODE bytedict,
        roi_event character varying(100) ENCODE lzo,
        roi_id character varying(255) ENCODE bytedict,
        event_time_at_utc timestamp with time zone ENCODE raw,
        event_time_at_local timestamp without time zone ENCODE az64,
        lead_car_reset_time_at_utc timestamp with time zone ENCODE az64,
        lead_car_reset_time_at_local timestamp without time zone ENCODE az64,
        lane_number character varying(50) ENCODE lzo,
        vehicle_count integer ENCODE az64,
        assets_info super,
        record_creation_time_utc timestamp with time zone ENCODE az64
) DISTSTYLE KEY
SORTKEY
    (event_time_at_utc, fingermark_id);