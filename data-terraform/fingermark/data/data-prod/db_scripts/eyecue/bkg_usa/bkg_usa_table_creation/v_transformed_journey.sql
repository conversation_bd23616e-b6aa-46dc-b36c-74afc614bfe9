CREATE OR REPLACE VIEW "eyecue_bkg_usa"."v_transformed_journey" AS WITH
    _journey_data AS (
        SELECT
            meta_kinesis_datastream_arrival_datetime_utc,
            meta_redshift_refresh_datetime_utc,
            partition_key,
            shard_id,
            sequence_number,
            payload,
            JSON_SERIALIZE(payload) AS serialized_payload,
            JSON_EXTRACT_PATH_TEXT(serialized_payload, 'vehicle_id') AS vehicle_id,
            JSON_EXTRACT_PATH_TEXT(serialized_payload, 'INGRESS') AS ingress_event_json,
            JSON_EXTRACT_PATH_TEXT(serialized_payload, 'START') AS start_event_json,
            JSON_EXTRACT_PATH_TEXT(serialized_payload, 'ORDER') AS order_event_json,
            JSON_EXTRACT_PATH_TEXT(serialized_payload, 'PAYMENT') AS payment_event_json,
            JSON_EXTRACT_PATH_TEXT(serialized_payload, 'PRESENTER') AS presenter_event_json,
            J<PERSON><PERSON>_EXTRACT_PATH_TEXT(serialized_payload, 'DELIVER') AS deliver_event_json,
            JSON_EXTRACT_PATH_TEXT(serialized_payload, 'PULL_FORWARD') AS pull_forward_event_json,
            JSON_EXTRACT_PATH_TEXT(serialized_payload, 'WAITING') AS waiting_event_json,
            JSON_EXTRACT_PATH_TEXT(serialized_payload, 'EGRESS') AS egress_event_json,
            JSON_EXTRACT_PATH_TEXT(serialized_payload, 'FINISH') AS finish_event_json,
            JSON_EXTRACT_PATH_TEXT(serialized_payload, 'LOST_ASSET') AS lost_asset_event_json,
            LEN(ingress_event_json) ingress_event_len,
            LEN(start_event_json) start_event_len,
            LEN(order_event_json) order_event_len,
            LEN(payment_event_json) payment_event_len,
            LEN(presenter_event_json) presenter_event_len,
            LEN(deliver_event_json) deliver_event_len,
            LEN(pull_forward_event_json) pull_forward_event_len,
            LEN(waiting_event_json) waiting_event_len,
            LEN(egress_event_json) egress_event_len,
            LEN(finish_event_json) finish_event_len,
            LEN(lost_asset_event_json) lost_asset_event_len,
            CASE
                WHEN ingress_event_len > 0 THEN JSON_PARSE(ingress_event_json)
                ELSE NULL
            END AS ingress_event,
            CASE
                WHEN start_event_len > 0 THEN JSON_PARSE(start_event_json)
                ELSE NULL
            END AS start_event,
            CASE
                WHEN order_event_len > 0 THEN JSON_PARSE(order_event_json)
                ELSE NULL
            END AS order_event,
            CASE
                WHEN payment_event_len > 0 THEN JSON_PARSE(payment_event_json)
                ELSE NULL
            END AS payment_event,
            CASE
                WHEN presenter_event_len > 0 THEN JSON_PARSE(presenter_event_json)
                ELSE NULL
            END AS presenter_event,
            CASE
                WHEN deliver_event_len > 0 THEN JSON_PARSE(deliver_event_json)
                ELSE NULL
            END AS deliver_event,
            CASE
                WHEN pull_forward_event_len > 0 THEN JSON_PARSE(pull_forward_event_json)
                ELSE NULL
            END AS pull_forward_event,
            CASE
                WHEN waiting_event_len > 0 THEN JSON_PARSE(waiting_event_json)
                ELSE NULL
            END AS waiting_event,
            CASE
                WHEN egress_event_len > 0 THEN JSON_PARSE(egress_event_json)
                ELSE NULL
            END AS egress_event,
            CASE
                WHEN lost_asset_event_len > 0 THEN JSON_PARSE(lost_asset_event_json)
                ELSE NULL
            END AS lost_asset_event,
            CASE
                WHEN finish_event_len > 0 THEN JSON_PARSE(finish_event_json)
                ELSE NULL
            END AS finish_event
        FROM eyecue_bkg_usa.v_journey
        WHERE 1 = 1
            AND meta_kinesis_datastream_arrival_datetime_utc > (GETDATE() - INTERVAL '7 days')
    ),
    _deduplicated AS (
        SELECT
            JSON_EXTRACT_PATH_TEXT(JSON_SERIALIZE(payload), 'vehicle_id') AS check_vehicle_id,
            MAX(meta_kinesis_datastream_arrival_datetime_utc) AS check_meta_kinesis_datastream_arrival_datetime_utc
        FROM eyecue_bkg_usa.v_journey
        WHERE 1 = 1
            AND meta_kinesis_datastream_arrival_datetime_utc > (GETDATE() - INTERVAL '7 days')
        GROUP BY check_vehicle_id
)
SELECT
    meta_kinesis_datastream_arrival_datetime_utc,
    meta_redshift_refresh_datetime_utc,
    partition_key,
    shard_id,
    sequence_number,
    payload,
    public.get_payload_variations(serialized_payload) AS payload_variations,
    vehicle_id,
    payload.site_id::TEXT AS fingermark_id,
    payload.ghost::BOOLEAN,
    payload.graph_history,
    payload.handheld_point_of_sale,
    -- tz offset
    CASE
        WHEN ingress_event_len > 0 THEN CONCAT('UTC', RIGHT(ingress_event.first_event::TEXT, 6))
        WHEN start_event_len > 0 THEN CONCAT('UTC', RIGHT(start_event.first_event::TEXT, 6))
        WHEN order_event_len > 0 THEN CONCAT('UTC', RIGHT(order_event.first_event::TEXT, 6))
        WHEN payment_event_len > 0 THEN CONCAT('UTC', RIGHT(payment_event.first_event::TEXT, 6))
        WHEN presenter_event_len > 0 THEN CONCAT('UTC', RIGHT(presenter_event.first_event::TEXT, 6))
        WHEN deliver_event_len > 0 THEN CONCAT('UTC', RIGHT(deliver_event.first_event::TEXT, 6))
        WHEN pull_forward_event_len > 0 THEN CONCAT('UTC', RIGHT(pull_forward_event.first_event::TEXT, 6))
        WHEN waiting_event_len > 0 THEN CONCAT('UTC', RIGHT(waiting_event.first_event::TEXT, 6))
        WHEN egress_event_len > 0 THEN CONCAT('UTC', RIGHT(egress_event.first_event::TEXT, 6))
        WHEN finish_event_len > 0 THEN CONCAT('UTC', RIGHT(finish_event.first_event::TEXT, 6))
        ELSE NULL
    END AS tz_offset,
    -- ingress
    CASE WHEN ingress_event_len > 0 THEN ingress_event.first_event::TEXT::TIMESTAMPTZ ELSE NULL END AS ingress_start_datetime_utc,
    CASE WHEN ingress_event_len > 0 THEN ingress_event.last_event::TEXT::TIMESTAMPTZ ELSE NULL END AS ingress_end_datetime_utc,
    CASE WHEN ingress_event_len > 0 THEN ingress_event.lane_number::INT ELSE NULL END AS ingress_lane_number,
    CASE WHEN ingress_event_len > 0 THEN ingress_event.roi_id::TEXT ELSE NULL END AS ingress_roi_id,
    CASE WHEN ingress_event_len > 0 THEN ingress_event.cars_count::INT ELSE NULL END AS ingress_cars_count,
    CASE WHEN ingress_event_len > 0 THEN ROUND(ingress_event.duration::FLOAT * 1000, 0)::INT ELSE NULL END AS ingress_stated_duration_ms,
    CASE WHEN ingress_event_len > 0 THEN ROUND(DATEDIFF(microsecond, ingress_start_datetime_utc::TIMESTAMP, ingress_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT = ingress_stated_duration_ms ELSE NULL END AS ingress_durations_match,
    -- start (sometimes from the start camera and sometimes from the start ROI)
    CASE WHEN start_event_len > 0 THEN start_event.first_event::TEXT::TIMESTAMPTZ ELSE NULL END AS start_start_datetime_utc,
    CASE WHEN start_event_len > 0 THEN start_event.last_event::TEXT::TIMESTAMPTZ ELSE NULL END AS start_end_datetime_utc,
    CASE WHEN start_event_len > 0 THEN start_event.lane_number::INT ELSE NULL END AS start_lane_number,
    CASE WHEN start_event_len > 0 THEN start_event.roi_id::TEXT ELSE NULL END AS start_roi_id,
    CASE WHEN start_event_len > 0 THEN start_event.cars_count::INT ELSE NULL END AS start_cars_count,
    CASE WHEN start_event_len > 0 THEN ROUND(start_event.duration::FLOAT * 1000, 0)::INT ELSE NULL END AS start_stated_duration_ms,
    CASE WHEN start_event_len > 0 THEN ROUND(DATEDIFF(microsecond, start_start_datetime_utc::TIMESTAMP, start_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT = start_stated_duration_ms ELSE NULL END AS start_durations_match,
    -- order 
    CASE WHEN order_event_len > 0 THEN order_event.first_event::TEXT::TIMESTAMPTZ ELSE NULL END AS order_start_datetime_utc,
    CASE WHEN order_event_len > 0 THEN order_event.last_event::TEXT::TIMESTAMPTZ ELSE NULL END AS order_end_datetime_utc,
    CASE WHEN order_event_len > 0 THEN order_event.lane_number::INT ELSE NULL END AS order_lane_number,
    CASE WHEN order_event_len > 0 THEN order_event.roi_id::TEXT ELSE NULL END AS order_roi_id,
    CASE WHEN order_event_len > 0 THEN order_event.cars_count::INT ELSE NULL END AS order_cars_count,
    CASE WHEN order_event_len > 0 THEN FLOOR(order_event.interaction_events_count/2)::INT ELSE NULL END AS order_hvi_count,
    CASE WHEN order_hvi_count > 0 THEN order_event.first_interaction::TEXT::TIMESTAMPTZ ELSE NULL END AS order_first_hvi_datetime_utc,
    CASE WHEN order_hvi_count > 0 THEN order_event.last_interaction::TEXT::TIMESTAMPTZ ELSE NULL END AS order_last_hvi_datetime_utc,
    CASE WHEN order_hvi_count > 0 THEN ROUND(DATEDIFF(microsecond, order_start_datetime_utc::TIMESTAMP, order_first_hvi_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT ELSE NULL END AS order_ms_to_first_hvi,
    CASE WHEN order_last_hvi_datetime_utc is NOT NULL THEN GREATEST(ROUND(DATEDIFF(microsecond, order_last_hvi_datetime_utc::TIMESTAMP, order_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT, 0) ELSE NULL END AS order_ms_from_last_hvi,
    CASE WHEN order_hvi_count > 0 THEN ROUND(DATEDIFF(microsecond, order_last_hvi_datetime_utc::TIMESTAMP, order_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT < 0 ELSE NULL END AS order_hvi_end_after_roi_end,
    CASE WHEN order_hvi_count > 0 THEN public.get_avg_hvi_duration_ms(JSON_SERIALIZE(order_event.interaction_events)) ELSE NULL END AS order_average_hvi_duration_ms,
    CASE WHEN order_event_len > 0 THEN ROUND(((order_event.duration::FLOAT) * 1000), 0)::INT ELSE NULL END AS order_stated_duration_ms,
    CASE WHEN order_event_len > 0 THEN ROUND(DATEDIFF(microsecond, order_start_datetime_utc::TIMESTAMP, order_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT = order_stated_duration_ms ELSE NULL END AS order_durations_match,
    -- payment
    CASE WHEN payment_event_len > 0 THEN payment_event.first_event::TEXT::TIMESTAMPTZ ELSE NULL END AS pmt_start_datetime_utc,
    CASE WHEN payment_event_len > 0 THEN payment_event.last_event::TEXT::TIMESTAMPTZ ELSE NULL END AS pmt_end_datetime_utc,
    CASE WHEN payment_event_len > 0 THEN payment_event.lane_number::INT ELSE NULL END AS pmt_lane_number,
    CASE WHEN payment_event_len > 0 THEN payment_event.roi_id::TEXT ELSE NULL END AS pmt_roi_id,
    CASE WHEN payment_event_len > 0 THEN payment_event.cars_count::INT ELSE NULL END AS pmt_cars_count,
    CASE WHEN payment_event_len > 0 THEN FLOOR(payment_event.interaction_events_count/2)::INT ELSE NULL END AS pmt_hvi_count,
    CASE WHEN pmt_hvi_count > 0 THEN payment_event.first_interaction::TEXT::TIMESTAMPTZ ELSE NULL END AS pmt_first_hvi_datetime_utc,
    CASE WHEN pmt_hvi_count > 0 THEN payment_event.last_interaction::TEXT::TIMESTAMPTZ ELSE NULL END AS pmt_last_hvi_datetime_utc,
    CASE WHEN pmt_hvi_count > 0 THEN ROUND(DATEDIFF(microsecond, pmt_start_datetime_utc::TIMESTAMP, pmt_first_hvi_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT ELSE NULL END AS pmt_ms_to_first_hvi,
    CASE WHEN pmt_last_hvi_datetime_utc is NOT NULL THEN GREATEST(ROUND(DATEDIFF(microsecond, pmt_last_hvi_datetime_utc::TIMESTAMP, pmt_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT, 0) ELSE NULL END AS pmt_ms_from_last_hvi,
    CASE WHEN pmt_hvi_count > 0 THEN ROUND(DATEDIFF(microsecond, pmt_last_hvi_datetime_utc::TIMESTAMP, pmt_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT < 0 ELSE NULL END AS pmt_hvi_end_after_roi_end,
    CASE WHEN pmt_hvi_count > 0 THEN public.get_avg_hvi_duration_ms(JSON_SERIALIZE(payment_event.interaction_events)) ELSE NULL END AS pmt_average_hvi_duration_ms,
    CASE WHEN payment_event_len > 0 THEN ROUND(payment_event.duration::FLOAT * 1000, 0)::INT ELSE NULL END AS pmt_stated_duration_ms,
    CASE WHEN payment_event_len > 0 THEN ROUND(DATEDIFF(microsecond, pmt_start_datetime_utc::TIMESTAMP, pmt_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT = pmt_stated_duration_ms ELSE NULL END AS pmt_durations_match,
    -- presenter
    CASE WHEN presenter_event_len > 0 THEN presenter_event.first_event::TEXT::TIMESTAMPTZ ELSE NULL END AS prsnt_start_datetime_utc,
    CASE WHEN presenter_event_len > 0 THEN presenter_event.last_event::TEXT::TIMESTAMPTZ ELSE NULL END AS prsnt_end_datetime_utc,
    CASE WHEN presenter_event_len > 0 THEN presenter_event.lane_number::INT ELSE NULL END AS prsnt_lane_number,
    CASE WHEN presenter_event_len > 0 THEN presenter_event.roi_id::TEXT ELSE NULL END AS prsnt_roi_id,
    CASE WHEN presenter_event_len > 0 THEN presenter_event.cars_count::INT ELSE NULL END AS prsnt_cars_count,
    CASE WHEN presenter_event_len > 0 THEN FLOOR(presenter_event.interaction_events_count/2)::INT ELSE NULL END AS prsnt_hvi_count,
    CASE WHEN prsnt_hvi_count > 0 THEN presenter_event.first_interaction::TEXT::TIMESTAMPTZ ELSE NULL END AS prsnt_first_hvi_datetime_utc,
    CASE WHEN prsnt_hvi_count > 0 THEN presenter_event.last_interaction::TEXT::TIMESTAMPTZ ELSE NULL END AS prsnt_last_hvi_datetime_utc,
    CASE WHEN prsnt_hvi_count > 0 THEN ROUND(DATEDIFF(microsecond, prsnt_start_datetime_utc::TIMESTAMP, prsnt_first_hvi_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT ELSE NULL END AS prsnt_ms_to_first_hvi,
    CASE WHEN prsnt_last_hvi_datetime_utc is NOT NULL THEN GREATEST(ROUND(DATEDIFF(microsecond, prsnt_last_hvi_datetime_utc::TIMESTAMP, prsnt_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT, 0) ELSE NULL END AS prsnt_ms_from_last_hvi,
    CASE WHEN prsnt_hvi_count > 0 THEN ROUND(DATEDIFF(microsecond, prsnt_last_hvi_datetime_utc::TIMESTAMP, prsnt_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT < 0 ELSE NULL END AS prsnt_hvi_end_after_roi_end,
    CASE WHEN prsnt_hvi_count > 0 THEN public.get_avg_hvi_duration_ms(JSON_SERIALIZE(presenter_event.interaction_events)) ELSE NULL END AS prsnt_average_hvi_duration_ms,
    CASE WHEN presenter_event_len > 0 THEN ROUND(presenter_event.duration::FLOAT * 1000, 0)::INT ELSE NULL END AS prsnt_stated_duration_ms,
    CASE WHEN presenter_event_len > 0 THEN ROUND(DATEDIFF(microsecond, prsnt_start_datetime_utc::TIMESTAMP, prsnt_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT = prsnt_stated_duration_ms ELSE NULL END AS prsnt_durations_match,
    -- deliver
    CASE WHEN deliver_event_len > 0 THEN deliver_event.first_event::TEXT::TIMESTAMPTZ ELSE NULL END AS dlr_start_datetime_utc,
    CASE WHEN deliver_event_len > 0 THEN deliver_event.last_event::TEXT::TIMESTAMPTZ ELSE NULL END AS dlr_end_datetime_utc,
    CASE WHEN deliver_event_len > 0 THEN deliver_event.lane_number::INT ELSE NULL END AS dlr_lane_number,
    CASE WHEN deliver_event_len > 0 THEN deliver_event.roi_id::TEXT ELSE NULL END AS dlr_roi_id,
    CASE WHEN deliver_event_len > 0 THEN deliver_event.cars_count::INT ELSE NULL END AS dlr_cars_count,
    CASE WHEN deliver_event_len > 0 THEN FLOOR(deliver_event.interaction_events_count/2)::INT ELSE NULL END AS dlr_hvi_count,
    CASE WHEN dlr_hvi_count > 0 THEN deliver_event.first_interaction::TEXT::TIMESTAMPTZ ELSE NULL END AS dlr_first_hvi_datetime_utc,
    CASE WHEN dlr_hvi_count > 0 THEN deliver_event.last_interaction::TEXT::TIMESTAMPTZ ELSE NULL END AS dlr_last_hvi_datetime_utc,
    CASE WHEN dlr_hvi_count > 0 THEN ROUND(DATEDIFF(microsecond, dlr_start_datetime_utc::TIMESTAMP, dlr_first_hvi_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT ELSE NULL END AS dlr_ms_to_first_hvi,
    CASE WHEN dlr_last_hvi_datetime_utc is NOT NULL THEN GREATEST(ROUND(DATEDIFF(microsecond, dlr_last_hvi_datetime_utc::TIMESTAMP, dlr_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT, 0) ELSE NULL END AS dlr_ms_from_last_hvi,
    CASE WHEN dlr_hvi_count > 0 THEN ROUND(DATEDIFF(microsecond, dlr_last_hvi_datetime_utc::TIMESTAMP, dlr_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT < 0 ELSE NULL END AS dlr_hvi_end_after_roi_end,
    CASE WHEN dlr_hvi_count > 0 THEN public.get_avg_hvi_duration_ms(JSON_SERIALIZE(deliver_event.interaction_events)) ELSE NULL END AS dlr_average_hvi_duration_ms,
    CASE WHEN deliver_event_len > 0 THEN ROUND(deliver_event.duration::FLOAT * 1000, 0)::INT ELSE NULL END AS dlr_stated_duration_ms,
    CASE WHEN deliver_event_len > 0 THEN ROUND(DATEDIFF(microsecond, dlr_start_datetime_utc::TIMESTAMP, dlr_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT = dlr_stated_duration_ms ELSE NULL END AS dlr_durations_match,
    -- pull forward
    CASE WHEN pull_forward_event_len > 0 THEN pull_forward_event.first_event::TEXT::TIMESTAMPTZ ELSE NULL END AS pf_start_datetime_utc,
    CASE WHEN pull_forward_event_len > 0 THEN pull_forward_event.last_event::TEXT::TIMESTAMPTZ ELSE NULL END AS pf_end_datetime_utc,
    CASE WHEN pull_forward_event_len > 0 THEN pull_forward_event.lane_number::INT ELSE NULL END AS pf_lane_number,
    CASE WHEN pull_forward_event_len > 0 THEN pull_forward_event.roi_id::TEXT ELSE NULL END AS pf_roi_id,
    CASE WHEN pull_forward_event_len > 0 THEN pull_forward_event.cars_count::INT ELSE NULL END AS pf_cars_count,
    CASE WHEN pull_forward_event_len > 0 THEN FLOOR(pull_forward_event.interaction_events_count/2)::INT ELSE NULL END AS pf_hvi_count,
    CASE WHEN pf_hvi_count > 0 THEN pull_forward_event.first_interaction::TEXT::TIMESTAMPTZ ELSE NULL END AS pf_first_hvi_datetime_utc,
    CASE WHEN pf_hvi_count > 0 THEN pull_forward_event.last_interaction::TEXT::TIMESTAMPTZ ELSE NULL END AS pf_last_hvi_datetime_utc,
    CASE WHEN pf_hvi_count > 0 THEN ROUND(DATEDIFF(microsecond, pf_start_datetime_utc::TIMESTAMP, pf_first_hvi_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT ELSE NULL END AS pf_ms_to_first_hvi,
    CASE WHEN pf_last_hvi_datetime_utc is NOT NULL THEN GREATEST(ROUND(DATEDIFF(microsecond, pf_last_hvi_datetime_utc::TIMESTAMP, pf_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT, 0) ELSE NULL END AS pf_ms_from_last_hvi,
    CASE WHEN pf_hvi_count > 0 THEN ROUND(DATEDIFF(microsecond, pf_last_hvi_datetime_utc::TIMESTAMP, pf_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT < 0 ELSE NULL END AS pf_hvi_end_after_roi_end,
    CASE WHEN pf_hvi_count > 0 THEN public.get_avg_hvi_duration_ms(JSON_SERIALIZE(pull_forward_event.interaction_events)) ELSE NULL END AS pf_average_hvi_duration_ms,
    CASE WHEN pull_forward_event_len > 0 THEN ROUND(((pull_forward_event.duration::FLOAT) * 1000), 0)::INT ELSE NULL END AS pf_stated_duration_ms,
    CASE WHEN pull_forward_event_len > 0 THEN ROUND(DATEDIFF(microsecond, pf_start_datetime_utc::TIMESTAMP, pf_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT = pf_stated_duration_ms ELSE NULL END AS pf_durations_match,
    -- waiting Bay
    CASE WHEN waiting_event_len > 0 THEN waiting_event.first_event::TEXT::TIMESTAMPTZ ELSE NULL END AS wb_start_datetime_utc,
    CASE WHEN waiting_event_len > 0 THEN waiting_event.last_event::TEXT::TIMESTAMPTZ ELSE NULL END AS wb_end_datetime_utc,
    CASE WHEN waiting_event_len > 0 THEN waiting_event.lane_number::INT ELSE NULL END AS wb_lane_number,
    CASE WHEN waiting_event_len > 0 THEN waiting_event.roi_id::TEXT ELSE NULL END AS wb_roi_id,
    CASE WHEN waiting_event_len > 0 THEN waiting_event.cars_count::INT ELSE NULL END AS wb_cars_count,
    CASE WHEN waiting_event_len > 0 THEN FLOOR(waiting_event.interaction_events_count/2)::INT ELSE NULL END AS wb_hvi_count,
    CASE WHEN wb_hvi_count > 0 THEN waiting_event.first_interaction::TEXT::TIMESTAMPTZ ELSE NULL END AS wb_first_hvi_datetime_utc,
    CASE WHEN wb_hvi_count > 0 THEN waiting_event.last_interaction::TEXT::TIMESTAMPTZ ELSE NULL END AS wb_last_hvi_datetime_utc,
    CASE WHEN wb_hvi_count > 0 THEN ROUND(DATEDIFF(microsecond, wb_start_datetime_utc::TIMESTAMP, wb_first_hvi_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT ELSE NULL END AS wb_ms_to_first_hvi,
    CASE WHEN wb_last_hvi_datetime_utc is NOT NULL THEN GREATEST(ROUND(DATEDIFF(microsecond, wb_last_hvi_datetime_utc::TIMESTAMP, wb_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT, 0) ELSE NULL END AS wb_ms_from_last_hvi,
    CASE WHEN wb_hvi_count > 0 THEN ROUND(DATEDIFF(microsecond, wb_last_hvi_datetime_utc::TIMESTAMP, wb_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT < 0 ELSE NULL END AS wb_hvi_end_after_roi_end,
    CASE WHEN wb_hvi_count > 0 THEN public.get_avg_hvi_duration_ms(JSON_SERIALIZE(waiting_event.interaction_events)) ELSE NULL END AS wb_average_hvi_duration_ms,
    CASE WHEN waiting_event_len > 0 THEN ROUND(waiting_event.duration::FLOAT * 1000, 0)::INT ELSE NULL END AS wb_stated_duration_ms,
    CASE WHEN waiting_event_len > 0 THEN ROUND(DATEDIFF(microsecond, wb_start_datetime_utc::TIMESTAMP, wb_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT = wb_stated_duration_ms ELSE NULL END AS wb_durations_match,
    -- egress
    CASE WHEN egress_event_len > 0 THEN egress_event.first_event::TEXT::TIMESTAMPTZ ELSE NULL END AS egress_start_datetime_utc,
    CASE WHEN egress_event_len > 0 THEN egress_event.last_event::TEXT::TIMESTAMPTZ ELSE NULL END AS egress_end_datetime_utc,
    CASE WHEN egress_event_len > 0 THEN egress_event.lane_number::INT ELSE NULL END AS egress_lane_number,
    CASE WHEN egress_event_len > 0 THEN egress_event.roi_id::TEXT ELSE NULL END AS egress_roi_id,
    CASE WHEN egress_event_len > 0 THEN egress_event.cars_count::INT ELSE NULL END AS egress_cars_count,
    CASE WHEN egress_event_len > 0 THEN ROUND(egress_event.duration::FLOAT * 1000, 0)::INT ELSE NULL END AS egress_stated_duration_ms,
    CASE WHEN egress_event_len > 0 THEN ROUND(DATEDIFF(microsecond, egress_start_datetime_utc::TIMESTAMP, egress_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT = egress_stated_duration_ms ELSE NULL END AS egress_durations_match,
    -- finish (could come from a finish camera or finish ROI)
    CASE WHEN finish_event_len > 0 THEN finish_event.first_event::TEXT::TIMESTAMPTZ ELSE NULL END AS fin_start_datetime_utc,
    CASE WHEN finish_event_len > 0 THEN finish_event.last_event::TEXT::TIMESTAMPTZ ELSE NULL END AS fin_end_datetime_utc,
    CASE WHEN finish_event_len > 0 THEN finish_event.lane_number::INT ELSE NULL END AS fin_lane_number,
    CASE WHEN finish_event_len > 0 THEN finish_event.roi_id::TEXT ELSE NULL END AS fin_roi_id,
    CASE WHEN finish_event_len > 0 THEN finish_event.cars_count::INT ELSE NULL END AS fin_cars_count,
    CASE WHEN finish_event_len > 0 THEN ROUND(finish_event.duration::FLOAT * 1000, 0)::INT ELSE NULL END AS fin_stated_duration_ms,
    CASE WHEN finish_event_len > 0 THEN ROUND(DATEDIFF(microsecond, fin_start_datetime_utc::TIMESTAMP, fin_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT = fin_stated_duration_ms ELSE NULL END AS fin_durations_match,
    -- as wb end time is not the true wb end time we use the finish end time for this calculation
    CASE WHEN waiting_event_len > 0 THEN ROUND(DATEDIFF(microsecond, wb_last_hvi_datetime_utc::TIMESTAMP, fin_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT ELSE NULL END AS wb_ms_from_last_hvi_to_finish,
    -- lost_asset
    CASE WHEN lost_asset_event_len > 0 THEN lost_asset_event.last_event::TEXT::TIMESTAMPTZ ELSE NULL END AS lost_asset_event_datetime_utc,
    --queue times
    CASE WHEN start_event_len > 0 THEN GREATEST(ROUND(DATEDIFF(microsecond, ingress_end_datetime_utc::TIMESTAMP, start_start_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT, 0) ELSE 0 END AS queue_pre_start_duration_ms,
    CASE 
        WHEN order_event_len > 0 THEN 
            CASE
                WHEN start_end_datetime_utc IS NOT NULL THEN GREATEST(ROUND(DATEDIFF(microsecond, start_end_datetime_utc::TIMESTAMP, order_start_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT, 0) 
                ELSE GREATEST(ROUND(DATEDIFF(microsecond, ingress_end_datetime_utc::TIMESTAMP, order_start_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT, 0) 
            END
        ELSE 0 
    END AS queue_pre_order_duration_ms,
    CASE
        WHEN payment_event_len > 0 THEN
            CASE
                WHEN order_end_datetime_utc IS NOT NULL THEN GREATEST(ROUND(DATEDIFF(microsecond, order_end_datetime_utc::TIMESTAMP, pmt_start_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT, 0)
                WHEN start_end_datetime_utc IS NOT NULL THEN GREATEST(ROUND(DATEDIFF(microsecond, start_end_datetime_utc::TIMESTAMP, pmt_start_datetime_utc::TIMESTAMP)::FLOAT / 1000)::INT, 0)
                ELSE GREATEST(ROUND(DATEDIFF(microsecond, ingress_end_datetime_utc::TIMESTAMP, pmt_start_datetime_utc::TIMESTAMP)::FLOAT / 1000)::INT, 0)
            END
        ELSE 0
    END AS queue_pre_pmt_duration_ms,
    CASE
        WHEN presenter_event_len > 0 THEN
            CASE
                WHEN pmt_end_datetime_utc IS NOT NULL THEN GREATEST(ROUND(DATEDIFF(microsecond, pmt_end_datetime_utc::TIMESTAMP, prsnt_start_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT, 0)
                WHEN order_end_datetime_utc IS NOT NULL THEN GREATEST(ROUND(DATEDIFF(microsecond, order_end_datetime_utc::TIMESTAMP, prsnt_start_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT, 0)
                WHEN start_end_datetime_utc IS NOT NULL THEN GREATEST(ROUND(DATEDIFF(microsecond, start_end_datetime_utc::TIMESTAMP, prsnt_start_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT, 0)
                ELSE GREATEST(ROUND(DATEDIFF(microsecond, ingress_end_datetime_utc::TIMESTAMP, prsnt_start_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT, 0)
            END
        ELSE 0
    END AS queue_pre_prsnt_duration_ms,
    CASE
        WHEN deliver_event_len > 0 THEN
            CASE
                WHEN prsnt_end_datetime_utc IS NOT NULL THEN GREATEST(ROUND(DATEDIFF(microsecond, prsnt_end_datetime_utc::TIMESTAMP, dlr_start_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT, 0)
                WHEN pmt_end_datetime_utc IS NOT NULL THEN GREATEST(ROUND(DATEDIFF(microsecond, pmt_end_datetime_utc::TIMESTAMP, dlr_start_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT, 0)
                WHEN order_end_datetime_utc IS NOT NULL THEN GREATEST(ROUND(DATEDIFF(microsecond, order_end_datetime_utc::TIMESTAMP, dlr_start_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT, 0)
                WHEN start_end_datetime_utc IS NOT NULL THEN GREATEST(ROUND(DATEDIFF(microsecond, start_end_datetime_utc::TIMESTAMP, dlr_start_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT, 0)
                ELSE GREATEST(ROUND(DATEDIFF(microsecond, ingress_end_datetime_utc::TIMESTAMP, dlr_start_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT, 0)
            END
        ELSE 0
    END AS queue_pre_dlr_duration_ms,
    CASE
        WHEN pull_forward_event_len > 0 THEN
            CASE
                WHEN dlr_end_datetime_utc IS NOT NULL THEN GREATEST(ROUND(DATEDIFF(microsecond, dlr_end_datetime_utc::TIMESTAMP, pf_start_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT, 0)
                WHEN prsnt_end_datetime_utc IS NOT NULL THEN GREATEST(ROUND(DATEDIFF(microsecond, prsnt_end_datetime_utc::TIMESTAMP, pf_start_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT, 0)
                WHEN pmt_end_datetime_utc IS NOT NULL THEN GREATEST(ROUND(DATEDIFF(microsecond, pmt_end_datetime_utc::TIMESTAMP, pf_start_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT, 0)
                WHEN order_end_datetime_utc IS NOT NULL THEN GREATEST(ROUND(DATEDIFF(microsecond, order_end_datetime_utc::TIMESTAMP, pf_start_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT, 0)
                WHEN start_end_datetime_utc IS NOT NULL THEN GREATEST(ROUND(DATEDIFF(microsecond, start_end_datetime_utc::TIMESTAMP, pf_start_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT, 0)
                ELSE GREATEST(ROUND(DATEDIFF(microsecond, ingress_end_datetime_utc::TIMESTAMP, pf_start_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT, 0)
            END
        ELSE 0
    END AS queue_pre_pf_duration_ms,
    CASE
        WHEN waiting_event_len > 0 THEN
            CASE
                WHEN pf_end_datetime_utc IS NOT NULL THEN GREATEST(ROUND(DATEDIFF(microsecond, pf_end_datetime_utc::TIMESTAMP, wb_start_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT, 0)
                WHEN dlr_end_datetime_utc IS NOT NULL THEN GREATEST(ROUND(DATEDIFF(microsecond, dlr_end_datetime_utc::TIMESTAMP, wb_start_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT, 0)
                WHEN prsnt_end_datetime_utc IS NOT NULL THEN GREATEST(ROUND(DATEDIFF(microsecond, prsnt_end_datetime_utc::TIMESTAMP, wb_start_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT, 0)
                WHEN pmt_end_datetime_utc IS NOT NULL THEN GREATEST(ROUND(DATEDIFF(microsecond, pmt_end_datetime_utc::TIMESTAMP, wb_start_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT, 0)
                WHEN order_end_datetime_utc IS NOT NULL THEN GREATEST(ROUND(DATEDIFF(microsecond, order_end_datetime_utc::TIMESTAMP, wb_start_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT, 0)
                WHEN start_end_datetime_utc IS NOT NULL THEN GREATEST(ROUND(DATEDIFF(microsecond, start_end_datetime_utc::TIMESTAMP, wb_start_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT, 0)
                ELSE GREATEST(ROUND(DATEDIFF(microsecond, ingress_end_datetime_utc::TIMESTAMP, wb_start_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT, 0)
            END
        ELSE 0
    END AS queue_pre_wb_duration_ms,
    CASE
        WHEN egress_event_len > 0 THEN
            CASE
                WHEN wb_end_datetime_utc IS NOT NULL THEN GREATEST(ROUND(DATEDIFF(microsecond, wb_end_datetime_utc::TIMESTAMP, egress_start_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT, 0)
                WHEN pf_end_datetime_utc IS NOT NULL THEN GREATEST(ROUND(DATEDIFF(microsecond, pf_end_datetime_utc::TIMESTAMP, egress_start_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT, 0)
                WHEN dlr_end_datetime_utc IS NOT NULL THEN GREATEST(ROUND(DATEDIFF(microsecond, dlr_end_datetime_utc::TIMESTAMP, egress_start_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT, 0)
                WHEN prsnt_end_datetime_utc IS NOT NULL THEN GREATEST(ROUND(DATEDIFF(microsecond, prsnt_end_datetime_utc::TIMESTAMP, egress_start_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT, 0)
                WHEN pmt_end_datetime_utc IS NOT NULL THEN GREATEST(ROUND(DATEDIFF(microsecond, pmt_end_datetime_utc::TIMESTAMP, egress_start_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT, 0)
                WHEN order_end_datetime_utc IS NOT NULL THEN GREATEST(ROUND(DATEDIFF(microsecond, order_end_datetime_utc::TIMESTAMP, egress_start_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT, 0)
                WHEN start_end_datetime_utc IS NOT NULL THEN GREATEST(ROUND(DATEDIFF(microsecond, start_end_datetime_utc::TIMESTAMP, egress_start_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT, 0)
                ELSE GREATEST(ROUND(DATEDIFF(microsecond, ingress_end_datetime_utc::TIMESTAMP, egress_start_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT, 0)
            END
        ELSE 0
    END AS queue_pre_egress_duration_ms,
    CASE
        WHEN finish_event_len > 0 THEN
            CASE
                WHEN egress_end_datetime_utc IS NOT NULL THEN GREATEST(ROUND(DATEDIFF(microsecond, egress_end_datetime_utc::TIMESTAMP, fin_start_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT, 0)
                WHEN wb_end_datetime_utc IS NOT NULL THEN GREATEST(ROUND(DATEDIFF(microsecond, wb_end_datetime_utc::TIMESTAMP, fin_start_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT, 0)
                WHEN pf_end_datetime_utc IS NOT NULL THEN GREATEST(ROUND(DATEDIFF(microsecond, pf_end_datetime_utc::TIMESTAMP, fin_start_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT, 0)
                WHEN dlr_end_datetime_utc IS NOT NULL THEN GREATEST(ROUND(DATEDIFF(microsecond, dlr_end_datetime_utc::TIMESTAMP, fin_start_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT, 0)
                WHEN prsnt_end_datetime_utc IS NOT NULL THEN GREATEST(ROUND(DATEDIFF(microsecond, prsnt_end_datetime_utc::TIMESTAMP, fin_start_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT, 0)
                WHEN pmt_end_datetime_utc IS NOT NULL THEN GREATEST(ROUND(DATEDIFF(microsecond, pmt_end_datetime_utc::TIMESTAMP, fin_start_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT, 0)
                WHEN order_end_datetime_utc IS NOT NULL THEN GREATEST(ROUND(DATEDIFF(microsecond, order_end_datetime_utc::TIMESTAMP, fin_start_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT, 0)
                WHEN start_end_datetime_utc IS NOT NULL THEN GREATEST(ROUND(DATEDIFF(microsecond, start_end_datetime_utc::TIMESTAMP, fin_start_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT, 0)
                ELSE GREATEST(ROUND(DATEDIFF(microsecond, ingress_end_datetime_utc::TIMESTAMP, fin_start_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT, 0)
            END
        ELSE 0
    END AS queue_pre_fin_duration_ms,
    --total_ms_observed
    CASE
        WHEN lost_asset_event_datetime_utc IS NULL THEN
            CASE
                WHEN ingress_start_datetime_utc IS NOT NULL THEN
                    GREATEST(
                        ROUND(DATEDIFF(microsecond, ingress_start_datetime_utc::TIMESTAMP, fin_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, ingress_start_datetime_utc::TIMESTAMP, egress_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, ingress_start_datetime_utc::TIMESTAMP, wb_last_hvi_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, ingress_start_datetime_utc::TIMESTAMP, pf_last_hvi_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, ingress_start_datetime_utc::TIMESTAMP, pf_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, ingress_start_datetime_utc::TIMESTAMP, dlr_last_hvi_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, ingress_start_datetime_utc::TIMESTAMP, dlr_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, ingress_start_datetime_utc::TIMESTAMP, prsnt_last_hvi_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, ingress_start_datetime_utc::TIMESTAMP, prsnt_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, ingress_start_datetime_utc::TIMESTAMP, pmt_last_hvi_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, ingress_start_datetime_utc::TIMESTAMP, pmt_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, ingress_start_datetime_utc::TIMESTAMP, order_last_hvi_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, ingress_start_datetime_utc::TIMESTAMP, order_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, ingress_start_datetime_utc::TIMESTAMP, start_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ingress_stated_duration_ms
                    )
                WHEN start_start_datetime_utc IS NOT NULL THEN
                    GREATEST(
                        ROUND(DATEDIFF(microsecond, start_start_datetime_utc::TIMESTAMP, fin_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, start_start_datetime_utc::TIMESTAMP, egress_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, start_start_datetime_utc::TIMESTAMP, wb_last_hvi_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, start_start_datetime_utc::TIMESTAMP, pf_last_hvi_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, start_start_datetime_utc::TIMESTAMP, pf_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, start_start_datetime_utc::TIMESTAMP, dlr_last_hvi_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, start_start_datetime_utc::TIMESTAMP, dlr_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, start_start_datetime_utc::TIMESTAMP, prsnt_last_hvi_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, start_start_datetime_utc::TIMESTAMP, prsnt_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, start_start_datetime_utc::TIMESTAMP, pmt_last_hvi_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, start_start_datetime_utc::TIMESTAMP, pmt_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, start_start_datetime_utc::TIMESTAMP, order_last_hvi_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, start_start_datetime_utc::TIMESTAMP, order_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        start_stated_duration_ms
                    )
                WHEN order_start_datetime_utc IS NOT NULL THEN
                    GREATEST(
                        ROUND(DATEDIFF(microsecond, order_start_datetime_utc::TIMESTAMP, fin_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, order_start_datetime_utc::TIMESTAMP, egress_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, order_start_datetime_utc::TIMESTAMP, wb_last_hvi_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, order_start_datetime_utc::TIMESTAMP, pf_last_hvi_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, order_start_datetime_utc::TIMESTAMP, pf_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, order_start_datetime_utc::TIMESTAMP, dlr_last_hvi_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, order_start_datetime_utc::TIMESTAMP, dlr_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, order_start_datetime_utc::TIMESTAMP, prsnt_last_hvi_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, order_start_datetime_utc::TIMESTAMP, prsnt_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, order_start_datetime_utc::TIMESTAMP, pmt_last_hvi_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, order_start_datetime_utc::TIMESTAMP, pmt_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        order_stated_duration_ms
                    )
                WHEN pmt_start_datetime_utc IS NOT NULL THEN
                    GREATEST(
                        ROUND(DATEDIFF(microsecond, pmt_start_datetime_utc::TIMESTAMP, fin_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, pmt_start_datetime_utc::TIMESTAMP, egress_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, pmt_start_datetime_utc::TIMESTAMP, wb_last_hvi_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, pmt_start_datetime_utc::TIMESTAMP, pf_last_hvi_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, pmt_start_datetime_utc::TIMESTAMP, pf_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, pmt_start_datetime_utc::TIMESTAMP, dlr_last_hvi_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, pmt_start_datetime_utc::TIMESTAMP, dlr_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, pmt_start_datetime_utc::TIMESTAMP, prsnt_last_hvi_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, pmt_start_datetime_utc::TIMESTAMP, prsnt_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, pmt_start_datetime_utc::TIMESTAMP, pmt_last_hvi_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, pmt_start_datetime_utc::TIMESTAMP, pmt_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                    )
                WHEN prsnt_start_datetime_utc IS NOT NULL THEN
                    GREATEST(
                        ROUND(DATEDIFF(microsecond, pmt_start_datetime_utc::TIMESTAMP, fin_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, pmt_start_datetime_utc::TIMESTAMP, egress_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, pmt_start_datetime_utc::TIMESTAMP, wb_last_hvi_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, pmt_start_datetime_utc::TIMESTAMP, pf_last_hvi_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, pmt_start_datetime_utc::TIMESTAMP, pf_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, pmt_start_datetime_utc::TIMESTAMP, dlr_last_hvi_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, pmt_start_datetime_utc::TIMESTAMP, dlr_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, pmt_start_datetime_utc::TIMESTAMP, prsnt_last_hvi_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, pmt_start_datetime_utc::TIMESTAMP, prsnt_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                    )
                WHEN dlr_start_datetime_utc IS NOT NULL THEN
                    GREATEST(
                        ROUND(DATEDIFF(microsecond, dlr_start_datetime_utc::TIMESTAMP, fin_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, dlr_start_datetime_utc::TIMESTAMP, egress_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, dlr_start_datetime_utc::TIMESTAMP, wb_last_hvi_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, dlr_start_datetime_utc::TIMESTAMP, pf_last_hvi_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, dlr_start_datetime_utc::TIMESTAMP, pf_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, dlr_start_datetime_utc::TIMESTAMP, dlr_last_hvi_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, dlr_start_datetime_utc::TIMESTAMP, dlr_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                )
                WHEN pf_start_datetime_utc IS NOT NULL THEN
                    GREATEST(
                        ROUND(DATEDIFF(microsecond, pf_start_datetime_utc::TIMESTAMP, fin_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, pf_start_datetime_utc::TIMESTAMP, egress_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, pf_start_datetime_utc::TIMESTAMP, wb_last_hvi_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, pf_start_datetime_utc::TIMESTAMP, pf_last_hvi_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, pf_start_datetime_utc::TIMESTAMP, pf_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                    )
                WHEN wb_start_datetime_utc IS NOT NULL THEN
                    GREATEST(
                        ROUND(DATEDIFF(microsecond, wb_start_datetime_utc::TIMESTAMP, fin_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, wb_start_datetime_utc::TIMESTAMP, egress_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, wb_start_datetime_utc::TIMESTAMP, wb_last_hvi_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                    )
                WHEN egress_start_datetime_utc IS NOT NULL THEN
                    GREATEST(
                        ROUND(DATEDIFF(microsecond, wb_start_datetime_utc::TIMESTAMP, fin_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, wb_start_datetime_utc::TIMESTAMP, egress_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                    )
                WHEN fin_start_datetime_utc IS NOT NULL THEN ROUND(DATEDIFF(microsecond, wb_start_datetime_utc::TIMESTAMP, fin_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                ELSE NULL
            END
        ELSE
            CASE
                WHEN ingress_start_datetime_utc IS NOT NULL THEN ROUND(DATEDIFF(microsecond, ingress_start_datetime_utc::TIMESTAMP, lost_asset_event_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                WHEN start_start_datetime_utc IS NOT NULL THEN ROUND(DATEDIFF(microsecond, start_start_datetime_utc::TIMESTAMP, lost_asset_event_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                WHEN order_start_datetime_utc IS NOT NULL THEN ROUND(DATEDIFF(microsecond, order_start_datetime_utc::TIMESTAMP, lost_asset_event_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                WHEN pmt_start_datetime_utc IS NOT NULL THEN ROUND(DATEDIFF(microsecond, pmt_start_datetime_utc::TIMESTAMP, lost_asset_event_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                WHEN prsnt_start_datetime_utc IS NOT NULL THEN ROUND(DATEDIFF(microsecond, prsnt_start_datetime_utc::TIMESTAMP, lost_asset_event_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                WHEN dlr_start_datetime_utc IS NOT NULL THEN ROUND(DATEDIFF(microsecond, dlr_start_datetime_utc::TIMESTAMP, lost_asset_event_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                WHEN pf_start_datetime_utc IS NOT NULL THEN ROUND(DATEDIFF(microsecond, pf_start_datetime_utc::TIMESTAMP, lost_asset_event_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                WHEN wb_start_datetime_utc IS NOT NULL THEN ROUND(DATEDIFF(microsecond, wb_start_datetime_utc::TIMESTAMP, lost_asset_event_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                WHEN egress_start_datetime_utc IS NOT NULL THEN ROUND(DATEDIFF(microsecond, egress_start_datetime_utc::TIMESTAMP, lost_asset_event_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                WHEN fin_start_datetime_utc IS NOT NULL THEN ROUND(DATEDIFF(microsecond, fin_start_datetime_utc::TIMESTAMP, lost_asset_event_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                ELSE NULL
            END
    END AS total_ms_observed,
    -- order end to last deliver end
    CASE
        WHEN order_last_hvi_datetime_utc IS NOT NULL THEN
            CASE
                WHEN wb_last_hvi_datetime_utc IS NOT NULL THEN ROUND(DATEDIFF(microsecond, order_last_hvi_datetime_utc::TIMESTAMP, wb_last_hvi_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                WHEN wb_stated_duration_ms > 60000 AND wb_last_hvi_datetime_utc IS NULL THEN ROUND(DATEDIFF(microsecond, order_last_hvi_datetime_utc::TIMESTAMP, wb_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                WHEN pf_last_hvi_datetime_utc IS NOT NULL THEN
                    LEAST(
                        ROUND(DATEDIFF(microsecond, order_last_hvi_datetime_utc::TIMESTAMP, pf_last_hvi_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, order_last_hvi_datetime_utc::TIMESTAMP, pf_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                    )
                WHEN pf_stated_duration_ms > 30000 AND pf_last_hvi_datetime_utc IS NULL THEN ROUND(DATEDIFF(microsecond, order_last_hvi_datetime_utc::TIMESTAMP, pf_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                WHEN dlr_hvi_count > 0 THEN
                    LEAST(
                        ROUND(DATEDIFF(microsecond, order_last_hvi_datetime_utc::TIMESTAMP, dlr_last_hvi_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, order_last_hvi_datetime_utc::TIMESTAMP, dlr_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                    )
                WHEN dlr_end_datetime_utc IS NOT NULL THEN ROUND(DATEDIFF(microsecond, order_last_hvi_datetime_utc::TIMESTAMP, dlr_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                WHEN prsnt_hvi_count > 0 THEN
                    LEAST(
                        ROUND(DATEDIFF(microsecond, order_last_hvi_datetime_utc::TIMESTAMP, prsnt_last_hvi_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, order_last_hvi_datetime_utc::TIMESTAMP, prsnt_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                    )
                WHEN prsnt_end_datetime_utc IS NOT NULL THEN ROUND(DATEDIFF(microsecond, order_last_hvi_datetime_utc::TIMESTAMP, prsnt_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                WHEN pmt_hvi_count > 0 THEN
                    LEAST(
                        ROUND(DATEDIFF(microsecond, order_last_hvi_datetime_utc::TIMESTAMP, dlr_last_hvi_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, order_last_hvi_datetime_utc::TIMESTAMP, pmt_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                    )
                WHEN pmt_end_datetime_utc IS NOT NULL THEN ROUND(DATEDIFF(microsecond, order_last_hvi_datetime_utc::TIMESTAMP, pmt_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
            END
        ELSE
            CASE 
                WHEN wb_last_hvi_datetime_utc IS NOT NULL THEN ROUND(DATEDIFF(microsecond, order_end_datetime_utc::TIMESTAMP, wb_last_hvi_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                WHEN wb_stated_duration_ms > 60000 AND wb_last_hvi_datetime_utc IS NULL THEN ROUND(DATEDIFF(microsecond, order_end_datetime_utc::TIMESTAMP, wb_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                WHEN pf_last_hvi_datetime_utc IS NOT NULL THEN
                    LEAST(
                        ROUND(DATEDIFF(microsecond, order_end_datetime_utc::TIMESTAMP, pf_last_hvi_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, order_end_datetime_utc::TIMESTAMP, pf_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                    )
                WHEN pf_stated_duration_ms > 30000 AND pf_last_hvi_datetime_utc IS NULL THEN ROUND(DATEDIFF(microsecond, order_end_datetime_utc::TIMESTAMP, pf_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                WHEN dlr_hvi_count > 0 THEN
                    LEAST(
                        ROUND(DATEDIFF(microsecond, order_end_datetime_utc::TIMESTAMP, dlr_last_hvi_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, order_end_datetime_utc::TIMESTAMP, dlr_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                    )
                WHEN dlr_end_datetime_utc IS NOT NULL THEN ROUND(DATEDIFF(microsecond, order_end_datetime_utc::TIMESTAMP, dlr_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                WHEN prsnt_hvi_count > 0 THEN
                    LEAST(
                        ROUND(DATEDIFF(microsecond, order_end_datetime_utc::TIMESTAMP, prsnt_last_hvi_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, order_end_datetime_utc::TIMESTAMP, prsnt_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                    )
                WHEN prsnt_end_datetime_utc IS NOT NULL THEN ROUND(DATEDIFF(microsecond, order_end_datetime_utc::TIMESTAMP, dlr_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                WHEN pmt_hvi_count > 0 THEN
                    LEAST(
                        ROUND(DATEDIFF(microsecond, order_end_datetime_utc::TIMESTAMP, dlr_last_hvi_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, order_end_datetime_utc::TIMESTAMP, pmt_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                    )
                WHEN pmt_end_datetime_utc IS NOT NULL THEN ROUND(DATEDIFF(microsecond, order_end_datetime_utc::TIMESTAMP, pmt_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT 
                ELSE NULL
            END
    END AS oelde_ms,
    -- order start to last deliver end
    CASE
        WHEN order_first_hvi_datetime_utc IS NOT NULL THEN
            CASE 
                WHEN wb_last_hvi_datetime_utc IS NOT NULL THEN ROUND(DATEDIFF(microsecond, order_first_hvi_datetime_utc::TIMESTAMP, wb_last_hvi_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                WHEN wb_stated_duration_ms > 60000 AND wb_last_hvi_datetime_utc IS NULL THEN ROUND(DATEDIFF(microsecond, order_first_hvi_datetime_utc::TIMESTAMP, wb_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                WHEN pf_last_hvi_datetime_utc IS NOT NULL THEN
                    LEAST(
                        ROUND(DATEDIFF(microsecond, order_first_hvi_datetime_utc::TIMESTAMP, pf_last_hvi_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, order_first_hvi_datetime_utc::TIMESTAMP, pf_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                    )
                WHEN pf_stated_duration_ms > 30000 AND pf_last_hvi_datetime_utc IS NULL THEN ROUND(DATEDIFF(microsecond, order_first_hvi_datetime_utc::TIMESTAMP, pf_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                WHEN dlr_hvi_count > 0 THEN
                    LEAST(
                        ROUND(DATEDIFF(microsecond, order_first_hvi_datetime_utc::TIMESTAMP, dlr_last_hvi_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, order_first_hvi_datetime_utc::TIMESTAMP, dlr_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                    )
                WHEN dlr_end_datetime_utc IS NOT NULL THEN ROUND(DATEDIFF(microsecond, order_first_hvi_datetime_utc::TIMESTAMP, dlr_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                WHEN prsnt_hvi_count > 0 THEN
                    LEAST(
                        ROUND(DATEDIFF(microsecond, order_first_hvi_datetime_utc::TIMESTAMP, prsnt_last_hvi_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, order_first_hvi_datetime_utc::TIMESTAMP, prsnt_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                    )
                WHEN prsnt_end_datetime_utc IS NOT NULL THEN ROUND(DATEDIFF(microsecond, order_first_hvi_datetime_utc::TIMESTAMP, prsnt_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                WHEN pmt_hvi_count > 0 THEN
                    LEAST(
                        ROUND(DATEDIFF(microsecond, order_first_hvi_datetime_utc::TIMESTAMP, pmt_last_hvi_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, order_first_hvi_datetime_utc::TIMESTAMP, pmt_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                    )
                WHEN pmt_end_datetime_utc IS NOT NULL THEN ROUND(DATEDIFF(microsecond, order_first_hvi_datetime_utc::TIMESTAMP, pmt_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
            END
        ELSE 
            CASE 
                WHEN wb_last_hvi_datetime_utc IS NOT NULL THEN ROUND(DATEDIFF(microsecond, order_start_datetime_utc::TIMESTAMP, wb_last_hvi_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                WHEN wb_stated_duration_ms > 60000 AND wb_last_hvi_datetime_utc IS NULL THEN ROUND(DATEDIFF(microsecond, order_start_datetime_utc::TIMESTAMP, wb_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                WHEN pf_last_hvi_datetime_utc IS NOT NULL THEN
                    LEAST(
                        ROUND(DATEDIFF(microsecond, order_start_datetime_utc::TIMESTAMP, pf_last_hvi_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, order_start_datetime_utc::TIMESTAMP, pf_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                    )
                WHEN pf_stated_duration_ms > 30000 AND pf_last_hvi_datetime_utc IS NULL THEN ROUND(DATEDIFF(microsecond, order_start_datetime_utc::TIMESTAMP, pf_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                WHEN dlr_hvi_count > 0 THEN
                    LEAST(
                        ROUND(DATEDIFF(microsecond, order_start_datetime_utc::TIMESTAMP, dlr_last_hvi_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, order_start_datetime_utc::TIMESTAMP, dlr_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                    )
                WHEN dlr_end_datetime_utc IS NOT NULL THEN ROUND(DATEDIFF(microsecond, order_start_datetime_utc::TIMESTAMP, dlr_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                WHEN prsnt_hvi_count > 0 THEN
                    LEAST(
                        ROUND(DATEDIFF(microsecond, order_start_datetime_utc::TIMESTAMP, prsnt_last_hvi_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, order_start_datetime_utc::TIMESTAMP, prsnt_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                    )
                WHEN prsnt_end_datetime_utc IS NOT NULL THEN ROUND(DATEDIFF(microsecond, order_start_datetime_utc::TIMESTAMP, dlr_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                WHEN pmt_hvi_count > 0 THEN
                    LEAST(
                        ROUND(DATEDIFF(microsecond, order_start_datetime_utc::TIMESTAMP, pmt_last_hvi_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, order_start_datetime_utc::TIMESTAMP, pmt_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                    )
                WHEN pmt_end_datetime_utc IS NOT NULL THEN ROUND(DATEDIFF(microsecond, order_start_datetime_utc::TIMESTAMP, pmt_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT 
                ELSE  NULL
            END
    END AS oslde_ms,
    -- deliver start to last deliver end
    CASE
        WHEN prsnt_start_datetime_utc IS NOT NULL THEN
            CASE 
                WHEN wb_last_hvi_datetime_utc IS NOT NULL THEN ROUND(DATEDIFF(microsecond, prsnt_start_datetime_utc::TIMESTAMP, wb_last_hvi_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                WHEN wb_stated_duration_ms > 60000 AND wb_last_hvi_datetime_utc IS NULL THEN ROUND(DATEDIFF(microsecond, prsnt_start_datetime_utc::TIMESTAMP, wb_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                WHEN pf_last_hvi_datetime_utc IS NOT NULL THEN
                    LEAST(
                        ROUND(DATEDIFF(microsecond, prsnt_start_datetime_utc::TIMESTAMP, pf_last_hvi_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, prsnt_start_datetime_utc::TIMESTAMP, pf_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                    )
                WHEN pf_stated_duration_ms > 30000 AND pf_last_hvi_datetime_utc IS NULL THEN ROUND(DATEDIFF(microsecond, prsnt_start_datetime_utc::TIMESTAMP, pf_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                WHEN dlr_hvi_count > 0 THEN
                    LEAST(
                        ROUND(DATEDIFF(microsecond, prsnt_start_datetime_utc::TIMESTAMP, dlr_last_hvi_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, prsnt_start_datetime_utc::TIMESTAMP, dlr_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                    )
                WHEN dlr_end_datetime_utc IS NOT NULL THEN ROUND(DATEDIFF(microsecond, prsnt_start_datetime_utc::TIMESTAMP, dlr_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                WHEN prsnt_hvi_count > 0 THEN
                    LEAST(
                        ROUND(DATEDIFF(microsecond, prsnt_start_datetime_utc::TIMESTAMP, prsnt_last_hvi_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, prsnt_start_datetime_utc::TIMESTAMP, prsnt_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                    )
                WHEN prsnt_end_datetime_utc IS NOT NULL THEN ROUND(DATEDIFF(microsecond, prsnt_start_datetime_utc::TIMESTAMP, prsnt_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                END
        ELSE 
            CASE 
                WHEN wb_last_hvi_datetime_utc IS NOT NULL THEN ROUND(DATEDIFF(microsecond, dlr_start_datetime_utc::TIMESTAMP, wb_last_hvi_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                WHEN wb_stated_duration_ms > 60000 AND wb_last_hvi_datetime_utc IS NULL THEN ROUND(DATEDIFF(microsecond, dlr_start_datetime_utc::TIMESTAMP, wb_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                WHEN pf_last_hvi_datetime_utc IS NOT NULL THEN
                    LEAST(
                        ROUND(DATEDIFF(microsecond, dlr_start_datetime_utc::TIMESTAMP, pf_last_hvi_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, dlr_start_datetime_utc::TIMESTAMP, pf_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                    )
                WHEN pf_stated_duration_ms > 30000 AND pf_last_hvi_datetime_utc IS NULL THEN ROUND(DATEDIFF(microsecond, dlr_start_datetime_utc::TIMESTAMP, pf_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                WHEN dlr_hvi_count > 0 THEN
                    LEAST(
                        ROUND(DATEDIFF(microsecond, dlr_start_datetime_utc::TIMESTAMP, dlr_last_hvi_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, dlr_start_datetime_utc::TIMESTAMP, dlr_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                    )
                WHEN dlr_end_datetime_utc IS NOT NULL THEN ROUND(DATEDIFF(microsecond, dlr_start_datetime_utc::TIMESTAMP, dlr_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                WHEN prsnt_hvi_count > 0 THEN
                    LEAST(
                        ROUND(DATEDIFF(microsecond, dlr_start_datetime_utc::TIMESTAMP, prsnt_last_hvi_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT,
                        ROUND(DATEDIFF(microsecond, dlr_start_datetime_utc::TIMESTAMP, prsnt_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                    )
                WHEN prsnt_end_datetime_utc IS NOT NULL THEN ROUND(DATEDIFF(microsecond, dlr_start_datetime_utc::TIMESTAMP, dlr_end_datetime_utc::TIMESTAMP)::FLOAT / 1000, 0)::INT
                ELSE  NULL
            END
    END AS dslde_ms
FROM _journey_data JOIN _deduplicated
ON (
    vehicle_id = check_vehicle_id
    AND meta_kinesis_datastream_arrival_datetime_utc = check_meta_kinesis_datastream_arrival_datetime_utc
)