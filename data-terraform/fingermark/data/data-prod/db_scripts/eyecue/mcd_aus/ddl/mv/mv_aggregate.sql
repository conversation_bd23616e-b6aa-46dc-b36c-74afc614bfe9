CREATE MATERIALIZED VIEW eyecue_mcd_aus.mv_aggregate AUTO REFRESH YES AS
SELECT approximate_arrival_timestamp,
       partition_key,
       shard_id,
       sequence_number,
       refresh_time,
       json_parse(kinesis_data) as         payload,
       JSON_SIZE(json_parse(KINESIS_DATA)) payload_size,
       case
           when JSON_SIZE(json_parse(KINESIS_DATA)) > 65355 then true
           else false end       as         is_outlier
FROM stream_mcd_aus."ds-mcd-eyecue-aggregate"
where CAN_JSON_PARSE(kinesis_data);

-- create table eyecue_mcd_aus.mv_aggregate_20241111 as

select *
from eyecue_mcd_aus.mv_aggregate
where approximate_arrival_timestamp > sysdate - 1
order by approximate_arrival_timestamp desc
limit 10;

-- drop MATERIALIZED view eyecue_mcd_aus.mv_aggregate