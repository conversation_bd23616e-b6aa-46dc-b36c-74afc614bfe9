-- drop  view eyecue_mcd_aus.v_aggregated ;

create or replace view eyecue_mcd_aus.v_aggregated
as
(
select "mv_aggregate"."approximate_arrival_timestamp" as meta_kinesis_datastream_arrival_datetime_utc,
       "mv_aggregate"."refresh_time"                  as meta_redshift_refresh_datetime_utc,
       "mv_aggregate"."partition_key"                 as partition_key,
       "mv_aggregate"."shard_id"                      as shard_id,
       "mv_aggregate"."sequence_number"
                                                      as sequence_number,
       "mv_aggregate"."payload"                       as payload
from "eyecue_mcd_aus"."mv_aggregate" as "mv_aggregate"
where meta_kinesis_datastream_arrival_datetime_utc > sysdate - 3
-- union all
-- select "mv_aggregate"."approximate_arrival_timestamp" as meta_kinesis_datastream_arrival_datetime_utc,
--        "mv_aggregate"."refresh_time"                  as meta_redshift_refresh_datetime_utc,
--        "mv_aggregate"."partition_key"                 as partition_key,
--        "mv_aggregate"."shard_id"                      as shard_id,
--        "mv_aggregate"."sequence_number"               as sequence_number,
--        "mv_aggregate"."payload"                       as payload
-- from "eyecue_mcd_aus"."mv_ds_mcd_aus_eyecue_events" as "mv_aggregate"
-- where meta_kinesis_datastream_arrival_datetime_utc > sysdate - 3
--   and payload.event_type = 'aggregate'
    );


select meta_kinesis_datastream_arrival_datetime_utc,
       meta_redshift_refresh_datetime_utc,
       partition_key,
       shard_id,
       sequence_number,
       payload
from (select meta_kinesis_datastream_arrival_datetime_utc,
             meta_redshift_refresh_datetime_utc,
             partition_key,
             shard_id,
             sequence_number,
             payload,
             ROW_NUMBER()
             over (PARTITION BY payload order by meta_kinesis_datastream_arrival_datetime_utc DESC NULLS FIRST) as rn
      from eyecue_mcd_aus.v_aggregated
      where meta_kinesis_datastream_arrival_datetime_utc > sysdate - interval '1' hour) as va
where rn = 1