
CREATE TABLE eyecue_mcd_aus.dim_restaurant
(
    fingermark_id   VARCHAR(20) ENCODE lzo,
    restaurant_name VARCHAR(255) ENCODE lzo,
    time_zone       VARCHAR(50) ENCODE lzo,
    address         VARCHAR(255) ENCODE lzo,
    suburb          VARCHAR(255) ENCODE lzo,
    city            VARCHAR(255) ENCODE lzo,
    state           VARCHAR(255) ENCODE lzo,
    post_code       VARCHAR(20) ENCODE lzo,
    country         VARCHAR(100) ENCODE lzo,
    latitude        NUMERIC(10, 7) ENCODE az64,
    longitude       NUMERIC(10, 7) ENCODE az64,
    num_order_lanes INT ENCODE az64,
    pf_blocks_lane  BOOLEAN ENCODE raw
);


-- Update latest fingermark_ids from aggregated data,

INSERT INTO eyecue_mcd_aus.dim_restaurant (fingermark_id)
SELECT store_id
FROM (SELECT payload.store_id::text AS store_id
      FROM eyecue_mcd_aus.v_aggregated
      GROUP BY payload.store_id::text) AS store_id
WHERE store_id NOT IN (SELECT fingermark_id
                       FROM eyecue_mcd_aus.dim_restaurant);


-- select * from eyecue_mcd_aus.dim_restaurant;