CREATE
OR REPLACE VIEW "eyecue_mcd_can"."v_quicksight_fct_aggregated" AS
SELECT
    _aggregated.fingermark_id AS "fingermark id",
    _restaurant.restaurant_name AS "restaurant name",
    (
        (
            _restaurant.restaurant_name:: text || ' (':: character varying:: text
        ) ||  _restaurant.restaurant_id:: text
    ) || ')':: character varying:: text AS "restaurant identifier",
    _restaurant.latitude,
    _restaurant.longitude,
    timezone(
        _restaurant.time_zone:: text,
        _aggregated.ingress_start_datetime_utc
    ) AS "ingress entry time (local)",
    round(
        _aggregated.ingress_stated_duration_ms:: double precision / 1000:: double precision,
        3:: numeric:: numeric(18, 0)
    ) AS "ingress zone duration (seconds)",
    CASE
    WHEN _aggregated.queue_pre_start_duration_ms = 0 THEN NULL:: double precision
    ELSE round(
        _aggregated.queue_pre_start_duration_ms:: double precision / 1000:: double precision,
        3:: numeric:: numeric(18, 0)
    ) END AS "start queue duration (seconds)",
    timezone(
        _restaurant.time_zone:: text,
        LEAST(
            _aggregated.start_start_datetime_utc,
            _aggregated.order_start_datetime_utc
        )
    ) AS "journey start time (local)",
    round(
        _aggregated.start_stated_duration_ms:: double precision / 1000:: double precision,
        3:: numeric:: numeric(18, 0)
    ) AS "start zone duration (seconds)",
    CASE
    WHEN _aggregated.queue_pre_order_duration_ms = 0 THEN NULL:: double precision
    ELSE round(
        _aggregated.queue_pre_order_duration_ms:: double precision / 1000:: double precision,
        3:: numeric:: numeric(18, 0)
    ) END AS "order queue duration (seconds)",
    _aggregated.order_lane_number AS "order lane number",
    round(
        _aggregated.order_stated_duration_ms:: double precision / 1000:: double precision,
        3:: numeric:: numeric(18, 0)
    ) AS "order zone duration (seconds)",
    CASE
    WHEN _aggregated.order_hvi_count = 0 THEN NULL:: integer
    ELSE _aggregated.order_hvi_count END AS "hvi count at order",
    round(
        _aggregated.order_ms_to_first_hvi:: double precision / 1000:: double precision,
        3:: numeric:: numeric(18, 0)
    ) AS "order entry to first hvi duration (seconds)",
    round(
        _aggregated.order_ms_from_last_hvi:: double precision / 1000:: double precision,
        3:: numeric:: numeric(18, 0)
    ) AS "last hvi to order exit duration (seconds)",
    round(
        _aggregated.order_average_hvi_duration_ms:: double precision / 1000:: double precision,
        3:: numeric:: numeric(18, 0)
    ) AS "order average hvi duration (seconds)",
    CASE
    WHEN _aggregated.queue_pre_pmt_duration_ms = 0 THEN NULL:: double precision
    ELSE round(
        _aggregated.queue_pre_pmt_duration_ms:: double precision / 1000:: double precision,
        3:: numeric:: numeric(18, 0)
    ) END AS "payment queue duration (seconds)",
    round(
        _aggregated.pmt_stated_duration_ms:: double precision / 1000:: double precision,
        3:: numeric:: numeric(18, 0)
    ) AS "payment zone duration (seconds)",
    CASE
    WHEN _aggregated.pmt_hvi_count = 0 THEN NULL:: integer
    ELSE _aggregated.pmt_hvi_count END AS "hvi count at payment",
    round(
        _aggregated.pmt_ms_to_first_hvi:: double precision / 1000:: double precision,
        3:: numeric:: numeric(18, 0)
    ) AS "payment entry to first hvi duration (seconds)",
    round(
        _aggregated.pmt_ms_from_last_hvi:: double precision / 1000:: double precision,
        3:: numeric:: numeric(18, 0)
    ) AS "last hvi to payment exit duration (seconds)",
    round(
        _aggregated.pmt_average_hvi_duration_ms:: double precision / 1000:: double precision,
        3:: numeric:: numeric(18, 0)
    ) AS "payment average hvi duration (seconds)",
    CASE
    WHEN _aggregated.queue_pre_prsnt_duration_ms = 0 THEN NULL:: double precision
    ELSE round(
        _aggregated.queue_pre_prsnt_duration_ms:: double precision / 1000:: double precision,
        3:: numeric:: numeric(18, 0)
    ) END AS "presenter queue duration (seconds)",
    round(
        _aggregated.prsnt_stated_duration_ms:: double precision / 1000:: double precision,
        3:: numeric:: numeric(18, 0)
    ) AS "presenter zone duration (seconds)",
    CASE
    WHEN _aggregated.prsnt_hvi_count = 0 THEN NULL:: integer
    ELSE _aggregated.prsnt_hvi_count END AS "hvi count at presenter",
    round(
        _aggregated.prsnt_ms_to_first_hvi:: double precision / 1000:: double precision,
        3:: numeric:: numeric(18, 0)
    ) AS "presenter entry to first hvi duration (seconds)",
    round(
        _aggregated.prsnt_ms_from_last_hvi:: double precision / 1000:: double precision,
        3:: numeric:: numeric(18, 0)
    ) AS "last hvi to presenter exit duration (seconds)",
    round(
        _aggregated.prsnt_average_hvi_duration_ms:: double precision / 1000:: double precision,
        3:: numeric:: numeric(18, 0)
    ) AS "presenter average hvi duration (seconds)",
    CASE
    WHEN _aggregated.queue_pre_dlr_duration_ms = 0 THEN NULL:: double precision
    ELSE round(
        _aggregated.queue_pre_dlr_duration_ms:: double precision / 1000:: double precision,
        3:: numeric:: numeric(18, 0)
    ) END AS "deliver queue duration (seconds)",
    round(
        _aggregated.dlr_stated_duration_ms:: double precision / 1000:: double precision,
        3:: numeric:: numeric(18, 0)
    ) AS "deliver zone duration (seconds)",
    CASE
    WHEN _aggregated.dlr_hvi_count = 0 THEN NULL:: integer
    ELSE _aggregated.dlr_hvi_count END AS "hvi count at deliver",
    round(
        _aggregated.dlr_ms_to_first_hvi:: double precision / 1000:: double precision,
        3:: numeric:: numeric(18, 0)
    ) AS "deliver entry to first hvi duration (seconds)",
    round(
        _aggregated.dlr_ms_from_last_hvi:: double precision / 1000:: double precision,
        3:: numeric:: numeric(18, 0)
    ) AS "last hvi to deliver exit duration (seconds)",
    round(
        _aggregated.dlr_average_hvi_duration_ms:: double precision / 1000:: double precision,
        3:: numeric:: numeric(18, 0)
    ) AS "deliver average hvi duration (seconds)",
    CASE
    WHEN _aggregated.queue_pre_pf_duration_ms = 0 THEN NULL:: double precision
    ELSE round(
        _aggregated.queue_pre_pf_duration_ms:: double precision / 1000:: double precision,
        3:: numeric:: numeric(18, 0)
    ) END AS "pull forward queue duration (seconds)",
    round(
        _aggregated.pf_stated_duration_ms:: double precision / 1000:: double precision,
        3:: numeric:: numeric(18, 0)
    ) AS "pull forward zone duration (seconds)",
    CASE
    WHEN _aggregated.pf_hvi_count = 0 THEN NULL:: integer
    ELSE _aggregated.pf_hvi_count END AS "hvi count at pull forward",
    round(
        _aggregated.pf_ms_to_first_hvi:: double precision / 1000:: double precision,
        3:: numeric:: numeric(18, 0)
    ) AS "pull forward entry to first hvi duration (seconds)",
    round(
        _aggregated.pf_ms_from_last_hvi:: double precision / 1000:: double precision,
        3:: numeric:: numeric(18, 0)
    ) AS "last hvi to pull forward exit duration (seconds)",
    round(
        _aggregated.pf_average_hvi_duration_ms:: double precision / 1000:: double precision,
        3:: numeric:: numeric(18, 0)
    ) AS "pull forward average hvi duration (seconds)",
    CASE
    WHEN _aggregated.queue_pre_wb_duration_ms = 0 THEN NULL:: double precision
    ELSE round(
        _aggregated.queue_pre_wb_duration_ms:: double precision / 1000:: double precision,
        3:: numeric:: numeric(18, 0)
    ) END AS "waiting bay queue duration (seconds)",
    round(
        _aggregated.wb_stated_duration_ms:: double precision / 1000:: double precision,
        3:: numeric:: numeric(18, 0)
    ) AS "waiting bay zone duration (seconds)",
    CASE
    WHEN _aggregated.wb_hvi_count = 0 THEN NULL:: integer
    ELSE _aggregated.wb_hvi_count END AS "hvi count at waiting bay",
    round(
        _aggregated.wb_ms_to_first_hvi:: double precision / 1000:: double precision,
        3:: numeric:: numeric(18, 0)
    ) AS "waiting bay entry to first hvi duration (seconds)",
    round(
        _aggregated.wb_ms_from_last_hvi:: double precision / 1000:: double precision,
        3:: numeric:: numeric(18, 0)
    ) AS "last hvi to waiting bay exit duration (seconds)",
    round(
        _aggregated.wb_average_hvi_duration_ms:: double precision / 1000:: double precision,
        3:: numeric:: numeric(18, 0)
    ) AS "waiting bay average hvi duration (seconds)",
    CASE
    WHEN _aggregated.queue_pre_egress_duration_ms = 0 THEN NULL:: double precision
    ELSE round(
        _aggregated.queue_pre_egress_duration_ms:: double precision / 1000:: double precision,
        3:: numeric:: numeric(18, 0)
    ) END AS "egress queue duration (seconds)",
    round(
        _aggregated.egress_stated_duration_ms:: double precision / 1000:: double precision,
        3:: numeric:: numeric(18, 0)
    ) AS "egress zone duration (seconds)",
    round(
        _aggregated.queue_pre_fin_duration_ms:: double precision / 1000:: double precision,
        3:: numeric:: numeric(18, 0)
    ) AS "finish queue duration (seconds)",
    round(
        _aggregated.fin_stated_duration_ms:: double precision / 1000:: double precision,
        3:: numeric:: numeric(18, 0)
    ) AS "finish zone duration (seconds)",
    CASE
    WHEN _aggregated.lost_asset_event_datetime_utc IS NULL THEN true
    ELSE false END AS "valid journey",
    round(
        _aggregated.total_ms_observed:: double precision / 1000:: double precision,
        3:: numeric:: numeric(18, 0)
    ) AS "total observed duration (seconds)",
    round(
        _aggregated.oelde_ms:: double precision / 1000:: double precision,
        3:: numeric:: numeric(18, 0)
    ) AS "order end to last deliver end duration (seconds)",
    round(
        _aggregated.oslde_ms:: double precision / 1000:: double precision,
        3:: numeric:: numeric(18, 0)
    ) AS "order start to last deliver end duration (seconds)",
    round(
        _aggregated.dslde_ms:: double precision / 1000:: double precision,
        3:: numeric:: numeric(18, 0)
    ) AS "deliver start to last deliver end duration (seconds)"
FROM
    (
        SELECT
            fct_aggregated_from_view.meta_kinesis_datastream_arrival_datetime_utc,
            fct_aggregated_from_view.meta_redshift_refresh_datetime_utc,
            fct_aggregated_from_view.partition_key,
            fct_aggregated_from_view.shard_id,
            fct_aggregated_from_view.sequence_number,
            fct_aggregated_from_view.payload_variations,
            fct_aggregated_from_view.vehicle_id,
            fct_aggregated_from_view.fingermark_id,
            fct_aggregated_from_view.ghost,
            fct_aggregated_from_view.graph_history,
            fct_aggregated_from_view.handheld_point_of_sale,
            fct_aggregated_from_view.tz_offset,
            fct_aggregated_from_view.ingress_start_datetime_utc,
            fct_aggregated_from_view.ingress_end_datetime_utc,
            fct_aggregated_from_view.ingress_lane_number,
            fct_aggregated_from_view.ingress_roi_id,
            fct_aggregated_from_view.ingress_cars_count,
            fct_aggregated_from_view.ingress_stated_duration_ms,
            fct_aggregated_from_view.ingress_durations_match,
            fct_aggregated_from_view.start_start_datetime_utc,
            fct_aggregated_from_view.start_end_datetime_utc,
            fct_aggregated_from_view.start_lane_number,
            fct_aggregated_from_view.start_roi_id,
            fct_aggregated_from_view.start_cars_count,
            fct_aggregated_from_view.start_stated_duration_ms,
            fct_aggregated_from_view.start_durations_match,
            fct_aggregated_from_view.order_start_datetime_utc,
            fct_aggregated_from_view.order_end_datetime_utc,
            fct_aggregated_from_view.order_lane_number,
            fct_aggregated_from_view.order_roi_id,
            fct_aggregated_from_view.order_cars_count,
            fct_aggregated_from_view.order_hvi_count,
            fct_aggregated_from_view.order_first_hvi_datetime_utc,
            fct_aggregated_from_view.order_last_hvi_datetime_utc,
            fct_aggregated_from_view.order_ms_to_first_hvi,
            fct_aggregated_from_view.order_ms_from_last_hvi,
            fct_aggregated_from_view.order_hvi_end_after_roi_end,
            fct_aggregated_from_view.order_average_hvi_duration_ms,
            fct_aggregated_from_view.order_stated_duration_ms,
            fct_aggregated_from_view.order_durations_match,
            fct_aggregated_from_view.pmt_start_datetime_utc,
            fct_aggregated_from_view.pmt_end_datetime_utc,
            fct_aggregated_from_view.pmt_lane_number,
            fct_aggregated_from_view.pmt_roi_id,
            fct_aggregated_from_view.pmt_cars_count,
            fct_aggregated_from_view.pmt_hvi_count,
            fct_aggregated_from_view.pmt_first_hvi_datetime_utc,
            fct_aggregated_from_view.pmt_last_hvi_datetime_utc,
            fct_aggregated_from_view.pmt_ms_to_first_hvi,
            fct_aggregated_from_view.pmt_ms_from_last_hvi,
            fct_aggregated_from_view.pmt_hvi_end_after_roi_end,
            fct_aggregated_from_view.pmt_average_hvi_duration_ms,
            fct_aggregated_from_view.pmt_stated_duration_ms,
            fct_aggregated_from_view.pmt_durations_match,
            fct_aggregated_from_view.prsnt_start_datetime_utc,
            fct_aggregated_from_view.prsnt_end_datetime_utc,
            fct_aggregated_from_view.prsnt_lane_number,
            fct_aggregated_from_view.prsnt_roi_id,
            fct_aggregated_from_view.prsnt_cars_count,
            fct_aggregated_from_view.prsnt_hvi_count,
            fct_aggregated_from_view.prsnt_first_hvi_datetime_utc,
            fct_aggregated_from_view.prsnt_last_hvi_datetime_utc,
            fct_aggregated_from_view.prsnt_ms_to_first_hvi,
            fct_aggregated_from_view.prsnt_ms_from_last_hvi,
            fct_aggregated_from_view.prsnt_hvi_end_after_roi_end,
            fct_aggregated_from_view.prsnt_average_hvi_duration_ms,
            fct_aggregated_from_view.prsnt_stated_duration_ms,
            fct_aggregated_from_view.prsnt_durations_match,
            fct_aggregated_from_view.dlr_start_datetime_utc,
            fct_aggregated_from_view.dlr_end_datetime_utc,
            fct_aggregated_from_view.dlr_lane_number,
            fct_aggregated_from_view.dlr_roi_id,
            fct_aggregated_from_view.dlr_cars_count,
            fct_aggregated_from_view.dlr_hvi_count,
            fct_aggregated_from_view.dlr_first_hvi_datetime_utc,
            fct_aggregated_from_view.dlr_last_hvi_datetime_utc,
            fct_aggregated_from_view.dlr_ms_to_first_hvi,
            fct_aggregated_from_view.dlr_ms_from_last_hvi,
            fct_aggregated_from_view.dlr_hvi_end_after_roi_end,
            fct_aggregated_from_view.dlr_average_hvi_duration_ms,
            fct_aggregated_from_view.dlr_stated_duration_ms,
            fct_aggregated_from_view.dlr_durations_match,
            fct_aggregated_from_view.pf_start_datetime_utc,
            fct_aggregated_from_view.pf_end_datetime_utc,
            fct_aggregated_from_view.pf_lane_number,
            fct_aggregated_from_view.pf_roi_id,
            fct_aggregated_from_view.pf_cars_count,
            fct_aggregated_from_view.pf_hvi_count,
            fct_aggregated_from_view.pf_first_hvi_datetime_utc,
            fct_aggregated_from_view.pf_last_hvi_datetime_utc,
            fct_aggregated_from_view.pf_ms_to_first_hvi,
            fct_aggregated_from_view.pf_ms_from_last_hvi,
            fct_aggregated_from_view.pf_hvi_end_after_roi_end,
            fct_aggregated_from_view.pf_average_hvi_duration_ms,
            fct_aggregated_from_view.pf_stated_duration_ms,
            fct_aggregated_from_view.pf_durations_match,
            fct_aggregated_from_view.wb_start_datetime_utc,
            fct_aggregated_from_view.wb_end_datetime_utc,
            fct_aggregated_from_view.wb_lane_number,
            fct_aggregated_from_view.wb_roi_id,
            fct_aggregated_from_view.wb_cars_count,
            fct_aggregated_from_view.wb_hvi_count,
            fct_aggregated_from_view.wb_first_hvi_datetime_utc,
            fct_aggregated_from_view.wb_last_hvi_datetime_utc,
            fct_aggregated_from_view.wb_ms_to_first_hvi,
            fct_aggregated_from_view.wb_ms_from_last_hvi,
            fct_aggregated_from_view.wb_hvi_end_after_roi_end,
            fct_aggregated_from_view.wb_average_hvi_duration_ms,
            fct_aggregated_from_view.wb_stated_duration_ms,
            fct_aggregated_from_view.wb_durations_match,
            fct_aggregated_from_view.egress_start_datetime_utc,
            fct_aggregated_from_view.egress_end_datetime_utc,
            fct_aggregated_from_view.egress_lane_number,
            fct_aggregated_from_view.egress_roi_id,
            fct_aggregated_from_view.egress_cars_count,
            fct_aggregated_from_view.egress_stated_duration_ms,
            fct_aggregated_from_view.egress_durations_match,
            fct_aggregated_from_view.fin_start_datetime_utc,
            fct_aggregated_from_view.fin_end_datetime_utc,
            fct_aggregated_from_view.fin_lane_number,
            fct_aggregated_from_view.fin_roi_id,
            fct_aggregated_from_view.fin_cars_count,
            fct_aggregated_from_view.fin_stated_duration_ms,
            fct_aggregated_from_view.fin_durations_match,
            fct_aggregated_from_view.wb_ms_from_last_hvi_to_finish,
            fct_aggregated_from_view.lost_asset_event_datetime_utc,
            fct_aggregated_from_view.queue_pre_start_duration_ms,
            fct_aggregated_from_view.queue_pre_order_duration_ms,
            fct_aggregated_from_view.queue_pre_pmt_duration_ms,
            fct_aggregated_from_view.queue_pre_prsnt_duration_ms,
            fct_aggregated_from_view.queue_pre_dlr_duration_ms,
            fct_aggregated_from_view.queue_pre_pf_duration_ms,
            fct_aggregated_from_view.queue_pre_wb_duration_ms,
            fct_aggregated_from_view.queue_pre_egress_duration_ms,
            fct_aggregated_from_view.queue_pre_fin_duration_ms,
            fct_aggregated_from_view.total_ms_observed,
            fct_aggregated_from_view.oelde_ms,
            fct_aggregated_from_view.oslde_ms,
            fct_aggregated_from_view.dslde_ms,
            fct_aggregated_from_view.record_creation_time_utc
        FROM
            eyecue_mcd_can.fct_aggregated_from_view
    ) _aggregated
    LEFT JOIN (
        SELECT
            dim_restaurant.fingermark_id,
            dim_restaurant.restaurant_name,
            dim_restaurant.restaurant_id,
            dim_restaurant.time_zone,
            dim_restaurant.address,
            dim_restaurant.suburb,
            dim_restaurant.city,
            dim_restaurant.state,
            dim_restaurant.post_code,
            dim_restaurant.country,
            dim_restaurant.latitude,
            dim_restaurant.longitude
        FROM
            eyecue_mcd_can.dim_restaurant
    ) _restaurant USING (fingermark_id);