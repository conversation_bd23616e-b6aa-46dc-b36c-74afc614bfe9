create MATERIALIZED view eyecue_mcd_can.mv_last_1_day_queue_zone as WITH data_with_ranking AS (
    SELECT
        md5(json_serialize(payload)) AS hash_key,
        meta_kinesis_datastream_arrival_datetime_utc,
        payload,
        JSON_PARSE(
            JSON_EXTRACT_PATH_TEXT(json_serialize(payload), 'functional_zones', true)
        ) AS functional_zones,
        ROW_NUMBER() OVER (
            PARTITION BY md5(json_serialize(payload))
            ORDER BY
                meta_kinesis_datastream_arrival_datetime_utc DESC
        ) AS row_num
    FROM
        prod.eyecue_mcd_can.mv_eventstream
    WHERE
        1 = 1
        AND meta_kinesis_datastream_arrival_datetime_utc >= trunc(sysdate) - 1 -- Records from the last 1 day
        AND payload.event_type = 'danger-zone'
)
select
    hash_key,
    payload,
    meta_kinesis_datastream_arrival_datetime_utc,
    functional_zones
from
    data_with_ranking
where
    row_num = 1;