CREATE
OR REPLACE VIEW "eyecue_mcd_can"."v_quicksight_fct_hvi" AS
SELECT
        _hvi.fingermark_id AS "fingermark id",
        _restaurant.restaurant_name AS "restaurant name",
        (
                (
                        _restaurant.restaurant_name:: text || ' (':: character varying:: text
                ) || _restaurant.restaurant_id:: text
        ) || ')':: character varying:: text AS "restaurant identifier",
        _restaurant.latitude,
        _restaurant.longitude,
        _hvi.event_id AS "event identifier",
        _hvi.event_type AS "event type",
        _hvi.vehicle_id AS "vehicle identifier",
        _hvi.start_datetime_local AS "zone region entry time (local)",
        _hvi.end_datetime_local AS "zone region exit time (local)",
        _hvi.total_time AS "duration (seconds)",
        timezone(
                _restaurant.time_zone:: text,
                _hvi.camera_detection_datetime_utc
        ) AS "camera entry time (local)",
        _hvi.camera_id AS "camera identifier",
        _hvi.roi_id AS "zone region identifier",
        _hvi.roi_id_match AS "start and end zone region types match",
        _hvi.roi_type AS "zone identifier",
        _hvi.roi_type_match AS "start and end zone types match",
        _hvi.start_confidence AS "start confidence",
        _hvi.end_confidence AS "end confidence",
        _hvi.start_frames_life AS "start frames life",
        _hvi.end_frames_life AS "end frames life",
        _hvi.vehicle_type AS "type of vehicle identified",
        _hvi.lane_number AS "lane number",
        _hvi.graph_order AS "graph order",
        _hvi.overtaken AS "count of vehicles overtaken",
        _hvi.valid_journey AS "valid journey"
FROM
        (
                SELECT
                        fct_hvi_events.fingermark_id,
                        fct_hvi_events.event_id,
                        fct_hvi_events.event_type,
                        fct_hvi_events.vehicle_id,
                        fct_hvi_events.start_datetime_local,
                        fct_hvi_events.end_datetime_local,
                        fct_hvi_events.camera_detection_datetime_utc,
                        fct_hvi_events.camera_id,
                        fct_hvi_events.roi_id,
                        fct_hvi_events.roi_id_match,
                        fct_hvi_events.roi_type,
                        fct_hvi_events.roi_type_match,
                        fct_hvi_events.total_time,
                        fct_hvi_events.start_confidence,
                        fct_hvi_events.end_confidence,
                        fct_hvi_events.start_frames_life,
                        fct_hvi_events.end_frames_life,
                        fct_hvi_events.vehicle_type,
                        fct_hvi_events.lane_number,
                        fct_hvi_events.graph_order,
                        fct_hvi_events.overtaken,
                        fct_hvi_events.valid_journey
                FROM
                        eyecue_mcd_can.fct_hvi_events
        ) _hvi
        LEFT JOIN (
                SELECT
                        dim_restaurant.fingermark_id,
                        dim_restaurant.restaurant_name,
                        dim_restaurant.restaurant_id,
                        dim_restaurant.time_zone,
                        dim_restaurant.address,
                        dim_restaurant.suburb,
                        dim_restaurant.city,
                        dim_restaurant.state,
                        dim_restaurant.post_code,
                        dim_restaurant.country,
                        dim_restaurant.latitude,
                        dim_restaurant.longitude
                FROM
                        eyecue_mcd_can.dim_restaurant
        ) _restaurant USING (fingermark_id);