CREATE OR REPLACE FUNCTION public.get_min_duration(json_string character varying(65535))
 RETURNS double precision LANGUAGE plpythonu
 STABLE
AS $$
    import json
    total_time_sum = 0
    total_time_count = 0
    min_total_time = float('inf')
    try:
        json_object = json.loads(json_string)
    except json.JSONDecodeError:
        return None
    for data in json_object:  # Corrected variable name
        if "total_time" in data and data["total_time"] is not None:
            total_time = data["total_time"]
            total_time_count += 1
            min_total_time = min(min_total_time, total_time)
    if total_time_count == 0:
        return None
    return (min_total_time)  
$$
