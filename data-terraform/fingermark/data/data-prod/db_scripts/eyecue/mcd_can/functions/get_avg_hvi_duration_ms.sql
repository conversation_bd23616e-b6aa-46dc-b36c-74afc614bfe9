CREATE OR REPLACE FUNCTION public.get_avg_hvi_duration_ms(json_string character varying(65535))
 RETURNS double precision LANGUAGE plpythonu
 STABLE
AS $$
	
    import json
    total_time_array = []

    try:
        json_object = json.loads(json_string)
    except:
        return None

    for data in json_object: 
        if "total_time" in data and data["total_time"] is not None:
            total_time_array.append(data["total_time"])
    if total_time_array == []:
        return None

    average_total_time = (sum(total_time_array)/len(total_time_array))*1000
    return round(average_total_time, 0) 

$$
