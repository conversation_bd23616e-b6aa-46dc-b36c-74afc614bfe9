CREATE OR REPLACE FUNCTION public.get_payload_variations(json_string character varying(65535))
 RETURNS character varying LANGUAGE plpythonu
 STABLE
AS $$
	
    import json

    try:
        json_object = json.loads(json_string)
    except:
        return None

    variations =  [key for key in json_object.keys() if key not in ['site_id', 'tracker_ids', 'vehicle_db_id', 'store_id', 'event_type', 'client_name','ghost', 'graph_history', 'handheld_point_of_sale', 'mmc', 'vehicle_id','eyeq_server_version']]
    
    return ','.join(variations)

$$
