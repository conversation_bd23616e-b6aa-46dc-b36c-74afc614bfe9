CREATE OR <PERSON><PERSON><PERSON>CE PROCEDURE eyecue_tim_can.upload_data_to_fct_journey_from_view_table(schema_name character varying(256))
 LANGUAGE plpgsql
AS $$
BEGIN
    -- Create a temporary table
    EXECUTE 'CREATE TEMP TABLE temp_fct_journey_table_'|| schema_name ||' AS
              SELECT 
                meta_kinesis_datastream_arrival_datetime_utc::TIM<PERSON><PERSON><PERSON> WITHOUT TIME ZONE,
                meta_redshift_refresh_datetime_utc::TIMESTAMP WITHOUT TIME ZONE,
                partition_key::VARCHAR,
                shard_id::VARCHAR,
                sequence_number::VARCHAR,
                payload_variations::VARCHAR,
                vehicle_id::VARCHA<PERSON>,
                fingermark_id::VARCHAR,
                ghost::BO<PERSON>EAN,
                graph_history::SUPER,
                handheld_point_of_sale::SUPER,
                tz_offset::VARCHAR(10),
                ingress_start_datetime_utc::<PERSON><PERSON><PERSON><PERSON><PERSON> WITHOUT TIME <PERSON>ONE,
                ingress_end_datetime_utc::<PERSON><PERSON><PERSON><PERSON><PERSON> WITHOUT TIME ZONE,
                ingress_lane_number::INT,
                ingress_roi_id::VARCHAR,
                ingress_cars_count::INT,
                ingress_stated_duration_ms::INT,
                ingress_durations_match::BOOLEAN,
                start_start_datetime_utc::TIMESTAMP WITH TIME ZONE,
                start_end_datetime_utc::TIMESTAMP WITH TIME ZONE,
                start_lane_number::INT,
                start_roi_id::VARCHAR,
                start_cars_count::INT,
                start_stated_duration_ms::INT,
                start_durations_match::BOOLEAN,
                order_start_datetime_utc::TIMESTAMP WITH TIME ZONE,
                order_end_datetime_utc::TIMESTAMP WITH TIME ZONE,
                order_lane_number::INT,
                order_roi_id::VARCHAR,
                order_cars_count::INT,
                order_hvi_count::INT,
                order_first_hvi_datetime_utc::TIMESTAMP WITH TIME ZONE,
                order_last_hvi_datetime_utc::TIMESTAMP WITH TIME ZONE,
                order_ms_to_first_hvi::INT,
                order_ms_from_last_hvi::INT,
                order_hvi_end_after_roi_end::BOOLEAN,
                order_average_hvi_duration_ms::INT,
                order_stated_duration_ms::INT,
                order_durations_match::BOOLEAN,
                pmt_start_datetime_utc::TIMESTAMP WITH TIME ZONE,
                pmt_end_datetime_utc::TIMESTAMP WITH TIME ZONE,
                pmt_lane_number::INT,
                pmt_roi_id::VARCHAR,
                pmt_cars_count::INT,
                pmt_hvi_count::INT,
                pmt_first_hvi_datetime_utc::TIMESTAMP WITH TIME ZONE,
                pmt_last_hvi_datetime_utc::TIMESTAMP WITH TIME ZONE,
                pmt_ms_to_first_hvi::INT,
                pmt_ms_from_last_hvi::INT,
                pmt_hvi_end_after_roi_end::BOOLEAN,
                pmt_average_hvi_duration_ms::INT,
                pmt_stated_duration_ms::INT,
                pmt_durations_match::BOOLEAN,
                prsnt_start_datetime_utc::TIMESTAMP WITHOUT TIME ZONE,
                prsnt_end_datetime_utc::TIMESTAMP WITHOUT TIME ZONE,
                prsnt_lane_number::INT,
                prsnt_roi_id::VARCHAR,
                prsnt_cars_count::INT,
                prsnt_hvi_count::INT,
                prsnt_first_hvi_datetime_utc::TIMESTAMP WITHOUT TIME ZONE,
                prsnt_last_hvi_datetime_utc::TIMESTAMP WITHOUT TIME ZONE,
                prsnt_ms_to_first_hvi::INT,
                prsnt_ms_from_last_hvi::INT,
                prsnt_hvi_end_after_roi_end::BOOLEAN,
                prsnt_average_hvi_duration_ms::INT,
                prsnt_stated_duration_ms::INT,
                prsnt_durations_match::BOOLEAN,
                dlr_start_datetime_utc::TIMESTAMP WITH TIME ZONE,
                dlr_end_datetime_utc::TIMESTAMP WITH TIME ZONE,
                dlr_lane_number::INT,
                dlr_roi_id::VARCHAR,
                dlr_cars_count::INT,
                dlr_hvi_count::INT,
                dlr_first_hvi_datetime_utc::TIMESTAMP WITH TIME ZONE,
                dlr_last_hvi_datetime_utc::TIMESTAMP WITH TIME ZONE,
                dlr_ms_to_first_hvi::INT,
                dlr_ms_from_last_hvi::INT,
                dlr_hvi_end_after_roi_end::BOOLEAN,
                dlr_average_hvi_duration_ms::INT,
                dlr_stated_duration_ms::INT,
                dlr_durations_match::BOOLEAN,
                pf_start_datetime_utc::TIMESTAMP WITH TIME ZONE,
                pf_end_datetime_utc::TIMESTAMP WITH TIME ZONE,
                pf_lane_number::INT,
                pf_roi_id::VARCHAR,
                pf_cars_count::INT,
                pf_hvi_count::INT,
                pf_first_hvi_datetime_utc::TIMESTAMP WITH TIME ZONE,
                pf_last_hvi_datetime_utc::TIMESTAMP WITH TIME ZONE,
                pf_ms_to_first_hvi::INT,
                pf_ms_from_last_hvi::INT,
                pf_hvi_end_after_roi_end::BOOLEAN,
                pf_average_hvi_duration_ms::INT,
                pf_stated_duration_ms::INT,
                pf_durations_match::BOOLEAN,
                wb_start_datetime_utc::TIMESTAMP WITH TIME ZONE,
                wb_end_datetime_utc::TIMESTAMP WITH TIME ZONE,
                wb_lane_number::INT,
                wb_roi_id::VARCHAR,
                wb_cars_count::INT,
                wb_hvi_count::INT,
                wb_first_hvi_datetime_utc::TIMESTAMP WITH TIME ZONE,
                wb_last_hvi_datetime_utc::TIMESTAMP WITH TIME ZONE,
                wb_ms_to_first_hvi::INT,
                wb_ms_from_last_hvi::INT,
                wb_hvi_end_after_roi_end::BOOLEAN,
                wb_average_hvi_duration_ms::INT,
                wb_stated_duration_ms::INT,
                wb_durations_match::BOOLEAN,
                egress_start_datetime_utc::TIMESTAMP WITHOUT TIME ZONE,
                egress_end_datetime_utc::TIMESTAMP WITHOUT TIME ZONE,
                egress_lane_number::INT,
                egress_roi_id::VARCHAR,
                egress_cars_count::INT,
                egress_stated_duration_ms::INT,
                egress_durations_match::BOOLEAN,
                fin_start_datetime_utc::TIMESTAMP WITH TIME ZONE,
                fin_end_datetime_utc::TIMESTAMP WITH TIME ZONE,
                fin_lane_number::INT,
                fin_roi_id::VARCHAR,
                fin_cars_count::INT,
                fin_stated_duration_ms::INT,
                fin_durations_match::BOOLEAN,
                wb_ms_from_last_hvi_to_finish::INT,
                lost_asset_event_datetime_utc::TIMESTAMP WITH TIME ZONE,
                Queue_pre_start_duration_ms::INT,
                queue_pre_order_duration_ms::INT,
                queue_pre_pmt_duration_ms::INT,
                queue_pre_prsnt_duration_ms::INT,
                queue_pre_dlr_duration_ms::INT,
                queue_pre_pf_duration_ms::INT,
                queue_pre_wb_duration_ms::INT,
                queue_pre_egress_duration_ms::INT,
                queue_pre_fin_duration_ms::INT,
                total_ms_observed::INT,
                oelde_ms::INT,
                oslde_ms::INT,
                dslde_ms::INT,
                CURRENT_TIMESTAMP AS record_creation_time_utc
              FROM ' || schema_name || '.v_transformed_journey v
              WHERE meta_kinesis_datastream_arrival_datetime_utc >= CURRENT_TIMESTAMP - INTERVAL ''3 HOURS''
                AND NOT EXISTS (
                  SELECT 1
                  FROM ' || schema_name || '.fct_journey_from_view f
                  WHERE f.meta_kinesis_datastream_arrival_datetime_utc = v.meta_kinesis_datastream_arrival_datetime_utc
                    AND f.vehicle_id = v.vehicle_id
              )';

    -- Insert into the final table from the temporary table
     EXECUTE 'INSERT INTO ' || schema_name || '.fct_journey_from_view
                SELECT * FROM temp_fct_journey_table_'|| schema_name ||'';

    -- Drop the temporary table
    EXECUTE 'DROP TABLE temp_fct_journey_table_'|| schema_name ||'';

    -- Log on completion
    RAISE NOTICE 'Data uploaded from %.v_transformed_journey to %.fct_journey_from_view', schema_name, schema_name;
END;
$$
