CREATE
OR REPLACE VIEW "eyecue_tim_can"."v_drivethru_events" AS
SELECT
   md5(json_serialize(mv_drivethru_events.payload)) AS hash_id,
   mv_drivethru_events.meta_kinesis_datastream_arrival_datetime_utc,
   mv_drivethru_events.meta_redshift_refresh_datetime_utc,
   mv_drivethru_events.partition_key,
   mv_drivethru_events.shard_id,
   mv_drivethru_events.sequence_number,
   mv_drivethru_events.payload
FROM
   eyecue_tim_can.mv_drivethru_events mv_drivethru_events;