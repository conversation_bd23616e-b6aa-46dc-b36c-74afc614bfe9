CREATE TABLE eyecue_tim_can.fct_journey_from_view (
    meta_kinesis_datastream_arrival_datetime_utc timestamp without time zone ENCODE az64,
    meta_redshift_refresh_datetime_utc timestamp without time zone ENCODE az64,
    partition_key character varying(256) ENCODE lzo,
    shard_id character varying(20) ENCODE lzo,
    sequence_number character varying(128) ENCODE lzo,
    payload_variations character varying(65535) ENCODE lzo,
    vehicle_id character varying(256) ENCODE lzo,
    fingermark_id character varying(256) ENCODE raw,
    ghost boolean ENCODE raw,
    graph_history super,
    handheld_point_of_sale super,
    tz_offset character varying(10) ENCODE lzo,
    ingress_start_datetime_utc timestamp with time zone ENCODE az64,
    ingress_end_datetime_utc timestamp with time zone ENCODE az64,
    ingress_lane_number integer ENCODE az64,
    ingress_roi_id character varying(65535) ENCODE lzo,
    ingress_cars_count integer ENCODE az64,
    ingress_stated_duration_ms integer ENCODE az64,
    ingress_durations_match boolean ENCODE raw,
    start_start_datetime_utc timestamp with time zone ENCODE az64,
    start_end_datetime_utc timestamp with time zone ENCODE az64,
    start_lane_number integer ENCODE az64,
    start_roi_id character varying(65535) ENCODE lzo,
    start_cars_count integer ENCODE az64,
    start_stated_duration_ms integer ENCODE az64,
    start_durations_match boolean ENCODE raw,
    order_start_datetime_utc timestamp with time zone ENCODE raw,
    order_end_datetime_utc timestamp with time zone ENCODE az64,
    order_lane_number integer ENCODE az64,
    order_roi_id character varying(65535) ENCODE lzo,
    order_cars_count integer ENCODE az64,
    order_hvi_count integer ENCODE az64,
    order_first_hvi_datetime_utc timestamp with time zone ENCODE az64,
    order_last_hvi_datetime_utc timestamp with time zone ENCODE az64,
    order_ms_to_first_hvi integer ENCODE az64,
    order_ms_from_last_hvi integer ENCODE az64,
    order_hvi_end_after_roi_end boolean ENCODE raw,
    order_average_hvi_duration_ms integer ENCODE az64,
    order_stated_duration_ms integer ENCODE az64,
    order_durations_match boolean ENCODE raw,
    pmt_start_datetime_utc timestamp with time zone ENCODE az64,
    pmt_end_datetime_utc timestamp with time zone ENCODE az64,
    pmt_lane_number integer ENCODE az64,
    pmt_roi_id character varying(65535) ENCODE lzo,
    pmt_cars_count integer ENCODE az64,
    pmt_hvi_count integer ENCODE az64,
    pmt_first_hvi_datetime_utc timestamp with time zone ENCODE az64,
    pmt_last_hvi_datetime_utc timestamp with time zone ENCODE az64,
    pmt_ms_to_first_hvi integer ENCODE az64,
    pmt_ms_from_last_hvi integer ENCODE az64,
    pmt_hvi_end_after_roi_end boolean ENCODE raw,
    pmt_average_hvi_duration_ms integer ENCODE az64,
    pmt_stated_duration_ms integer ENCODE az64,
    pmt_durations_match boolean ENCODE raw,
    prsnt_start_datetime_utc timestamp with time zone ENCODE az64,
    prsnt_end_datetime_utc timestamp with time zone ENCODE az64,
    prsnt_lane_number integer ENCODE az64,
    prsnt_roi_id character varying(65535) ENCODE lzo,
    prsnt_cars_count integer ENCODE az64,
    prsnt_hvi_count integer ENCODE az64,
    prsnt_first_hvi_datetime_utc timestamp with time zone ENCODE az64,
    prsnt_last_hvi_datetime_utc timestamp with time zone ENCODE az64,
    prsnt_ms_to_first_hvi integer ENCODE az64,
    prsnt_ms_from_last_hvi integer ENCODE az64,
    prsnt_hvi_end_after_roi_end boolean ENCODE raw,
    prsnt_average_hvi_duration_ms integer ENCODE az64,
    prsnt_stated_duration_ms integer ENCODE az64,
    prsnt_durations_match boolean ENCODE raw,
    dlr_start_datetime_utc timestamp with time zone ENCODE az64,
    dlr_end_datetime_utc timestamp with time zone ENCODE az64,
    dlr_lane_number integer ENCODE az64,
    dlr_roi_id character varying(65535) ENCODE lzo,
    dlr_cars_count integer ENCODE az64,
    dlr_hvi_count integer ENCODE az64,
    dlr_first_hvi_datetime_utc timestamp with time zone ENCODE az64,
    dlr_last_hvi_datetime_utc timestamp with time zone ENCODE az64,
    dlr_ms_to_first_hvi integer ENCODE az64,
    dlr_ms_from_last_hvi integer ENCODE az64,
    dlr_hvi_end_after_roi_end boolean ENCODE raw,
    dlr_average_hvi_duration_ms integer ENCODE az64,
    dlr_stated_duration_ms integer ENCODE az64,
    dlr_durations_match boolean ENCODE raw,
    pf_start_datetime_utc timestamp with time zone ENCODE az64,
    pf_end_datetime_utc timestamp with time zone ENCODE az64,
    pf_lane_number integer ENCODE az64,
    pf_roi_id character varying(65535) ENCODE lzo,
    pf_cars_count integer ENCODE az64,
    pf_hvi_count integer ENCODE az64,
    pf_first_hvi_datetime_utc timestamp with time zone ENCODE az64,
    pf_last_hvi_datetime_utc timestamp with time zone ENCODE az64,
    pf_ms_to_first_hvi integer ENCODE az64,
    pf_ms_from_last_hvi integer ENCODE az64,
    pf_hvi_end_after_roi_end boolean ENCODE raw,
    pf_average_hvi_duration_ms integer ENCODE az64,
    pf_stated_duration_ms integer ENCODE az64,
    pf_durations_match boolean ENCODE raw,
    wb_start_datetime_utc timestamp with time zone ENCODE az64,
    wb_end_datetime_utc timestamp with time zone ENCODE az64,
    wb_lane_number integer ENCODE az64,
    wb_roi_id character varying(65535) ENCODE lzo,
    wb_cars_count integer ENCODE az64,
    wb_hvi_count integer ENCODE az64,
    wb_first_hvi_datetime_utc timestamp with time zone ENCODE az64,
    wb_last_hvi_datetime_utc timestamp with time zone ENCODE az64,
    wb_ms_to_first_hvi integer ENCODE az64,
    wb_ms_from_last_hvi integer ENCODE az64,
    wb_hvi_end_after_roi_end boolean ENCODE raw,
    wb_average_hvi_duration_ms integer ENCODE az64,
    wb_stated_duration_ms integer ENCODE az64,
    wb_durations_match boolean ENCODE raw,
    egress_start_datetime_utc timestamp with time zone ENCODE az64,
    egress_end_datetime_utc timestamp with time zone ENCODE az64,
    egress_lane_number integer ENCODE az64,
    egress_roi_id character varying(65535) ENCODE lzo,
    egress_cars_count integer ENCODE az64,
    egress_stated_duration_ms integer ENCODE az64,
    egress_durations_match boolean ENCODE raw,
    fin_start_datetime_utc timestamp with time zone ENCODE az64,
    fin_end_datetime_utc timestamp with time zone ENCODE az64,
    fin_lane_number integer ENCODE az64,
    fin_roi_id character varying(65535) ENCODE lzo,
    fin_cars_count integer ENCODE az64,
    fin_stated_duration_ms integer ENCODE az64,
    fin_durations_match boolean ENCODE raw,
    wb_ms_from_last_hvi_to_finish integer ENCODE az64,
    lost_asset_event_datetime_utc timestamp with time zone ENCODE az64,
    queue_pre_start_duration_ms integer ENCODE az64,
    queue_pre_order_duration_ms integer ENCODE az64,
    queue_pre_pmt_duration_ms integer ENCODE az64,
    queue_pre_prsnt_duration_ms integer ENCODE az64,
    queue_pre_dlr_duration_ms integer ENCODE az64,
    queue_pre_pf_duration_ms integer ENCODE az64,
    queue_pre_wb_duration_ms integer ENCODE az64,
    queue_pre_egress_duration_ms integer ENCODE az64,
    queue_pre_fin_duration_ms integer ENCODE az64,
    total_ms_observed integer ENCODE az64,
    oelde_ms integer ENCODE az64,
    oslde_ms integer ENCODE az64,
    dslde_ms integer ENCODE az64,
    record_creation_time_utc timestamp with time zone ENCODE az64
) DISTSTYLE AUTO
SORTKEY
    (fingermark_id, order_start_datetime_utc);