CREATE
OR REPLACE VIEW "eyecue_tim_can"."v_quicksight_fct_interaction" AS
SELECT
        _interaction.fingermark_id AS "fingermark id",
        _restaurant.restaurant_name AS "restaurant name",
        (
                (
                        _restaurant.restaurant_name:: text || ' (':: character varying:: text
                ) || _restaurant.restaurant_id:: character varying:: text
        ) || ')':: character varying:: text AS "restaurant identifier",
        _restaurant.latitude,
        _restaurant.longitude,
        _interaction.event_id AS "event identifier",
        _interaction.event_type AS "event type",
        _interaction.device_interaction AS "device interaction",
        _interaction.vehicle_id AS "vehicle identifier",
        _interaction.start_datetime_local AS "zone region entry time (local)",
        _interaction.end_datetime_local AS "zone region exit time (local)",
        _interaction.total_time AS "duration (seconds)",
        timezone(
                _restaurant.time_zone:: text,
                _interaction.camera_detection_datetime_utc
        ) AS "camera entry time (local)",
        _interaction.camera_id AS "camera identifier",
        _interaction.roi_id AS "zone region identifier",
        _interaction.roi_id_match AS "start and end zone region types match",
        _interaction.roi_type AS "zone identifier",
        _interaction.roi_type_match AS "start and end zone types match",
        _interaction.start_confidence AS "start confidence",
        _interaction.end_confidence AS "end confidence",
        _interaction.start_frames_life AS "start frames life",
        _interaction.end_frames_life AS "end frames life",
        _interaction.vehicle_type AS "type of vehicle identified",
        _interaction.lane_number AS "lane number",
        _interaction.graph_order AS "graph order",
        _interaction.overtaken AS "count of vehicles overtaken",
        _interaction.valid_journey AS "valid journey"
FROM
        (
                SELECT
                        fct_interaction_events.fingermark_id,
                        fct_interaction_events.event_id,
                        fct_interaction_events.event_type,
                        fct_interaction_events.device_interaction,
                        fct_interaction_events.vehicle_id,
                        fct_interaction_events.start_datetime_local,
                        fct_interaction_events.end_datetime_local,
                        fct_interaction_events.camera_detection_datetime_utc,
                        fct_interaction_events.camera_id,
                        fct_interaction_events.roi_id,
                        fct_interaction_events.roi_id_match,
                        fct_interaction_events.roi_type,
                        fct_interaction_events.roi_type_match,
                        fct_interaction_events.total_time,
                        fct_interaction_events.start_confidence,
                        fct_interaction_events.end_confidence,
                        fct_interaction_events.start_frames_life,
                        fct_interaction_events.end_frames_life,
                        fct_interaction_events.vehicle_type,
                        fct_interaction_events.lane_number,
                        fct_interaction_events.graph_order,
                        fct_interaction_events.overtaken,
                        fct_interaction_events.valid_journey
                FROM
                        eyecue_tim_can.fct_interaction_events
        ) _interaction
        LEFT JOIN (
                SELECT
                        dim_restaurant.fingermark_id,
                        dim_restaurant.restaurant_name,
                        dim_restaurant.restaurant_id,
                        dim_restaurant.time_zone,
                        dim_restaurant.address,
                        dim_restaurant.suburb,
                        dim_restaurant.city,
                        dim_restaurant.state,
                        dim_restaurant.post_code,
                        dim_restaurant.country,
                        dim_restaurant.latitude,
                        dim_restaurant.longitude
                FROM
                        eyecue_tim_can.dim_restaurant
        ) _restaurant USING (fingermark_id);