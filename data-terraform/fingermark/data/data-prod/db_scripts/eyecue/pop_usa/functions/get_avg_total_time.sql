CREATE OR REPLACE FUNCTION public.get_avg_total_time(json_string character varying(65535))
 RETURNS double precision LANGUAGE plpythonu
 STABLE
AS $$
    import json
    total_time_sum = 0
    total_time_count = 0
    try:
        json_object = json.loads(json_string)
    except json.JSONDecodeError:
        return None

    for data in json_object: 
        if "total_time" in data and data["total_time"] is not None:
            total_time_sum += data["total_time"]
            total_time_count += 1
    if total_time_count == 0:
        return None

    average_total_time = total_time_sum / total_time_count
    return round(average_total_time,6)  
$$
