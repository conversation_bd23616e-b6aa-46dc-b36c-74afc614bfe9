CREATE TABLE eyecue_pop_usa.fct_queue_zone (
    functional_zone_auto_id integer NOT NULL identity(1, 1) ENCODE az64,
    record_hash_id character varying(256) NOT NULL ENCODE lzo,
    fingermark_id character varying(256) ENCODE lzo
    distkey
,
        event_type character varying(256) ENCODE lzo,
        version double precision ENCODE raw,
        functional_zone character varying(256) ENCODE lzo,
        queue_level integer ENCODE az64,
        queuing_time_ms integer ENCODE az64,
        datetime_utc timestamp with time zone ENCODE raw,
        meta_kinesis_datastream_arrival_datetime_utc timestamp with time zone ENCODE az64,
        PRIMARY KEY (functional_zone_auto_id)
) DISTSTYLE KEY
SORTKEY
    (datetime_utc, record_hash_id);