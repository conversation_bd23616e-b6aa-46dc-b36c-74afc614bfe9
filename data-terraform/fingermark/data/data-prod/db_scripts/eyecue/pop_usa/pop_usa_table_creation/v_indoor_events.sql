CREATE
OR REPLACE VIEW "eyecue_pop_usa"."v_indoor_events" AS
SELECT
   md5(json_serialize(mv_indoor_events.payload)) AS hash_id,
   mv_indoor_events.meta_kinesis_datastream_arrival_datetime_utc,
   mv_indoor_events.meta_redshift_refresh_datetime_utc,
   mv_indoor_events.partition_key,
   mv_indoor_events.shard_id,
   mv_indoor_events.sequence_number,
   mv_indoor_events.payload
FROM
   eyecue_pop_usa.mv_indoor_events mv_indoor_events;