CREATE
OR REPLACE VIEW "eyecue_pop_usa"."v_quicksight_fct_roi" AS
SELECT
        _roi.fingermark_id AS "fingermark id",
        _restaurant.restaurant_name AS "restaurant name",
        (
                (
                        _restaurant.restaurant_name:: text || ' (':: character varying:: text
                ) || _restaurant.restaurant_id:: character varying:: text
        ) || ')':: character varying:: text AS "restaurant identifier",
        _restaurant.latitude,
        _restaurant.longitude,
        _roi.event_id AS "event identifier",
        _roi.event_type AS "event type",
        _roi.vehicle_id AS "vehicle identifier",
        _roi.start_datetime_local AS "zone region entry time (local)",
        _roi.end_datetime_local AS "zone region exit time (local)",
        _roi.total_time AS "duration (seconds)",
        timezone(
                _restaurant.time_zone:: text,
                _roi.camera_detection_datetime_utc
        ) AS "camera entry time (local)",
        _roi.camera_id AS "camera identifier",
        _roi.roi_id AS "zone region identifier",
        _roi.roi_id_match AS "start and end zone region types match",
        _roi.roi_type AS "zone identifier",
        _roi.roi_type_match AS "start and end zone types match",
        _roi.lost_asset AS "lost asset",
        _roi.start_confidence AS "start confidence",
        _roi.end_confidence AS "end confidence",
        _roi.start_frames_life AS "start frames life",
        _roi.end_frames_life AS "end frames life",
        _roi.car_count_reid_pool AS "count of vehicles in reidentification pool",
        _roi.vehicle_type AS "type of vehicle identified",
        _roi.lane_number AS "lane number",
        _roi.graph_order AS "graph order",
        _roi.overtaken AS "count of vehicles overtaken",
        _roi.functional_zone AS "queue zone",
        _roi.queue_level AS "queue level",
        _roi.valid_journey AS "valid journey"
FROM
        (
                SELECT
                        fct_roi_events.fingermark_id,
                        fct_roi_events.event_id,
                        fct_roi_events.event_type,
                        fct_roi_events.vehicle_id,
                        fct_roi_events.start_datetime_local,
                        fct_roi_events.end_datetime_local,
                        fct_roi_events.camera_detection_datetime_utc,
                        fct_roi_events.camera_id,
                        fct_roi_events.roi_id,
                        fct_roi_events.roi_id_match,
                        fct_roi_events.roi_type,
                        fct_roi_events.roi_type_match,
                        fct_roi_events.lost_asset,
                        fct_roi_events.total_time,
                        fct_roi_events.start_confidence,
                        fct_roi_events.end_confidence,
                        fct_roi_events.start_frames_life,
                        fct_roi_events.end_frames_life,
                        fct_roi_events.car_count_reid_pool,
                        fct_roi_events.vehicle_type,
                        fct_roi_events.lane_number,
                        fct_roi_events.graph_order,
                        fct_roi_events.overtaken,
                        fct_roi_events.functional_zone,
                        fct_roi_events.queue_level,
                        fct_roi_events.valid_journey
                FROM
                        eyecue_pop_usa.fct_roi_events
        ) _roi
        LEFT JOIN (
                SELECT
                        dim_restaurant.fingermark_id,
                        dim_restaurant.restaurant_name,
                        dim_restaurant.restaurant_id,
                        dim_restaurant.time_zone,
                        dim_restaurant.address,
                        dim_restaurant.suburb,
                        dim_restaurant.city,
                        dim_restaurant.state,
                        dim_restaurant.post_code,
                        dim_restaurant.country,
                        dim_restaurant.latitude,
                        dim_restaurant.longitude
                FROM
                        eyecue_pop_usa.dim_restaurant
        ) _restaurant USING (fingermark_id);