create MATERIALIZED view eyecue_pop_usa.mv_last_1_day_departure_events as with data as (
    select
        *,
        ROW_NUMBER() OVER (
            PARTITION BY payload
            ORDER BY
                "meta_kinesis_datastream_arrival_datetime_utc" DESC NULLS FIRST
        ) as rn
    from
        eyecue_pop_usa.mv_drivethru_events
    where
        1 = 1
        and payload.event_type = 'departure'
        and meta_kinesis_datastream_arrival_datetime_utc >= trunc(sysdate) - 1
)
select
    md5(json_serialize(payload)) hash_id,
    meta_kinesis_datastream_arrival_datetime_utc,
    payload.client_name:: text AS client_name,
    payload.store_id:: text AS fingermark_id,
    payload.camera_id:: text AS camera_id,
    payload.event_type:: text AS event_type,
    payload.roi_type:: text AS roi_type,
    payload.roi_id:: text AS roi_id,
    payload.timestamp:: TIMESTAMPTZ as event_time_at_utc,
    payload.timestamp:: TIMESTAMP as event_time_at_local,
    payload.lane:: int AS lane_number
from
    data
where
    rn = 1
;