CREATE TABLE eyecue_pop_usa.fct_departure_events (
    hash_id character varying(32) NOT NULL ENCODE lzo,
    meta_kinesis_datastream_arrival_datetime_utc timestamp with time zone ENCODE az64,
    fingermark_id character varying(255) ENCODE raw
    distkey
,
        camera_id character varying(255) ENCODE lzo,
        event_type character varying(100) ENCODE lzo,
        roi_type character varying(100) ENCODE lzo,
        roi_id character varying(255) ENCODE lzo,
        event_time_at_utc timestamp with time zone ENCODE raw,
        event_time_at_local timestamp without time zone ENCODE az64,
        lane_number integer ENCODE az64,
        record_creation_time_utc timestamp with time zone ENCODE az64
) DISTSTYLE KEY
SORTKEY
    (event_time_at_utc, fingermark_id);