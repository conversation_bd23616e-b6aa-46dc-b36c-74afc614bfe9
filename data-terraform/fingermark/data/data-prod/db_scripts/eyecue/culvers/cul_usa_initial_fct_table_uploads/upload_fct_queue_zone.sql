create temp TABLE temp_queue_zone_events as (
    with data as (
        select
            *,
            ROW_NUMBER() OVER (
                PARTITION BY payload
                ORDER BY
                    "approximate_arrival_timestamp" DESC NULLS FIRST
            ) as rn
        from
            eyecue_cul_usa.mv_cul_usa_eventstream
        where
            1 = 1
            and payload.event_type:: text = 'danger-zone'
        )
    select
        md5(json_serialize(payload)) AS hash_key,
        approximate_arrival_timestamp AS meta_kinesis_datastream_arrival_datetime_utc,
        payload,
        JSON_PARSE(
            JSON_EXTRACT_PATH_TEXT(json_serialize(payload), 'functional_zones', true)
        ) AS functional_zones
    from
        data
    where
        rn = 1
);


INSERT INTO eyecue_cul_usa.fct_queue_zone (record_hash_id, fingermark_id, event_type, version, functional_zone, queue_level, queuing_time_ms, datetime_utc, meta_kinesis_datastream_arrival_datetime_utc)
(
    SELECT

    t.hash_key as record_hash_id,
    t.payload.store_id:: text AS fingermark_id,
    t.payload.event_type:: text AS event_type,
    t.payload.version:: FLOAT8 AS version,
    key:: text AS functional_zone,
    value.queue_level:: INT AS queue_level,
    ROUND(value.queuing_time * 1000):: INT AS queuing_time_ms,
    value.time_utc:: timestamptz AS datetime_utc,
    t.meta_kinesis_datastream_arrival_datetime_utc
FROM
    temp_queue_zone_events AS t,
    UNPIVOT t.functional_zones AS value AT key
);

DROP TABLE temp_queue_zone_events;