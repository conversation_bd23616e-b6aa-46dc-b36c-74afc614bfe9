create temp TABLE temp_roi_end_events as (
    with data as (
        select
            *,
            ROW_NUMBER() OVER (
                PARTITION BY payload
                ORDER BY
                    "approximate_arrival_timestamp" DESC NULLS FIRST
            ) as rn
        from
            eyecue_cul_usa.mv_cul_usa_eventstream
        where
            1 = 1
            and payload.event_type:: text = 'roi'
            and payload.roi_event:: text = 'end'
        )
    select
        payload.client_name:: text AS client_name,
        payload.store_id:: text AS fingermark_id,
        payload.event_id:: text AS event_id,
        payload.event_type::text AS event_type,
        payload.event_time_tz:: TIMESTAMPTZ AS end_datetime_utc,
        payload.event_time_tz:: TIMESTAMP AS end_datetime_local,
        payload.creation_time:: TIMESTAMP AS camera_detection_datetime_utc,
        payload.roi_id:: text AS roi_id,
        payload.roi_type:: text AS roi_type,
        payload.total_time:: Double precision AS total_time,
        payload.camera_id:: text AS camera_id,
        payload.confidence:: Double precision AS end_confidence,
        payload.frames_life:: INT AS end_frames_life,
        payload.lane_number:: INT AS lane_number,
        payload.car_count:: INT AS car_count_reid_pool,
        payload.order:: text AS graph_order,
        payload.overtaken:: INT AS overtaken,
        payload.valid_journey:: BOOLEAN AS valid_journey,
        payload.label:: text AS vehicle_type,
        payload.functional_zone:: text AS functional_zone,
        payload.queue_level:: INT AS queue_level,
        payload.eyeq_server_version:: text AS eyeq_server_version,
        payload.tracker_version:: text AS tracker_version,
        payload.vehicle_id:: text AS vehicle_id,
        approximate_arrival_timestamp AS meta_kinesis_datastream_arrival_datetime_utc
    from
        data
    where
        rn = 1
);

create temp TABLE temp_roi_start_events as (
    with data as (
        select
            *,
            ROW_NUMBER() OVER (
                PARTITION BY payload
                ORDER BY
                    "approximate_arrival_timestamp" DESC NULLS FIRST
            ) as rn
        from
            eyecue_cul_usa.mv_cul_usa_eventstream
        where
            1 = 1
            and payload.event_type:: text = 'roi'
            and payload.roi_event:: text = 'start'
    )
    select
        payload.client_name:: text AS client_name,
        payload.store_id:: text AS fingermark_id,
        payload.event_id:: text AS event_id,
        payload.event_type::text AS event_type,
        payload.event_time_tz:: TIMESTAMPTZ AS start_datetime_utc,
        payload.event_time_tz:: TIMESTAMP AS start_datetime_local,
        payload.creation_time:: TIMESTAMP AS camera_detection_datetime_utc,
        payload.roi_id:: text AS roi_id,
        payload.roi_type:: text AS roi_type,
        payload.total_time:: Double precision AS total_time,
        payload.camera_id:: text AS camera_id,
        payload.confidence:: double precision AS start_confidence,
        payload.frames_life:: INT AS start_frames_life,
        payload.lane_number:: integer AS lane_number,
        payload.car_count:: integer AS car_count_reid_pool,
        payload.order:: text AS graph_order,
        payload.overtaken:: integer AS overtaken,
        payload.valid_journey:: BOOLEAN AS valid_journey,
        payload.label:: text AS vehicle_type,
        payload.functional_zone:: text AS functional_zone,
        payload.queue_level:: integer AS queue_level,
        payload.eyeq_server_version:: text AS eyeq_server_version,
        payload.tracker_version:: text AS tracker_version,
        payload.vehicle_id:: text AS vehicle_id,
        approximate_arrival_timestamp AS meta_kinesis_datastream_arrival_datetime_utc
    from
        data
    where
        rn = 1
);

INSERT INTO eyecue_cul_usa.fct_roi_events (
    SELECT 
        MD5(CONCAT(CONCAT(COALESCE(ts.fingermark_id, te.fingermark_id, ''), COALESCE(ts.event_id, te.event_id, '')), COALESCE(ts.start_datetime_utc, te.end_datetime_utc))) AS hash_key,
        COALESCE(ts.fingermark_id, te.fingermark_id) AS fingermark_id,
        COALESCE(ts.event_id, te.event_id) AS event_id,
        COALESCE(ts.event_type, te.event_type) AS event_type,
        COALESCE(ts.vehicle_id, te.vehicle_id) AS vehicle_id,
        ts.start_datetime_utc,
        te.end_datetime_utc,
        ts.start_datetime_local,
        te.end_datetime_local,
        ts.camera_detection_datetime_utc,
        COALESCE(ts.camera_id, te.camera_id) AS camera_id,
        COALESCE(ts.roi_id, te.roi_id) AS roi_id,
        CASE WHEN ts.roi_id = te.roi_id THEN TRUE ELSE FALSE END AS roi_id_match, 
        COALESCE(ts.roi_type, te.roi_type) AS roi_type,
        CASE WHEN ts.roi_type = te.roi_type THEN TRUE ELSE FALSE END AS roi_type_match,
        CASE WHEN te.roi_type = 'LOST_ASSET' THEN TRUE ELSE FALSE END AS lost_asset,
        GREATEST(ts.total_time,te.total_time)AS total_time,
        ts.start_confidence AS start_confidence,
        te.end_confidence AS end_confidence,
        ts.start_frames_life AS start_frames_life,
        te.end_frames_life AS end_frames_life,
        GREATEST(ts.car_count_reid_pool, te.car_count_reid_pool) AS car_count_reid_pool,
        COALESCE(ts.vehicle_type, te.vehicle_type) AS vehicle_type,
        COALESCE(ts.lane_number, te.lane_number) AS lane_number,
        ts.graph_order AS graph_order,
        GREATEST(ts.overtaken, te.overtaken) AS overtaken,
        ts.functional_zone AS functional_zone,
        ts.queue_level AS queue_level,
        CASE WHEN ts.valid_journey IS TRUE AND te.valid_journey IS TRUE THEN TRUE ELSE FALSE END AS valid_journey,
        COALESCE(ts.tracker_version, te.tracker_version) AS tracker_version,
        COALESCE(ts.eyeq_server_version, te.eyeq_server_version) AS eyeq_server_version,
        GETDATE() AS record_creation_time_utc,
        COALESCE(ts.meta_kinesis_datastream_arrival_datetime_utc, te.meta_kinesis_datastream_arrival_datetime_utc) as meta_kinesis_datastream_arrival_datetime_utc
    FROM 
        temp_roi_start_events ts
    FULL OUTER JOIN 
        temp_roi_end_events te
    ON 
        ts.event_id = te.event_id
);

DROP TABLE temp_roi_start_events;
DROP TABLE temp_roi_end_events;