CREATE TABLE eyecue_cul_usa.fct_hvi_events (
    hash_id character varying(255) NOT NULL ENCODE lzo,
    fingermark_id character varying(255) ENCODE raw,
    event_id character varying(255) ENCODE lzo
    distkey
,
        event_type character varying(255) ENCODE lzo,
        vehicle_id character varying(255) ENCODE lzo,
        start_datetime_utc timestamp with time zone ENCODE raw,
        end_datetime_utc timestamp with time zone ENCODE raw,
        start_datetime_local timestamp without time zone ENCODE az64,
        end_datetime_local timestamp without time zone ENCODE az64,
        camera_detection_datetime_utc timestamp with time zone ENCODE az64,
        camera_id character varying(100) ENCODE lzo,
        roi_id character varying(255) ENCODE lzo,
        roi_id_match boolean ENCODE raw,
        roi_type character varying(100) ENCODE lzo,
        roi_type_match boolean ENCODE raw,
        total_time double precision ENCODE raw,
        start_confidence double precision ENCODE raw,
        end_confidence double precision ENCODE raw,
        start_frames_life character varying(50) ENCODE lzo,
        end_frames_life character varying(50) ENCODE lzo,
        vehicle_type character varying(255) ENCODE lzo,
        lane_number integer ENCODE az64,
        graph_order character varying(255) ENCODE lzo,
        overtaken integer ENCODE az64,
        valid_journey boolean ENCODE raw,
        tracker_version character varying(50) ENCODE lzo,
        eyeq_server_version character varying(50) ENCODE lzo,
        record_creation_time_utc timestamp with time zone ENCODE az64,
        meta_kinesis_datastream_arrival_datetime_utc timestamp with time zone ENCODE az64,
        PRIMARY KEY (hash_id)
) DISTSTYLE KEY
SORTKEY
    (
        fingermark_id,
        start_datetime_utc,
        end_datetime_utc
    );