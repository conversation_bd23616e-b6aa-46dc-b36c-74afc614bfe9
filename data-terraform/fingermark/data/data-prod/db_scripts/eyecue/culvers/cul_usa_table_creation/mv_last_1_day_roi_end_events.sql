create MATERIALIZED view eyecue_cul_usa.mv_last_1_day_roi_end_events as (
    with data as (
        select
            *,
            ROW_NUMBER() OVER (
                PARTITION BY payload
                ORDER BY
                    "meta_kinesis_datastream_arrival_datetime_utc" DESC NULLS FIRST
            ) as rn
        from
            eyecue_cul_usa.mv_eventstream
        where
            1 = 1
            and payload.event_type:: text = 'roi'
            and payload.roi_event:: text = 'end'
            and meta_kinesis_datastream_arrival_datetime_utc >= (GETDATE() - INTERVAL '1 day')
    )
    select
        payload.client_name:: text AS client_name,
        payload.store_id:: text AS fingermark_id,
        payload.event_id:: text AS event_id,
        payload.event_type:: text AS event_type,
        payload.event_time_tz:: TIMESTAMPTZ AS end_datetime_utc,
        payload.event_time_tz:: TIMESTAMP AS end_datetime_local,
        payload.creation_time:: TIMESTAMP AS camera_detection_datetime_utc,
        payload.roi_id:: text AS roi_id,
        payload.roi_type:: text AS roi_type,
        payload.total_time:: Double precision AS total_time,
        payload.camera_id:: text AS camera_id,
        payload.confidence:: double precision AS end_confidence,
        payload.frames_life:: INT AS end_frames_life,
        payload.lane_number:: integer AS lane_number,
        payload.car_count:: integer AS car_count_reid_pool,
        payload.order:: text AS graph_order,
        payload.overtaken:: integer AS overtaken,
        payload.valid_journey:: BOOLEAN AS valid_journey,
        payload.label:: text AS vehicle_type,
        payload.functional_zone:: text AS functional_zone,
        payload.queue_level:: integer AS queue_level,
        payload.eyeq_server_version:: text AS eyeq_server_version,
        payload.tracker_version:: text AS tracker_version,
        payload.vehicle_id:: text AS vehicle_id,
        meta_kinesis_datastream_arrival_datetime_utc
    from
        data
    where
        rn = 1
);