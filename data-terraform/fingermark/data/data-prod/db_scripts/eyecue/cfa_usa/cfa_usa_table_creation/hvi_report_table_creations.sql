CREATE TABLE eyecue_cfa_usa.flattened_hvi_events_start (
    flattened_hvi_event_start_id bigint IDENTITY (1, 1) PRIMARY KEY,
    meta_kinesis_datastream_arrival_datetime_utc TIMESTAMP,
    meta_redshift_refresh_datetime_utc TIMESTAMP,
    md5_hash_id VARCHAR,
    artificial boolean,
    camera_id VARCHAR,
    complete boolean,
    confidence double precision,
    creation_time TIMESTAMP,
    event_id VARCHAR,
    event_time TIMESTAMP,
    event_time_tz TIMESTAMP,
    eyeq_server_version VARCHAR,
    frames_life integer,
    hvi_event boolean,
    label VARCHAR,
    lane_number integer,
    "order" VARCHAR,
    overtaken integer,
    person_id VARCHAR,
    roi_event VARCHAR,
    roi_id VARCHAR,
    roi_type VARCHAR,
    total_time double precision,
    tracker_id VARCHAR,
    tracker_version VARCHAR,
    valid_journey boolean,
    vehicle_db_id double precision,
    vehicle_id VARCHAR,
    store_id VARCHAR,
    frame_id VARCHAR,
    event_type VARCHAR,
    client_name VA<PERSON>HAR,
    coordinates_mqtt_x1 double precision,
    coordinates_mqtt_x2 double precision,
    coordinates_mqtt_xc double precision,
    coordinates_mqtt_y1 double precision,
    coordinates_mqtt_y2 double precision,
    coordinates_mqtt_yc double precision
) DISTSTYLE AUTO SORTKEY (
    roi_event,
    meta_kinesis_datastream_arrival_datetime_utc
);

--

CREATE TABLE eyecue_cfa_usa.flattened_hvi_events_end (
    flattened_hvi_event_end_id bigint IDENTITY (1, 1) PRIMARY KEY,
    meta_kinesis_datastream_arrival_datetime_utc TIMESTAMP,
    meta_redshift_refresh_datetime_utc TIMESTAMP,
    md5_hash_id VARCHAR,
    artificial boolean,
    camera_id VARCHAR,
    complete boolean,
    confidence double precision,
    creation_time TIMESTAMP,
    event_id VARCHAR,
    event_time TIMESTAMP,
    event_time_tz TIMESTAMP,
    eyeq_server_version VARCHAR,
    frames_life integer,
    hvi_event boolean,
    label VARCHAR,
    lane_number integer,
    "order" VARCHAR,
    overtaken integer,
    person_id VARCHAR,
    roi_event VARCHAR,
    roi_id VARCHAR,
    roi_type VARCHAR,
    total_time double precision,
    tracker_id VARCHAR,
    tracker_version VARCHAR,
    valid_journey boolean,
    vehicle_db_id double precision,
    vehicle_id VARCHAR,
    store_id VARCHAR,
    frame_id VARCHAR,
    event_type VARCHAR,
    client_name VARCHAR,
    coordinates_mqtt_x1 double precision,
    coordinates_mqtt_x2 double precision,
    coordinates_mqtt_xc double precision,
    coordinates_mqtt_y1 double precision,
    coordinates_mqtt_y2 double precision,
    coordinates_mqtt_yc double precision
) DISTSTYLE AUTO SORTKEY (
    roi_event,
    meta_kinesis_datastream_arrival_datetime_utc
);

--

CREATE TABLE eyecue_cfa_usa.flattened_roi_events_start (
    flattened_roi_event_start_id bigint IDENTITY (1, 1) PRIMARY KEY,
    meta_kinesis_datastream_arrival_datetime_utc TIMESTAMP,
    meta_redshift_refresh_datetime_utc TIMESTAMP,
    md5_hash_id VARCHAR,
    artificial boolean,
    camera_id VARCHAR,
    complete boolean,
    confidence double precision,
    creation_time TIMESTAMP,
    event_id VARCHAR,
    event_time TIMESTAMP,
    event_time_tz TIMESTAMP,
    eyeq_server_version VARCHAR,
    frames_life integer,
    hvi_event boolean,
    label VARCHAR,
    lane_number integer,
    "order" VARCHAR,
    overtaken integer,
    person_id VARCHAR,
    roi_event VARCHAR,
    roi_id VARCHAR,
    roi_type VARCHAR,
    total_time double precision,
    tracker_id VARCHAR,
    tracker_version VARCHAR,
    valid_journey boolean,
    vehicle_db_id double precision,
    vehicle_id VARCHAR,
    store_id VARCHAR,
    frame_id VARCHAR,
    event_type VARCHAR,
    client_name VARCHAR,
    coordinates_mqtt_x1 double precision,
    coordinates_mqtt_x2 double precision,
    coordinates_mqtt_xc double precision,
    coordinates_mqtt_y1 double precision,
    coordinates_mqtt_y2 double precision,
    coordinates_mqtt_yc double precision
) DISTSTYLE AUTO SORTKEY (
    roi_event,
    meta_kinesis_datastream_arrival_datetime_utc
);

--

CREATE TABLE eyecue_cfa_usa.flattened_roi_events_end (
    flattened_roi_event_end_id bigint IDENTITY (1, 1) PRIMARY KEY,
    meta_kinesis_datastream_arrival_datetime_utc TIMESTAMP,
    meta_redshift_refresh_datetime_utc TIMESTAMP,
    md5_hash_id VARCHAR,
    artificial boolean,
    camera_id VARCHAR,
    complete boolean,
    confidence double precision,
    creation_time TIMESTAMP,
    event_id VARCHAR,
    event_time TIMESTAMP,
    event_time_tz TIMESTAMP,
    eyeq_server_version VARCHAR,
    frames_life integer,
    hvi_event boolean,
    label VARCHAR,
    lane_number integer,
    "order" VARCHAR,
    overtaken integer,
    person_id VARCHAR,
    roi_event VARCHAR,
    roi_id VARCHAR,
    roi_type VARCHAR,
    total_time double precision,
    tracker_id VARCHAR,
    tracker_version VARCHAR,
    valid_journey boolean,
    vehicle_db_id double precision,
    vehicle_id VARCHAR,
    store_id VARCHAR,
    frame_id VARCHAR,
    event_type VARCHAR,
    client_name VARCHAR,
    coordinates_mqtt_x1 double precision,
    coordinates_mqtt_x2 double precision,
    coordinates_mqtt_xc double precision,
    coordinates_mqtt_y1 double precision,
    coordinates_mqtt_y2 double precision,
    coordinates_mqtt_yc double precision
) DISTSTYLE AUTO SORTKEY (
    roi_event,
    meta_kinesis_datastream_arrival_datetime_utc
);

--

CREATE TABLE hvi_reporting_automation (
    hvi_reporting_automation_id bigint IDENTITY (1, 1) PRIMARY KEY,
    hvi_count INT,
    avg_interaction_duration FLOAT8,
    min_interaction_duration FLOAT8,
    max_interaction_duration FLOAT8,
    first_start_interaction_time_local TIMESTAMP,
    last_end_interaction_time_local TIMESTAMP,
    start_start_to_end_end_interaction_duration_seconds FLOAT8,
    roi_start_to_hvi_start_duration FLOAT8,
    hvi_end_to_roi_end_duration FLOAT8,
    roi_duration FLOAT8,
    avg_duration_between_hvis FLOAT8,
    lane_number INT,
    roi_id VARCHAR(256),
    roi_type VARCHAR(256),
    vehicle_db_id INT,
    vehicle__id VARCHAR(256),
    store_id VARCHAR(256),
    store_name VARCHAR(256),
    event_time_local TIMESTAMP,
    roi_start TIMESTAMP,
    roi_end TIMESTAMP,
    mapped_function VARCHAR(256)
);