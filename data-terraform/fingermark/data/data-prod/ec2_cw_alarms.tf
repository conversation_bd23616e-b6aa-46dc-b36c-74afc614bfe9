module "ec2_instance_cw_alarms_ap_southeast_2" {
  source         = "git::*****************:fingermarkltd/fingermark-terraform.git//modules/ec2_instance_cw_alarms?ref=master"
  sns_topic_arns = [var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]]
  cw_alarm_config_ec2_by_tags_cpu_util_high = {
    "quicksight-report-generator-bkg-nzl-prod" = { instance_tags = { Name = "quicksight-report-generator-bkg-nzl-prod" } }
    "bastion-prod-ap-southeast-2"              = { instance_tags = { Name = "bastion-prod-ap-southeast-2" } }
    "bastion-v2-prod-ap-southeast-2"           = { instance_tags = { Name = "bastion-v2-prod-ap-southeast-2" } }
    "vpn-v2-prod-ap-southeast-2"               = { instance_tags = { Name = "vpn-v2-prod-ap-southeast-2" } }
    "app-flow-ga-data"                         = { instance_tags = { Name = "app-flow-ga-data" } }
    "grafana-jupyterlab"                       = { instance_tags = { Name = "grafana & JupyterLab" } }
    "tableau-bridge"                           = { instance_tags = { Name = "tableau-bridge" } }
    "i-040879bdde742bf70"                      = { instance_tags = { Name = "i-040879bdde742bf70" } }
    "tableau_bridge-prod-ap-southeast-2"       = { instance_tags = { Name = "tableau_bridge-prod-ap-southeast-2" } }
  }
  cw_alarm_config_ec2_by_tags_cpu_util_low = {}
  tags = {
    Environment = "Production"
    Application = "EC2-Monitoring"
    Squad       = "Platform team"
  }
}

module "ec2_instance_cw_alarms_us_east_1" {
  providers      = { aws = aws.us_e1 }
  source         = "git::*****************:fingermarkltd/fingermark-terraform.git//modules/ec2_instance_cw_alarms?ref=master"
  sns_topic_arns = [var.cw_alarms_sns_topic_arns_region_lookup["us-east-1"]]
  cw_alarm_config_ec2_by_tags_cpu_util_high = {
    "bastion-v2-prod-us-east-1"                       = { instance_tags = { Name = "bastion-v2-prod-us-east-1" } }
    "vpn-v2-prod-us-east-1"                           = { instance_tags = { Name = "vpn-v2-prod-us-east-1" } }
    "quicksight-report-generator-cfa-usa-weekly-prod" = { instance_tags = { Name = "quicksight-report-generator-cfa-usa-weekly-prod" } }
    "quicksight-report-generator-cfa-usa-prod"        = { instance_tags = { Name = "quicksight-report-generator-cfa-usa-prod" } }
    "tableau_bridge-prod-us-east-1"                   = { instance_tags = { Name = "tableau_bridge-prod-us-east-1" } }
  }
  cw_alarm_config_ec2_by_tags_cpu_util_low = {}
  tags = {
    Environment = "Production"
    Application = "EC2-Monitoring"
    Squad       = "Platform team"
  }
}

module "ec2_instance_cw_alarms_us_west_1" {
  providers      = { aws = aws.usw1 }
  source         = "git::*****************:fingermarkltd/fingermark-terraform.git//modules/ec2_instance_cw_alarms?ref=master"
  sns_topic_arns = [var.cw_alarms_sns_topic_arns_region_lookup["us-west-1"]]
  cw_alarm_config_ec2_by_tags_cpu_util_high = {
    "bastion-v2-prod-us-west-1"                       = { instance_tags = { Name = "bastion-v2-prod-us-west-1" } }
    "vpn-v2-prod-us-west-1"                           = { instance_tags = { Name = "vpn-v2-prod-us-west-1" } }
    "quicksight-report-generator-cfa-usa-weekly-prod" = { instance_tags = { Name = "quicksight-report-generator-cfa-usa-weekly-prod" } }
    "quicksight-report-generator-cfa-usa-prod"        = { instance_tags = { Name = "quicksight-report-generator-cfa-usa-prod" } }
    "bastion-prod-us-west-1"                          = { instance_tags = { Name = "bastion-prod-us-west-1" } }
    "tableau_bridge-prod-us-west-1"                   = { instance_tags = { Name = "tableau_bridge-prod-us-west-1" } }
  }
  cw_alarm_config_ec2_by_tags_cpu_util_low = {}
  tags = {
    Environment = "Production"
    Application = "EC2-Monitoring"
    Squad       = "Platform team"
  }
}

module "ec2_instance_cw_alarms_us_west_2" {
  providers      = { aws = aws.us_w2 }
  source         = "git::*****************:fingermarkltd/fingermark-terraform.git//modules/ec2_instance_cw_alarms?ref=master"
  sns_topic_arns = [var.cw_alarms_sns_topic_arns_region_lookup["us-west-2"]]
  cw_alarm_config_ec2_by_tags_cpu_util_high = {
    "bastion-v2-prod-us-west-2"     = { instance_tags = { Name = "bastion-v2-prod-us-west-2" } }
    "vpn-v2-prod-us-west-2"         = { instance_tags = { Name = "vpn-v2-prod-us-west-2" } }
    "tableau_bridge-prod-us-west-2" = { instance_tags = { Name = "tableau_bridge-prod-us-west-2" } }
  }
  cw_alarm_config_ec2_by_tags_cpu_util_low = {}
  tags = {
    Environment = "Production"
    Application = "EC2-Monitoring"
    Squad       = "Platform team"
  }
}
