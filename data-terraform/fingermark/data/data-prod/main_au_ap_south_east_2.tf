# Setup VPC and 3 public subnets and 3 private subnets
module "data_vpc_au" {
  source                 = "../../../modules/data_vpc"
  vpc_cidr_block         = "********/16"
  environment_code       = var.APP_STAGE
  vpc_name               = "data-${var.APP_STAGE}"
  aws_availability_zones = ["ap-southeast-2a", "ap-southeast-2b", "ap-southeast-2c"]
  tags                   = merge(local.common_tags, { Stack = "Data-VPC", Application = "Data-Infrastructure" })
  extra_ingress_rules = [
    {
      description      = ""
      from_port        = 6379
      to_port          = 6379
      protocol         = "tcp"
      cidr_blocks      = []
      ipv6_cidr_blocks = []
      prefix_list_ids  = []
      security_groups  = ["sg-064ee1b7bdf88beef"]
      self             = false
    }
  ]
  extra_egress_rules = [
    {
      description      = ""
      from_port        = 443
      to_port          = 443
      protocol         = "tcp"
      cidr_blocks      = ["0.0.0.0/0"]
      ipv6_cidr_blocks = []
      prefix_list_ids  = []
      security_groups  = []
      self             = false
    }
  ]
}

# Setup Data Lake
module "datalake_au" {
  source                   = "../../../modules/datalake"
  prefix                   = "fm-${var.APP_STAGE}-datalake-1"
  lifecycle_configurations = local.datalake_lifecycle
  tags                     = merge(local.common_tags, { Application = "Data-Lake" })
}

resource "aws_s3_bucket_policy" "datalake_au_bucket" {
  bucket = "fm-prod-adhoc-datalake-${data.aws_region.current.name}"
  policy = data.aws_iam_policy_document.datalake_adhoc_bucket_policy.json
}

resource "aws_kms_key_policy" "datalake_au_key" {
  key_id = module.datalake_au.datalake_kms_key_id
  policy = data.aws_iam_policy_document.datalake_adhoc_kms_key_policy.json
}

module "athena_au" {
  source = "../../../modules/athena"
  # To update primary workgroup, import it first by running:
  # terraform import module.athena.aws_athena_workgroup.primary primary
  workgroup_name   = "primary"
  environment_code = var.APP_STAGE
  tags             = merge(local.common_tags, { Application = "Data-Lake" })
}

module "bastion_au" {
  source                   = "../../../modules/bastion"
  environment_code         = var.APP_STAGE
  vpc_id                   = module.data_vpc_au.vpc_id
  ami_id                   = var.aps2_ami_id
  user_data_template       = "users/amazon-linux.sh"
  ssh_public_key_full_path = "users/main_key.pub"
  users_path               = "users/public_keys/"
  allowed_cidr_blocks      = ["202.137.242.38/32"] # allow Havelock office
  subnets                  = module.data_vpc_au.public_subnets_ids
  tags                     = merge(local.common_tags, { Application = "Bastion" })
}

module "redshift_au" {
  source                  = "../../../modules/redshift"
  cluster_identifier      = "main-dw-au"
  aws_account_id          = data.aws_caller_identity.current.account_id
  environment_code        = var.APP_STAGE
  cluster_node_type       = "ra3.large"
  cluster_number_of_nodes = 2
  vpc_id                  = module.data_vpc_au.vpc_id
  vpc_cidr                = module.data_vpc_au.vpc_cidr
  subnet_ids              = module.data_vpc_au.private_subnets_ids
  iam_role_policies = [
    module.datalake_au.datalake_read_write_delete_policy_arn,
    module.datalake_au.datalake_kms_key_policy_arn,
    "arn:aws:iam::aws:policy/AWSGlueConsoleFullAccess",
    module.supersonic_au.s3_read_write_delete_policy_arn
  ]
  additional_iam_roles = [
    module.redshift_kinesis_stream_access_au.kinesis_iam_role_arn
    # , module.supersonic_au.redshift_data_copy_role_arn
    , "arn:aws:iam::************:role/jitsu_redshift_role"
    , "arn:aws:iam::************:role/redshift_stream_role_ap_southeast_2"
  ]
  allow_ingress_by_sg_ids             = [module.bastion_au.bastion_sg_id]
  security_group_ids                  = [module.data_vpc_au.private_resources_sg]
  enable_logging                      = false
  automated_snapshot_rate             = 12 # 1 snapshot every 12 hours
  automated_snapshot_retention        = 7  # keep snapshot for 7 days
  enable_scheduled_action_pause       = false
  enable_scheduled_action_resume      = false
  is_enable_case_sensitive_identifier = false
  tags                                = merge(local.common_tags, { Application = "Data-Warehouse" })
}

module "supersonic_redshift_au" {
  source                  = "../../../modules/redshift"
  cluster_identifier      = "supersonic-dw-au"
  aws_account_id          = data.aws_caller_identity.current.account_id
  environment_code        = var.APP_STAGE
  cluster_node_type       = "ra3.large"
  cluster_number_of_nodes = 1
  vpc_id                  = module.data_vpc_au.vpc_id
  vpc_cidr                = module.data_vpc_au.vpc_cidr
  subnet_ids              = module.data_vpc_au.private_subnets_ids
  iam_role_policies = [
    module.datalake_au.datalake_read_write_delete_policy_arn,
    module.datalake_au.datalake_kms_key_policy_arn,
    "arn:aws:iam::aws:policy/AWSGlueConsoleFullAccess",
    module.supersonic_au.s3_read_write_delete_policy_arn
  ]
  additional_iam_roles = [
    "arn:aws:iam::************:role/jitsu_supersonic_redshift_role"
  ]
  allow_ingress_by_sg_ids                     = [module.bastion_v2_au.bastion_sg_id]
  security_group_ids                          = [module.data_vpc_au.private_resources_sg]
  enable_logging                              = false
  automated_snapshot_rate                     = 12 # 1 snapshot every 12 hours
  automated_snapshot_retention                = 7  # keep snapshot for 7 days
  enable_scheduled_action_pause               = false
  enable_scheduled_action_resume              = false
  scheduler_role_name                         = "supersonic-redshift-scheduler-role"
  scheduler_policy_name                       = "supersonic-redshift-scheduler-action"
  scheduled_action_pause_name                 = "supersonic-scheduled-action-pause"
  scheduled_action_resume_name                = "supersonic-scheduled-action-resume"
  aws_redshift_snapshot_schedule_default_name = "supersonic-automated-snapshot-schedule"
  tags                                        = merge(local.common_tags, { Application = "Data-Warehouse" })
}


module "cloudwatch_insights_au" {
  source           = "../../../modules/data_cloudwatch_insights"
  environment_code = var.APP_STAGE
}

module "datalake_adhoc_au" {
  source           = "../../../modules/datalake"
  prefix           = "fm-${var.APP_STAGE}-adhoc-datalake"
  bucket_folders   = ["raw/", "processed/", "eyecue/", "supersonic/"]
  bucket_ownership = "BucketOwnerEnforced"
  additional_s3_arn_list = [
    "arn:aws:s3:::fm-prod-maf-staging-datalake-ap-southeast-2/*",
    "arn:aws:s3:::fm-prod-maf-staging-datalake-ap-southeast-2"
  ]
  tags = merge(local.common_tags, { Application = "Adhoc-Data-Lake" })
}

resource "aws_s3_bucket" "mcd_aus_s3" {
  bucket = "external-transfer-mcd-aus"
  lifecycle {
    prevent_destroy = true
  }
}

resource "aws_iam_policy" "mcd_aus_s3_access_policy" {
  name = "mcd-aus-s3-access-policy"

  policy = jsonencode({
    "Version" : "2012-10-17",
    "Statement" : [
      {
        Effect : "Allow",
        Action : "s3:ListBucket",
        Resource : "${aws_s3_bucket.mcd_aus_s3.arn}",
        Condition : {
          "StringLike" : {
            "s3:prefix" : [
              "",
              "*"
            ]
          }
        }
      },
      {
        Effect : "Allow",
        Action : ["s3:PutObject", "s3:GetObject"],
        Resource : "${aws_s3_bucket.mcd_aus_s3.arn}/*"
      }
    ]
  })
}

resource "aws_iam_user" "mcd_aus_user" {
  name = "mcd-aus"
}

resource "aws_iam_access_key" "mcd_aus_access_key" {
  user = aws_iam_user.mcd_aus_user.name
}

resource "aws_iam_user_policy_attachment" "mcd-aus_user_attach_policy" {
  user       = aws_iam_user.mcd_aus_user.name
  policy_arn = aws_iam_policy.mcd_aus_s3_access_policy.arn
}


module "bastion_v2_au" {
  source                   = "../../../modules/bastion_v2"
  environment_code         = var.APP_STAGE
  ACCOUNT_ID               = data.aws_caller_identity.current.account_id
  AWS_REGION               = data.aws_region.current.name
  vpc_id                   = module.data_vpc_au.vpc_id
  ami_id                   = var.aps2_ami_id
  ssh_public_key_full_path = "users/main_key_v2.pub"
  users_path               = "users/user_public_keys/"
  admin_users_path         = "users/admin_public_keys/"
  allowed_cidr_blocks      = ["202.137.242.38/32"] # allow Havelock office
  subnets                  = module.data_vpc_au.public_subnets_ids
  bucket_name              = var.BASTION_AU_S3_BUCKET_NAME
  tags                     = merge(local.common_tags, { Application = "Bastion V2", "Customer Facing" = "false" })
}


module "redshift_kinesis_stream_access_au" {
  source                       = "../../../modules/redshift-cross-acc-kinesis-stream-access"
  cross_account_kinesis_roles  = var.cross_account_kinesis_roles_ap_se2
  redshift_stream_role_name    = var.redshift_stream_role_name
  redshift_cluster_identifier  = module.redshift_au.redshift_cluster_identifier
  default_iam_role_arn         = module.redshift_au.default_iam_role_arn
  attache_redshift_stream_role = "false"
}

module "datalake_s3_bucket_policy_au" {
  source              = "../../../modules/kinesis_firehose_cross_account_s3_access"
  cross_account_roles = var.cross_account_roles
  datalake_name       = "fm-prod-datalake-1-ap-southeast-2"
}


module "vpn_au" {
  source                   = "../../../modules/data_vpn"
  environment_code         = var.APP_STAGE
  ACCOUNT_ID               = data.aws_caller_identity.current.account_id
  AWS_REGION               = data.aws_region.current.name
  vpc_id                   = module.data_vpc_au.vpc_id
  ami_id                   = var.aps2_ami_id
  ssh_public_key_full_path = var.ap_se2_ssh_public_key_full_path
  users_path               = "users/user_public_keys/"
  admin_users_path         = "users/admin_public_keys/"
  allowed_cidr_blocks      = ["202.137.242.38/32"] # allow Havelock office
  subnets                  = module.data_vpc_au.public_subnets_ids
  bucket_name              = var.VPN_AU_S3_BUCKET_NAME
  tags                     = merge(local.common_tags, { Application = "vpn+bastion", "Customer Facing" = "false" })
}


module "supersonic_au" {
  source                         = "../../../modules/supersonic"
  APP_STAGE                      = var.APP_STAGE
  REGION                         = data.aws_region.current.name
  redshift_cluster_id            = module.redshift_au.redshift_cluster_identifier
  notification_arns              = []
  secret_name                    = "main-dw-au-prod-credential"
  schema_name                    = "supersonic"
  delete_after_load              = "True"
  delete_from_staging            = "True"
  redshift_data_copy_role_arn    = module.redshift_au.default_iam_role_arn
  supersonic_redshift_cluster_id = "supersonic-dw-au-prod"
}

#mcd-aus
module "mcd_aus_lambda_etl" {
  source      = "../../../modules/clients/mcd_aus"
  region_name = data.aws_region.current.name
  tags = {
    "Environment" = "prod"
  }
}


module "jitsu_redshift_iam" {
  source         = "../../../modules/jitsu_redshift_access"
  s3_bucket_name = "fm-jitsu-redshift-data"
  iam_role_name  = "jitsu_redshift_role"
  workspace_id   = "clk7l1kh30000mm0f2u01d28j"
  aws_region     = "ap-southeast-2"
  aws_account_id = data.aws_caller_identity.current.account_id
  cluster_id     = "main-dw-au-prod"
  db_user        = "jitsu_iam_user"
  db_name        = "prod"

  tags = {
    Environment = "prod"
    Owner       = "data-team"
  }
}

module "jitsu_supersonic_redshift_iam" {
  source         = "../../../modules/jitsu_redshift_access"
  s3_bucket_name = "fm-jitsu-supersonic-redshift-data"
  iam_role_name  = "jitsu_supersonic_redshift_role"
  workspace_id   = "clk7l1kh30000mm0f2u01d28j"
  aws_region     = "ap-southeast-2"
  aws_account_id = data.aws_caller_identity.current.account_id
  cluster_id     = "supersonic-dw-au-prod"
  db_user        = "jitsu_iam_user"
  db_name        = "supersonic"

  tags = {
    Environment = "prod"
    Owner       = "data-team"
  }
}

module "quicksight_reporting_setup" {
  source                                 = "../../../modules/quicksight_reporting_setup"
  environment_code                       = var.APP_STAGE
  client_name                            = "bkg-nzl"
  report_tracking_dynamodb_table_name    = "quicksight-report-export-details-bkg-nzl"
  execution_tracking_dynamodb_table_name = "quicksight-report-execution-tracker-bkg-nzl"
  dynamodb_pitr_enabled                  = true
  sqs_queue_names = [
    "quicksight-report-initialization-queue-bkg-nzl",
    "quicksight-report-email-send-queue-bkg-nzl"
  ]
  sqs_report_initialization_queue_name       = "quicksight-report-initialization-queue-bkg-nzl"
  sqs_report_email_queue_name                = "quicksight-report-email-send-queue-bkg-nzl"
  redshift_cluster_id                        = "main-dw-au-prod"
  database                                   = "prod"
  db_user                                    = "dw_master"
  region_name                                = data.aws_region.current.name
  quicksight_reporting_s3_bucket_name        = "quicksight-pdf-report-exporting-bkg-nzl-prod"
  quicksight_report_pdf_init_lambda_name     = "quicksight-report-generation-initializer-bkg-nzl"
  ec2_ami_id                                 = "ami-040e71e7b8391cae4"
  ec2_instance_type                          = "t3a.xlarge"
  ec2_root_block_device_encrypted            = true
  ec2_root_block_device_volume_size          = 30
  ec2_server_name                            = "quicksight-report-generator-bkg-nzl"
  ec2_ssh_public_key_full_path               = "users/admin_public_keys/kasun.bamunusingha"
  sns_topic_name_for_errors                  = "quicksight-report-generation-notifications-bkg-nzl"
  aws_account_id                             = data.aws_caller_identity.current.account_id
  embed_url_generation_lambda_name           = "quicksight-embed-url-for-pdf-generation-bkg-nzl"
  quicksight_dashboard_id                    = "53f413bc-91f3-4a99-af4e-29991ab06df2"
  dashboard_sheet_id                         = "53f413bc-91f3-4a99-af4e-29991ab06df2_f24af872-31e9-48d3-b946-c32254528473"
  ses_region_name                            = "us-east-1"
  ses_sender_email                           = "<EMAIL>"
  bcc_email                                  = "<EMAIL>"
  report_generation_finalization_lambda_name = "quicksight-report-generation-finalization-bkg-nzl"
  email_sending_lambda_name                  = "quicksight-report-generation-email-sender-bkg-nzl"
  execution_time_zone                        = "Pacific/Auckland"
  execution_time_window_start_time           = "06:30"
  execution_time_window_end_time             = "07:30"
  pdf_render_view_width_px                   = "1920"
  pdf_render_view_height_px                  = "4000"
  pdf_render_waiting_time_ms                 = 120000
  pdf_print_page_size                        = "A2"
  row_level_security_enabled_for_dashboard   = "True"

  tags = merge(
    local.common_tags,
    {
      "Application"     = "Quicksight-Reporting-For-BKG-NZL"
      "Terraform"       = "True"
      Environment       = "prod"
      Product           = "Eyecue"
      Customer          = "BKG-NZL"
      Serverless        = "False"
      Stack             = "Application"
      Owner             = "data-team"
      Squad             = "Data"
      "Customer Facing" = "False"
      System            = "Ubuntu 22.04 - Node 22.13.0 - Python 3.12"
      MultiRegion       = "False"
      Backup            = "False"
  })
}

module "data-api" {
  source           = "../../../modules/data_api"
  environment_code = var.APP_STAGE

  aws_account_id     = data.aws_caller_identity.current.account_id
  vpc_id             = module.data_vpc_au.vpc_id
  vpc_cidr           = module.data_vpc_au.vpc_cidr
  private_subnet_ids = module.data_vpc_au.private_subnets_ids

  api_handling_lambda_name = "data-api-fastapi-endpoint"
  api_handling_lambda_arn  = "arn:aws:lambda:ap-southeast-2:************:function:data-api-fastapi-endpoint"
  region_name              = data.aws_region.current.name

  tags = merge(
    local.common_tags,
    {
      "Application"     = "Data-API"
      "Terraform"       = "True"
      Environment       = "prod"
      Product           = "Eyecue"
      Customer          = "ALL"
      Serverless        = "True"
      Stack             = "Application"
      Owner             = "data-team"
      Squad             = "Data"
      "Customer Facing" = "True"
      System            = "Python 3.12"
      MultiRegion       = "False"
      Backup            = "False"
  })
}


module "airflow" {
  source              = "../../../modules/airflow"
  environment_code    = var.APP_STAGE
  rds_identifier      = "airflowdb"
  rds_master_username = "airflowadmin"
  rds_database_name   = "airflow"
  vpc_id              = module.data_vpc_au.vpc_id
  subnet_ids          = module.data_vpc_au.private_subnets_ids
  vpc_cidr            = module.data_vpc_au.vpc_cidr
  APP_STAGE           = var.APP_STAGE
  REGION              = data.aws_region.current.name

  tags = {
    Environment = "production"
    Application = "airflow"
  }
}

# module "tableau_bridge" {
#   source        = "../../../modules/Tableau/Bridge"
#   vpc_id        = module.data_vpc_au.vpc_id
#   subnet_id     = module.data_vpc_au.private_subnets_ids[0]
#   instance_type = "t3.xlarge"
#   name          = "tableau-bridge-server"
#   APP_STAGE     = var.APP_STAGE

#   additional_tags = {
#     "Application" = "Tableau-Bridge"
#   }
# }

module "tableau_bridge" {
  source = "../../../modules/Tableau/Bridge"
  vpc_id = module.data_vpc_au.vpc_id
  # ecr_image_uri = "************.dkr.ecr.ap-southeast-2.amazonaws.com/tableau-bridge:latest"
  # bridge_client       = var.tableau_bridge_client
  # secret_manager_arn  = var.tableau_bridge_secret_manager_arn
  # bridge_user_email   = var.tableau_bridge_user_email
  # bridge_site         = var.tableau_bridge_site
  subnet_id                = module.data_vpc_au.private_subnets_ids[0]
  ssh_public_key_full_path = var.ap_se2_ssh_public_key_full_path
  environment_code         = var.APP_STAGE
}