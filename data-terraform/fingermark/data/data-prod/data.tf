data "aws_caller_identity" "current" {}
data "aws_region" "current" {}

data "aws_iam_policy_document" "datalake_adhoc_bucket_policy" {
  statement {
    sid    = "1"
    effect = "Allow"
    principals {
      type        = "AWS"
      identifiers = ["${var.DYNAMODB_S3_ARN}"]
    }
    actions = [
      "s3:GetObject",
      "s3:GetObjectTagging",
      "s3:PutObject",
      "s3:PutObjectAcl",
      "s3:List*",
      "s3:PutObjectTagging"
    ]
    resources = [
      "${var.ADHOC_S3_ARN}",
      "${var.ADHOC_S3_ARN}/*"
    ]
  }
}



data "aws_iam_policy_document" "datalake_adhoc_kms_key_policy" {
  statement {
    sid    = "Enable IAM User Permissions"
    effect = "Allow"
    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::047783385012:root"]
    }
    actions = [
      "kms:*"
    ]
    resources = ["*"]
  }
  statement {
    sid    = "Enable infra-dynamodbs3-export-role Permissions"
    effect = "Allow"
    principals {
      type        = "AWS"
      identifiers = ["${var.DYNAMODB_S3_ARN}"]
    }
    actions = [
      "kms:Decrypt",
      "kms:Encrypt",
      "kms:DescribeKey"
    ]
    resources = ["*"]
  }
}
