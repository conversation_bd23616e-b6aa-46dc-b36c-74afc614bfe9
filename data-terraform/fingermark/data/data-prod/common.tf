locals {
  common_tags = {
    Terraform   = "true"
    Author      = "Data-Team"
    Backup      = "false"
    Environment = var.APP_STAGE
    MultiRegion = "true"
  }

  datalake_lifecycle = [
    {
      id      = "delete-rds-copies"
      enabled = true
      filter = {
        prefix = "external/rds/"
      }

      expiration = {
        days = 7
      }

      noncurrent_version_expiration = {
        days = 7
      }
    },
    {
      id      = "transition-eyecue-archive"
      enabled = true
      filter = {
        prefix = "eyecue-archive/"
      }

      transition = [
        {
          days          = 30
          storage_class = "ONEZONE_IA"
        },
        {
          days          = 60
          storage_class = "DEEP_ARCHIVE"
        }
      ]
    },
    {
      id      = "delete-noncurrent-version"
      enabled = true
      noncurrent_version_expiration = {
        days = 30
      }
    }
  ]
}

module "assume_role" {
  source = "git::*****************:fingermarkltd/fingermark-terraform.git//modules/fingermark_users_assume_role"
  roles  = ["AdminAccess", "DevAccess", "PowerAccess", "DeployerAccess", "DataScientist"]
}

module "budget" {
  source                   = "../../../modules/aws_budgets"
  aws_budgets_limit_amount = "2500"
}


module "iam_password_policy" {
  source = "git::*****************:fingermarkltd/fingermark-terraform.git//modules/iam_password_policy"
}