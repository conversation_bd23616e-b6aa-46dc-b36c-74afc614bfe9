# =======================================================
# CloudWatch Alarms: DynamoDB Tables in us-east-1 (SOC2 requirement)
# =======================================================

# Define the DynamoDB tables to monitor in us-east-1
locals {
  dynamodb_tables_us_east_1 = {
    "etl_hvi_roi_report_meta_data"                       = "etl_hvi_roi_report_meta_data"
    "quicksight-report-execution-tracker-cfa-usa"        = "quicksight-report-execution-tracker-cfa-usa"
    "quicksight-report-export-details-cfa-usa"           = "quicksight-report-export-details-cfa-usa"
    "quicksight-report-execution-tracker-cfa-usa-weekly" = "quicksight-report-execution-tracker-cfa-usa-weekly"
    "quicksight-report-export-details-cfa-usa-weekly"    = "quicksight-report-export-details-cfa-usa-weekly"
    "redshift_query_id_tracker"                          = "redshift_query_id_tracker"
  }
}

module "dynamodb_cw_alarms_us_east_1" {
  source = "git::*****************:fingermarkltd/fingermark-terraform.git//modules/dynamodb_cw_alarms?ref=master"

  # Use the us-east-1 provider
  providers = {
    aws = aws.us_e1
  }

  sns_topic_arns = [var.cw_alarms_sns_topic_arns_region_lookup["us-east-1"]]

  # Configure RCU alarms for all tables
  cw_alarm_config_ddb_table_consumed_rcu = {
    for id, table in local.dynamodb_tables_us_east_1 : id => {
      table_name = table
    }
  }

  # Configure WCU alarms for all tables
  cw_alarm_config_ddb_table_consumed_wcu = {
    for id, table in local.dynamodb_tables_us_east_1 : id => {
      table_name = table
    }
  }

  # We're not configuring GSI alarms here as we don't have specific GSI information for these tables
  # If GSIs need to be monitored, they can be added later

  tags = {
    Environment = "Production"
    Application = "DynamoDB-Monitoring"
    Squad       = "Platform team"
  }
}
