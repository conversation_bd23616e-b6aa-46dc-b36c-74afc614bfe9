provider "aws" {
  region = "ap-southeast-2"
  assume_role {
    # The role AR<PERSON> within Account B to AssumeRole into. Created in step 1.
    role_arn     = "arn:aws:iam::************:role/AdminAccess"
    session_name = "fingermark-data-prod"
  }
}

provider "aws" {
  alias  = "usw1"
  region = "us-west-1"
  assume_role {
    # The role ARN within Account B to AssumeRole into. Created in step 1.
    role_arn     = "arn:aws:iam::************:role/AdminAccess"
    session_name = "fingermark-data-prod"
  }
}


provider "aws" {
  alias  = "us_e1"
  region = "us-east-1"
  assume_role {
    role_arn     = "arn:aws:iam::************:role/AdminAccess"
    session_name = "fingermark-data-prod"
  }
}

provider "aws" {
  alias  = "us_e2"
  region = "us-east-2"
  assume_role {
    role_arn     = "arn:aws:iam::************:role/AdminAccess"
    session_name = "fingermark-data-prod"
  }
}

provider "aws" {
  alias  = "us_w2"
  region = "us-west-2"
  assume_role {
    role_arn     = "arn:aws:iam::************:role/AdminAccess"
    session_name = "fingermark-data-prod"
  }
}


provider "aws" {
  alias  = "ca_c1"
  region = "ca-central-1"
  assume_role {
    role_arn     = "arn:aws:iam::************:role/AdminAccess"
    session_name = "fingermark-data-prod"
  }
}
