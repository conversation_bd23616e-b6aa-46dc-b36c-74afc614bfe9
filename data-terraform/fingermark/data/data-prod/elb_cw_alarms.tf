# =======================================================
# CloudWatch Alarms: ELB (SOC2 requirement)
# =======================================================

# Define the ALBs to monitor by region
locals {
  elb_cw_alarms = {
    ap_southeast_2 = {
      alb_config = {
        "metabase" = {
          lb_name  = "metabase",
          tg_names = ["metabase"]
        },
        "grafana-prod" = {
          lb_name  = "grafana-prod",
          tg_names = ["grafana"]
        }
      }
    }
  }
}

# ELB CloudWatch Alarms - ap-southeast-2
module "elb_cw_alarms_ap_southeast_2" {
  source = "git::*****************:fingermarkltd/fingermark-terraform.git//modules/elb_cw_alarms?ref=master"

  sns_topic_arns = [var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]]

  # ALB Healthy Host Count alarms
  cw_alarm_config_alb_healthy_host_count = local.elb_cw_alarms.ap_southeast_2.alb_config

  # ALB Unhealthy Host Count alarms
  cw_alarm_config_alb_unhealthy_host_count = local.elb_cw_alarms.ap_southeast_2.alb_config

  # ALB Target Response Time alarms
  cw_alarm_config_alb_target_response_time = {
    for k, v in local.elb_cw_alarms.ap_southeast_2.alb_config : k => {
      lb_name = v.lb_name
    }
  }

  # ALB HTTP 5XX Error Count alarms (ELB)
  cw_alarm_config_alb_httpcode_elb_5xx_count = {
    for k, v in local.elb_cw_alarms.ap_southeast_2.alb_config : k => {
      lb_name = v.lb_name
    }
  }

  # ALB HTTP 5XX Error Count alarms (Target)
  cw_alarm_config_alb_httpcode_target_5xx_count = {
    for k, v in local.elb_cw_alarms.ap_southeast_2.alb_config : k => {
      lb_name = v.lb_name
    }
  }

  tags = {
    Environment = "Production"
    Application = "ELB-Monitoring"
    Squad       = "Platform team"
  }
}
