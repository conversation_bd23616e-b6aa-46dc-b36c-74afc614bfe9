module "data_vpc_ca_c1" {
  source = "../../../modules/data_vpc"
  providers = {
    aws = aws.ca_c1
  }
  vpc_cidr_block         = "10.1.0.0/16"
  aws_availability_zones = ["ca-central-1a", "ca-central-1b", "ca-central-1d"]
  environment_code       = var.APP_STAGE
  vpc_name               = "data-${var.APP_STAGE}"
  limit_subnet           = true
  tags                   = merge(local.common_tags, { Stack = "Data-VPC", Application = "Data-Infrastructure" })

}

module "datalake_ca_c1" {
  source = "../../../modules/datalake"
  providers = {
    aws = aws.ca_c1
  }
  prefix                   = "fm-${var.APP_STAGE}-datalake-1"
  lifecycle_configurations = local.datalake_lifecycle
  tags                     = merge(local.common_tags, { Application = "Data-Lake" })
}

module "bastion_v2_ca_c1" {
  source = "../../../modules/bastion_v2"
  providers = {
    aws = aws.ca_c1
  }
  environment_code         = var.APP_STAGE
  ACCOUNT_ID               = data.aws_caller_identity.current.account_id
  AWS_REGION               = "ca-central-1"
  vpc_id                   = module.data_vpc_ca_c1.vpc_id
  ami_id                   = "ami-0654ca17e4d49cdb4"
  ssh_public_key_full_path = var.ca_c1_ssh_public_key_full_path
  users_path               = "users/user_public_keys/"
  admin_users_path         = "users/admin_public_keys/"
  allowed_cidr_blocks      = ["202.137.242.38/32"] # allow Havelock office
  subnets                  = module.data_vpc_ca_c1.public_subnets_ids
  bucket_name              = var.bastion_ca_c1_s3_bucket_name
  tags                     = merge(local.common_tags, { Application = "Bastion V2", "Customer Facing" = "false" })
}


module "redshift_ca_c1" {
  source = "../../../modules/redshift"
  providers = {
    aws = aws.ca_c1
  }
  cluster_identifier = "main-dw-ca-central-1"
  aws_account_id     = data.aws_caller_identity.current.account_id
  environment_code   = var.APP_STAGE
  vpc_id             = module.data_vpc_ca_c1.vpc_id
  vpc_cidr           = module.data_vpc_ca_c1.vpc_cidr
  subnet_ids         = module.data_vpc_ca_c1.private_subnets_ids
  iam_role_policies = [
    module.datalake_ca_c1.datalake_read_write_delete_policy_arn,
    module.datalake_ca_c1.datalake_kms_key_policy_arn,
    "arn:aws:iam::aws:policy/AWSGlueConsoleFullAccess"
  ]
  additional_iam_roles           = [module.redshift_kinesis_stream_access_ca_c1.kinesis_iam_role_arn]
  allow_ingress_by_sg_ids        = [module.bastion_v2_ca_c1.bastion_sg_id]
  security_group_ids             = [module.data_vpc_ca_c1.private_resources_sg]
  enable_logging                 = false
  automated_snapshot_rate        = 24 # 1 snapshot every 12 hours
  automated_snapshot_retention   = 7  # keep snapshot for 7 days
  enable_scheduled_action_pause  = false
  enable_scheduled_action_resume = false
  tags                           = merge(local.common_tags, { Application = "Data-Warehouse" })
}


module "redshift_kinesis_stream_access_ca_c1" {
  source = "../../../modules/redshift-cross-acc-kinesis-stream-access"
  providers = {
    aws = aws.ca_c1
  }
  cross_account_kinesis_roles = var.cross_account_kinesis_roles_ca_c1
  redshift_stream_role_name   = var.redshift_stream_role_name
  redshift_cluster_identifier = module.redshift_ca_c1.redshift_cluster_identifier
  default_iam_role_arn        = module.redshift_ca_c1.default_iam_role_arn
}


module "vpn_ca_c1" {
  providers = {
    aws = aws.ca_c1
  }
  source                   = "../../../modules/data_vpn"
  environment_code         = var.APP_STAGE
  ACCOUNT_ID               = data.aws_caller_identity.current.account_id
  AWS_REGION               = data.aws_region.current.name
  vpc_id                   = module.data_vpc_ca_c1.vpc_id
  ami_id                   = "ami-0463820149fb13e43"
  ssh_public_key_full_path = var.ca_c1_ssh_public_key_full_path
  users_path               = "users/user_public_keys/"
  admin_users_path         = "users/admin_public_keys/"
  allowed_cidr_blocks      = ["202.137.242.38/32"] # allow Havelock office
  subnets                  = module.data_vpc_ca_c1.public_subnets_ids
  bucket_name              = "data-prod-ca-c1-vpn"
  tags                     = merge(local.common_tags, { Application = "vpn+bastion", "Customer Facing" = "false" })
}

resource "aws_sns_topic" "data_engineering_slack_message_ca_c1" {

  provider = aws.ca_c1
  name     = "data-engineering-slack-message-ca-c1"
  tags = {
    Environment = "prod"
    Project     = "Data Engineering"
  }
}

resource "aws_sns_topic_subscription" "slack_subscription_ca_c1" {
  provider  = aws.ca_c1
  topic_arn = aws_sns_topic.data_engineering_slack_message_ca_c1.arn
  protocol  = "email"
  endpoint  = "<EMAIL>"
}

#mcd-can lambda etl
module "client_mcd_can_lambda_etl" {
  source = "../../../modules/clients/mcd_can/lambda_etl"
  providers = {
    aws = aws.ca_c1
  }

  secrets_manager_arns = [
    "arn:aws:secretsmanager:ca-central-1:************:secret:main-dw-ca-central-1-prod-credential-aHzmGR"
  ]

  redshift_cluster_id = "main-dw-ca-central-1-prod"
  database            = "prod"
  db_user             = "dw_master"
  secret_name         = "main-dw-ca-central-1-prod-credential"
  region_name         = "ca-central-1"
  s3_bucket_name      = "etl-lambda-code-eyecue-mcd-can"
  notification_arns   = [aws_sns_topic.data_engineering_slack_message_ca_c1.arn]
  tags = {
    "Environment" = "prod"
  }
}

#tim-can lambda etl
module "client_tim_can_lambda_etl" {
  source = "../../../modules/clients/tim_can/lambda_etl"
  providers = {
    aws = aws.ca_c1
  }

  secrets_manager_arns = [
    "arn:aws:secretsmanager:ca-central-1:************:secret:main-dw-ca-central-1-prod-credential-aHzmGR"
  ]

  redshift_cluster_id = "main-dw-ca-central-1-prod"
  database            = "prod"
  db_user             = "dw_master"
  secret_name         = "main-dw-ca-central-1-prod-credential"
  region_name         = "ca-central-1"
  s3_bucket_name      = "etl-lambda-code-eyecue-tim-can"
  notification_arns   = [aws_sns_topic.data_engineering_slack_message_ca_c1.arn]
  tags = {
    "Environment" = "prod"
  }
}

module "tim_can_daily_email_sender" {
  source = "../../../modules/clients/tim_can/daily_email_sender"

  providers = {
    aws = aws.ca_c1
  }

  redshift_cluster_id              = "main-dw-ca-central-1-prod"
  database                         = "prod"
  db_user                          = "dw_master"
  region_name                      = "ca-central-1"
  ses_region_name                  = "us-east-1"
  s3_bucket_name                   = "daily-email-reports-tim-can"
  execution_time_window_start_time = "07:00"
  execution_time_window_end_time   = "08:00"
  execution_time_zone              = "America/Toronto"
  data_unload_iam_role             = "arn:aws:iam::************:role/redshift-main-dw-ca-central-1-ca-central-1"
  aws_account_id                   = data.aws_caller_identity.current.account_id
  ses_sender_email                 = "<EMAIL>"
  email_receiver_emails            = "<EMAIL>"
  bcc_emails                       = "<EMAIL>,<EMAIL>"
  notification_arns                = [aws_sns_topic.data_engineering_slack_message_ca_c1.arn]

  tags = merge(
    local.common_tags,
    {
      "Application"     = "Daily-Email-Sender-for-TIM-CAN"
      "Terraform"       = "True"
      Environment       = "prod"
      Product           = "Eyecue"
      Customer          = "TIM-CAN"
      Serverless        = "True"
      Stack             = "Application"
      Owner             = "data-team"
      Squad             = "Data"
      "Customer Facing" = "True"
      System            = "Python 3.12"
      MultiRegion       = "False"
      Backup            = "False"
  })
}


module "tableau_bridge_ca_c1" {

  source = "../../../modules/Tableau/Bridge"
  providers = {
    aws = aws.ca_c1
  }

  vpc_id                   = module.data_vpc_ca_c1.vpc_id
  subnet_id                = module.data_vpc_ca_c1.private_subnets_ids[0]
  ssh_public_key_full_path = var.ca_c1_ssh_public_key_full_path
  environment_code         = var.APP_STAGE

}