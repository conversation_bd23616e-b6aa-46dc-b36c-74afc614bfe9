module "data_vpc_us_w2" {
  source = "../../../modules/data_vpc"
  providers = {
    aws = aws.us_w2
  }
  vpc_cidr_block         = "10.1.0.0/16"
  aws_availability_zones = ["us-west-2a", "us-west-2b", "us-west-2c"]
  environment_code       = var.APP_STAGE
  vpc_name               = "data-${var.APP_STAGE}"
  tags                   = merge(local.common_tags, { Stack = "Data-VPC", Application = "Data-Infrastructure" })
}

# Setup Data Lake
module "datalake_us_w2" {
  source = "../../../modules/datalake"
  providers = {
    aws = aws.us_w2
  }
  prefix                   = "fm-${var.APP_STAGE}-datalake-1"
  lifecycle_configurations = local.datalake_lifecycle
  tags                     = merge(local.common_tags, { Application = "Data-Lake" })
}


module "bastion_v2_us_w2" {
  source = "../../../modules/bastion_v2"
  providers = {
    aws = aws.us_w2
  }
  environment_code         = var.APP_STAGE
  ACCOUNT_ID               = data.aws_caller_identity.current.account_id
  AWS_REGION               = "us-west-2"
  vpc_id                   = module.data_vpc_us_w2.vpc_id
  ami_id                   = var.usw2_ami_id
  ssh_public_key_full_path = "users/main_key_v2.pub"
  users_path               = "users/user_public_keys/"
  admin_users_path         = "users/admin_public_keys/"
  allowed_cidr_blocks      = ["202.137.242.38/32"] # allow Havelock office
  subnets                  = module.data_vpc_us_w2.public_subnets_ids
  bucket_name              = var.bastion_us_w2_s3_bucket_name
  tags                     = merge(local.common_tags, { Application = "Bastion V2", "Customer Facing" = "false" })
}



module "redshift_us_w2" {
  source = "../../../modules/redshift"
  providers = {
    aws = aws.us_w2
  }
  cluster_identifier = "main-dw-us-west-2"
  aws_account_id     = data.aws_caller_identity.current.account_id
  environment_code   = var.APP_STAGE
  vpc_id             = module.data_vpc_us_w2.vpc_id
  vpc_cidr           = module.data_vpc_us_w2.vpc_cidr
  subnet_ids         = module.data_vpc_us_w2.private_subnets_ids
  iam_role_policies = [
    module.datalake_us_w2.datalake_read_write_delete_policy_arn,
    module.datalake_us_w2.datalake_kms_key_policy_arn,
    "arn:aws:iam::aws:policy/AWSGlueConsoleFullAccess"
  ]
  additional_iam_roles           = [module.redshift_kinesis_stream_access_us_w2.kinesis_iam_role_arn]
  allow_ingress_by_sg_ids        = [module.bastion_v2_us_w2.bastion_sg_id]
  security_group_ids             = [module.data_vpc_us_w2.private_resources_sg]
  enable_logging                 = false
  automated_snapshot_rate        = 12 # 1 snapshot every 12 hours
  automated_snapshot_retention   = 7  # keep snapshot for 7 days
  enable_scheduled_action_pause  = false
  enable_scheduled_action_resume = false
  tags                           = merge(local.common_tags, { Application = "Data-Warehouse" })
}

module "redshift_kinesis_stream_access_us_w2" {
  source = "../../../modules/redshift-cross-acc-kinesis-stream-access"
  providers = {
    aws = aws.us_w2
  }
  cross_account_kinesis_roles = var.cross_account_kinesis_roles_us_w2
  redshift_stream_role_name   = var.redshift_stream_role_name
  redshift_cluster_identifier = module.redshift_us_w2.redshift_cluster_identifier
  default_iam_role_arn        = module.redshift_us_w2.default_iam_role_arn
}


module "vpn_us_w2" {
  providers = {
    aws = aws.us_w2
  }
  source                   = "../../../modules/data_vpn"
  environment_code         = var.APP_STAGE
  ACCOUNT_ID               = data.aws_caller_identity.current.account_id
  AWS_REGION               = data.aws_region.current.name
  vpc_id                   = module.data_vpc_us_w2.vpc_id
  ami_id                   = "ami-0323ead22d6752894"
  ssh_public_key_full_path = var.us_ssh_public_key_full_path
  users_path               = "users/user_public_keys/"
  admin_users_path         = "users/admin_public_keys/"
  allowed_cidr_blocks      = ["202.137.242.38/32"] # allow Havelock office
  subnets                  = module.data_vpc_us_w2.public_subnets_ids
  bucket_name              = "data-prod-us-w2-vpn"
  tags                     = merge(local.common_tags, { Application = "vpn+bastion", "Customer Facing" = "false" })
}

module "tableau_bridge_us_w2" {
  source = "../../../modules/Tableau/Bridge"
  providers = {
    aws = aws.us_w2
  }

  vpc_id                   = module.data_vpc_us_w2.vpc_id
  subnet_id                = module.data_vpc_us_w2.private_subnets_ids[0]
  ssh_public_key_full_path = var.us_ssh_public_key_full_path
  environment_code         = var.APP_STAGE

}