# Setup VPC and 3 public subnets and 3 private subnets
module "data_vpc_us" {
  source = "../../../modules/data_vpc"
  providers = {
    aws = aws.usw1
  }
  vpc_cidr_block         = "10.1.0.0/16"
  environment_code       = var.APP_STAGE
  aws_availability_zones = ["us-west-1a", "us-west-1c"]
  vpc_name               = "data-${var.APP_STAGE}"
  tags                   = merge(local.common_tags, { Stack = "Data-VPC", Application = "Data-Infrastructure" })
}

# Setup Data Lake
module "datalake_us" {
  source = "../../../modules/datalake"
  providers = {
    aws = aws.usw1
  }
  prefix                   = "fm-${var.APP_STAGE}-datalake-1"
  lifecycle_configurations = local.datalake_lifecycle
  tags                     = merge(local.common_tags, { Application = "Data-Lake" })
  s3_replication_configuration = {
    role = aws_iam_role.datalake_us_bucket_replication.arn,
    rule = [
      {
        id                        = "datalake-to-stb-account"
        status                    = "Enabled"
        delete_marker_replication = true
        filter = {
          prefix = "stb/"
        }

        source_selection_criteria = {
          replica_modifications = {
            status = "Enabled"
          }
          sse_kms_encrypted_objects = {
            enabled = true
          }
        }

        destination = {
          bucket        = var.stb_replication_bucket_arn
          account_id    = "************"
          storage_class = "STANDARD"
          access_control_translation = {
            owner = "Destination"
          }
          encryption_configuration = {
            replica_kms_key_id = "${var.destination_stb_kms_key_arn}"
          }
          replication_time = {
            status  = "Enabled"
            minutes = 15
          }
          metrics = {
            status  = "Enabled"
            minutes = 15
          }
        }
      }
    ]
  }
}

module "athena_us" {
  source = "../../../modules/athena"
  providers = {
    aws = aws.usw1
  }
  # To update primary workgroup, import it first by running:
  # terraform import module.athena_us.aws_athena_workgroup.primary primary
  workgroup_name   = "primary"
  environment_code = var.APP_STAGE
  tags             = merge(local.common_tags, { Application = "Data-Lake" })
}

module "bastion_us" {
  source = "../../../modules/bastion"
  providers = {
    aws = aws.usw1
  }
  environment_code         = var.APP_STAGE
  vpc_id                   = module.data_vpc_us.vpc_id
  ami_id                   = var.usw1_ami_id
  user_data_template       = "users/amazon-linux.sh"
  ssh_public_key_full_path = "users/main_key.pub"
  users_path               = "users/public_keys/"
  allowed_cidr_blocks      = ["202.137.242.38/32"] # allow Havelock office
  subnets                  = module.data_vpc_us.public_subnets_ids
  tags                     = merge(local.common_tags, { Application = "Bastion" })
}

module "redshift_us" {
  source = "../../../modules/redshift"
  providers = {
    aws = aws.usw1
  }
  cluster_identifier = "main-dw-us"
  aws_account_id     = data.aws_caller_identity.current.account_id
  environment_code   = var.APP_STAGE
  vpc_id             = module.data_vpc_us.vpc_id
  vpc_cidr           = module.data_vpc_us.vpc_cidr
  subnet_ids         = module.data_vpc_us.private_subnets_ids
  iam_role_policies = [
    module.datalake_us.datalake_read_write_delete_policy_arn,
    module.datalake_us.datalake_kms_key_policy_arn,
    "arn:aws:iam::aws:policy/AWSGlueConsoleFullAccess"
  ]
  additional_iam_roles           = [module.redshift_kinesis_stream_access_us.kinesis_iam_role_arn]
  allow_ingress_by_sg_ids        = [module.bastion_us.bastion_sg_id]
  security_group_ids             = [module.data_vpc_us.private_resources_sg]
  enable_logging                 = false
  automated_snapshot_rate        = 12 # 1 snapshot every 12 hours
  automated_snapshot_retention   = 7  # keep snapshot for 7 days
  enable_scheduled_action_pause  = false
  enable_scheduled_action_resume = false
  tags                           = merge(local.common_tags, { Application = "Data-Warehouse" })
}


module "cloudwatch_insights_us" {
  source = "../../../modules/data_cloudwatch_insights"
  providers = {
    aws = aws.usw1
  }
  environment_code = var.APP_STAGE
}

module "datalake_adhoc_us" {
  source = "../../../modules/datalake"
  providers = {
    aws = aws.usw1
  }
  prefix         = "fm-${var.APP_STAGE}-adhoc-datalake"
  bucket_folders = ["raw/", "processed/", "eyecue/", "supersonic/"]
  tags           = merge(local.common_tags, { Application = "Adhoc-Data-Lake" })
}

module "sagemaker_notebook_us" {
  source = "../../../modules/sagemaker_notebook"
  providers = {
    aws = aws.usw1
  }
  environment_code   = var.APP_STAGE
  aws_account_id     = data.aws_caller_identity.current.account_id
  notebook_prefix    = "analyst-notebook"
  instance_type      = "ml.t3.large"
  vpc_id             = module.data_vpc_us.vpc_id
  subnet_id          = module.data_vpc_us.private_subnets_ids[0]
  security_group_ids = [module.data_vpc_us.private_resources_sg]
  iam_role_policies = [
    module.datalake_us.datalake_read_policy_arn,
    module.datalake_us.datalake_kms_key_policy_arn,
    module.datalake_adhoc_us.datalake_read_write_delete_policy_arn,
    module.datalake_adhoc_us.datalake_kms_key_policy_arn,
    "arn:aws:iam::aws:policy/AWSCodeCommitPowerUser",
    "arn:aws:iam::aws:policy/AmazonRedshiftReadOnlyAccess",
    "arn:aws:iam::aws:policy/service-role/AWSGlueServiceNotebookRole"
  ]
  on_start_script  = base64encode(file("scripts/sagemaker-notebook-on-start.sh"))
  on_create_script = base64encode(file("scripts/sagemaker-notebook-on-create.sh"))
  tags             = merge(local.common_tags, { Application = "Data-Notebooks" })
}




# DATALAKE_US IAM RESOURCES
data "aws_iam_policy_document" "datalake_us_bucket_replication_assumerole" {
  statement {
    actions = ["sts:AssumeRole"]

    principals {
      type        = "Service"
      identifiers = ["s3.amazonaws.com"]
    }
  }
}

data "aws_iam_policy_document" "datalake_us_bucket_replication" {
  statement {
    actions = [
      "s3:ListBucket",
      "s3:GetReplicationConfiguration",
      "s3:GetObjectVersionForReplication",
      "s3:GetObjectVersionAcl",
      "s3:PutInventoryConfiguration"

    ]

    resources = [
      module.datalake_us.datalake_arn,
      "${module.datalake_us.datalake_arn}/*"
    ]
  }

  statement {
    actions = [
      "s3:ReplicateObject",
      "s3:ReplicateDelete",
      "s3:ObjectOwnerOverrideToBucketOwner",
    ]

    resources = [
      var.stb_replication_bucket_arn,
      "${var.stb_replication_bucket_arn}/*",
    ]
  }

  statement {
    actions = [
      "kms:Decrypt"
    ]
    resources = [
      module.datalake_us.datalake_kms_key_arn
    ]
  }
  statement {
    actions = [
      "kms:Encrypt"
    ]
    resources = [
      var.destination_stb_kms_key_arn
    ]
  }
}

resource "aws_iam_role" "datalake_us_bucket_replication" {
  name               = "fm-${var.APP_STAGE}-datalake_us-replication-role"
  assume_role_policy = data.aws_iam_policy_document.datalake_us_bucket_replication_assumerole.json
}

resource "aws_iam_policy" "datalake_us_bucket_replication" {
  name   = "fm-${var.APP_STAGE}-datalake_us-replication-policy"
  policy = data.aws_iam_policy_document.datalake_us_bucket_replication.json
}

resource "aws_iam_role_policy_attachment" "stbbucket_replication" {
  role       = aws_iam_role.datalake_us_bucket_replication.name
  policy_arn = aws_iam_policy.datalake_us_bucket_replication.arn
}


module "bastion_v2_us" {
  source = "../../../modules/bastion_v2"
  providers = {
    aws = aws.usw1
  }
  environment_code         = var.APP_STAGE
  ACCOUNT_ID               = data.aws_caller_identity.current.account_id
  AWS_REGION               = "us-west-1"
  vpc_id                   = module.data_vpc_us.vpc_id
  ami_id                   = var.usw1_ami_id
  ssh_public_key_full_path = "users/main_key_v2.pub"
  users_path               = "users/user_public_keys/"
  admin_users_path         = "users/admin_public_keys/"
  allowed_cidr_blocks      = ["202.137.242.38/32"] # allow Havelock office
  subnets                  = module.data_vpc_us.public_subnets_ids
  bucket_name              = var.BASTION_US_S3_BUCKET_NAME
  tags                     = merge(local.common_tags, { Application = "Bastion V2", "Customer Facing" = "false" })
}

module "redshift_kinesis_stream_access_us" {
  source = "../../../modules/redshift-cross-acc-kinesis-stream-access"
  providers = {
    aws = aws.usw1
  }
  cross_account_kinesis_roles        = var.cross_account_kinesis_roles_us_w1
  redshift_stream_role_name          = var.redshift_stream_role_name
  redshift_cluster_identifier        = module.redshift_us.redshift_cluster_identifier
  default_iam_role_arn               = module.redshift_us.default_iam_role_arn
  iot_to_kinesis_failure_bucket_name = var.iot_to_kinesis_failure_bucket_name
  # pass the cross_account_iot_kinesis_failure_lambda_roles as map once role deployed on client AWS account
}


module "vpn_us" {
  providers = {
    aws = aws.usw1
  }
  source                   = "../../../modules/data_vpn"
  environment_code         = var.APP_STAGE
  ACCOUNT_ID               = data.aws_caller_identity.current.account_id
  AWS_REGION               = data.aws_region.current.name
  vpc_id                   = module.data_vpc_us.vpc_id
  ami_id                   = var.usw1_ami_id
  ssh_public_key_full_path = var.us_ssh_public_key_full_path
  users_path               = "users/user_public_keys/"
  admin_users_path         = "users/admin_public_keys/"
  allowed_cidr_blocks      = ["202.137.242.38/32"] # allow Havelock office
  subnets                  = module.data_vpc_us.public_subnets_ids
  bucket_name              = "data-prod-us-w1-vpn"
  tags                     = merge(local.common_tags, { Application = "vpn+bastion", "Customer Facing" = "false" })
}


module "tableau_bridge_usw1" {
  source = "../../../modules/Tableau/Bridge"
  providers = {
    aws = aws.usw1
  }

  vpc_id                   = module.data_vpc_us.vpc_id
  subnet_id                = module.data_vpc_us.private_subnets_ids[0]
  ssh_public_key_full_path = var.us_ssh_public_key_full_path
  environment_code         = var.APP_STAGE

}