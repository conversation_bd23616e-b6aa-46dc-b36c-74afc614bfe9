module "data_vpc_us_e1" {
  source = "../../../modules/data_vpc"
  providers = {
    aws = aws.us_e1
  }
  vpc_cidr_block         = "10.1.0.0/16"
  aws_availability_zones = ["us-east-1a", "us-east-1b", "us-east-1c"]
  environment_code       = var.APP_STAGE
  vpc_name               = "data-${var.APP_STAGE}"
  tags                   = merge(local.common_tags, { Stack = "Data-VPC", Application = "Data-Infrastructure" })
}

# Setup Data Lake
module "datalake_us_e1" {
  source = "../../../modules/datalake"
  providers = {
    aws = aws.us_e1
  }
  prefix                   = "fm-${var.APP_STAGE}-datalake-1"
  lifecycle_configurations = local.datalake_lifecycle
  tags                     = merge(local.common_tags, { Application = "Data-Lake" })
}

module "bastion_v2_us_e1" {
  source = "../../../modules/bastion_v2"
  providers = {
    aws = aws.us_e1
  }
  environment_code         = var.APP_STAGE
  ACCOUNT_ID               = data.aws_caller_identity.current.account_id
  AWS_REGION               = "us-east-1"
  vpc_id                   = module.data_vpc_us_e1.vpc_id
  ami_id                   = var.use1_ami_id
  ssh_public_key_full_path = "users/main_key_v2.pub"
  users_path               = "users/user_public_keys/"
  admin_users_path         = "users/admin_public_keys/"
  allowed_cidr_blocks      = ["202.137.242.38/32"] # allow Havelock office
  subnets                  = module.data_vpc_us_e1.public_subnets_ids
  bucket_name              = var.bastion_us_e1_s3_bucket_name
  tags                     = merge(local.common_tags, { Application = "Bastion V2", "Customer Facing" = "false" })
}


module "redshift_us_e1" {
  source = "../../../modules/redshift"
  providers = {
    aws = aws.us_e1
  }
  cluster_identifier      = "main-dw-us-east-1"
  aws_account_id          = data.aws_caller_identity.current.account_id
  environment_code        = var.APP_STAGE
  vpc_id                  = module.data_vpc_us_e1.vpc_id
  vpc_cidr                = module.data_vpc_us_e1.vpc_cidr
  subnet_ids              = module.data_vpc_us_e1.private_subnets_ids
  cluster_node_type       = "ra3.large"
  cluster_number_of_nodes = 2
  iam_role_policies = [
    module.datalake_us_e1.datalake_read_write_delete_policy_arn,
    module.datalake_us_e1.datalake_kms_key_policy_arn,
    "arn:aws:iam::aws:policy/AWSGlueConsoleFullAccess"
  ]
  additional_iam_roles           = [module.redshift_kinesis_stream_access_us_e1.kinesis_iam_role_arn]
  allow_ingress_by_sg_ids        = [module.bastion_v2_us_e1.bastion_sg_id]
  security_group_ids             = [module.data_vpc_us_e1.private_resources_sg]
  enable_logging                 = false
  automated_snapshot_rate        = 12 # 1 snapshot every 12 hours
  automated_snapshot_retention   = 7  # keep snapshot for 7 days
  enable_scheduled_action_pause  = false
  enable_scheduled_action_resume = false
  tags                           = merge(local.common_tags, { Application = "Data-Warehouse" })
  enable_s3_unload               = true
  s3_unload_bucket_list = [
    "ptl-lambda-etl"
  ]
}

module "redshift_kinesis_stream_access_us_e1" {
  source = "../../../modules/redshift-cross-acc-kinesis-stream-access"
  providers = {
    aws = aws.us_e1
  }
  cross_account_kinesis_roles                    = var.cross_account_kinesis_roles_us_e1
  redshift_stream_role_name                      = var.redshift_stream_role_name
  redshift_cluster_identifier                    = module.redshift_us_e1.redshift_cluster_identifier
  default_iam_role_arn                           = module.redshift_us_e1.default_iam_role_arn
  iot_to_kinesis_failure_bucket_name             = var.iot_to_kinesis_failure_bucket_name
  cross_account_iot_kinesis_failure_lambda_roles = var.cross_account_iot_kinesis_failure_lambda_roles_us_e1
}

# module "datalake_s3_bucket_policy_us_e1" {
#   source = "../../../modules/kinesis_firehose_cross_account_s3_access"
#   providers = {
#     aws = aws.us_e1
#   }
#   cross_account_roles = "arn:aws:iam::************:root"
#   datalake_name       = "data-dev-rnd-us-east-1"
# }
# module "datalake_s3_bucket_policy_us_e1" {
#   source = "../../../modules/kinesis_firehose_cross_account_s3_access"
#   providers = {
#     aws = aws.us_e1
#   }
#   cross_account_roles = "arn:aws:iam::************:root"
#   datalake_name       = "data-dev-rnd-us-east-1"
# }




module "vpn_us_e1" {
  providers = {
    aws = aws.us_e1
  }
  source                   = "../../../modules/data_vpn"
  environment_code         = var.APP_STAGE
  ACCOUNT_ID               = data.aws_caller_identity.current.account_id
  AWS_REGION               = data.aws_region.current.name
  vpc_id                   = module.data_vpc_us_e1.vpc_id
  ami_id                   = var.use1_ami_id
  ssh_public_key_full_path = var.us_e1_ssh_public_key_full_path
  users_path               = "users/user_public_keys/"
  admin_users_path         = "users/admin_public_keys/"
  allowed_cidr_blocks      = ["202.137.242.38/32"] # allow Havelock office
  subnets                  = module.data_vpc_us_e1.public_subnets_ids
  bucket_name              = var.VPN_US_E1_S3_BUCKET_NAME
  tags                     = merge(local.common_tags, { Application = "vpn+bastion", "Customer Facing" = "false" })
}


#ptl usa lambda etl
module "client_ptl_usa_lambda_etl" {
  source = "../../../modules/clients/ptl_usa/lambda_etl"
  providers = {
    aws = aws.us_e1
  }

  secrets_manager_arns = [
    "arn:aws:secretsmanager:us-east-1:************:secret:main-dw-us-east-1-prod-credential-fDwwJy"
  ]
  redshift_copy_iam        = "arn:aws:iam::************:role/redshift-main-dw-us-east-1-us-east-1"
  redshift_cluster_id      = "main-dw-us-east-1-prod"
  database                 = "prod"
  db_user                  = "dw_master"
  secret_name              = "main-dw-us-east-1-prod-credential"
  region_name              = "us-east-1"
  s3_bucket_name           = "etl-lambda-code-ptl-usa"
  parquet_unload_s3_bucket = "eyecue-ptl-usa-adr-unload-parquet"
  aws_account_id           = data.aws_caller_identity.current.account_id
  notification_arns        = [aws_sns_topic.data_engineering_slack_message_us_e1.arn]
  tags = {
    "Environment" = "prod"
  }
}

module "ptl_usa_ftp_data_share" {
  source = "../../../modules/clients/ptl_usa/lambda_data_export"
  providers = {
    aws = aws.us_e1
  }
  export_iam_role_arn       = "arn:aws:iam::************:role/redshift-main-dw-us-east-1-us-east-1"
  redshift_cluster_id       = "main-dw-us-east-1-prod"
  database_name             = "prod"
  db_username               = "dw_master"
  secret_name               = "main-dw-us-east-1-prod-credential"
  aws_region                = "us-east-1"
  lambda_s3_bucket          = module.client_ptl_usa_lambda_etl.s3_bucket_name
  export_base_partition_key = "ptl_data_export"
  export_s3_bucket_name     = "ptl-lambda-etl"
}

# End PTL-USA

resource "aws_sns_topic" "data_engineering_slack_message_us_e1" {
  provider          = aws.us_e1
  name              = "data-engineering-slack-message-us-e1"
  kms_master_key_id = "alias/aws/sns"
  tags = {
    Environment = "prod"
    Project     = "Data Engineering"
  }
}

resource "aws_sns_topic_subscription" "slack_subscription" {
  provider  = aws.us_e1
  topic_arn = aws_sns_topic.data_engineering_slack_message_us_e1.arn
  protocol  = "email"
  endpoint  = "<EMAIL>"
}


#cul usa lambda etl
module "client_cul_usa_lambda_etl" {
  source = "../../../modules/clients/cul_usa/lambda_etl"
  providers = {
    aws = aws.us_e1
  }

  secrets_manager_arns = [
    "arn:aws:secretsmanager:us-east-1:************:secret:main-dw-us-east-1-prod-credential-fDwwJy"
  ]

  redshift_cluster_id = "main-dw-us-east-1-prod"
  database            = "prod"
  db_user             = "dw_master"
  secret_name         = "main-dw-us-east-1-prod-credential"
  region_name         = "us-east-1"
  s3_bucket_name      = "etl-lambda-code-cul-usa"
  notification_arns   = [aws_sns_topic.data_engineering_slack_message_us_e1.arn]
  tags = {
    "Environment" = "prod"
  }
}

#cfa usa lambda etl
module "client_cfa_usa_lambda_etl" {
  source = "../../../modules/clients/cfa_usa/lambda_etl"
  providers = {
    aws = aws.us_e1
  }

  secrets_manager_arns = [
    "arn:aws:secretsmanager:us-east-1:************:secret:main-dw-us-east-1-prod-credential-fDwwJy"
  ]

  redshift_cluster_id                            = "main-dw-us-east-1-prod"
  database                                       = "prod"
  db_user                                        = "dw_master"
  secret_name                                    = "main-dw-us-east-1-prod-credential"
  region_name                                    = "us-east-1"
  s3_bucket_name                                 = "etl-lambda-code-cfa-usa"
  redshift_copy_iam                              = "arn:aws:iam::************:role/redshift-main-dw-us-east-1-us-east-1"
  dynamodb_table_name                            = "etl_hvi_roi_report_meta_data"
  data_select_gap_hvi_roi_mins                   = 240
  duplicate_check_gap_hvi_roi_mins               = 2160
  next_execution_date_gap_hvi_roi_mins           = 10
  destination_schema_hvi_roi_reporting           = "eyecue_cfa_usa"
  destination_table_hvi_roi_reporting            = "hvi_reporting_automation"
  redshift_copy_s3_bucket                        = "data-hvi-report-redshift-copy"
  notification_arns                              = [aws_sns_topic.data_engineering_slack_message_us_e1.arn]
  aws_account_id                                 = data.aws_caller_identity.current.account_id
  redshift_query_id_tracking_dynamodb_table_name = "redshift_query_id_tracker"
  tags = {
    "Environment" = "prod"
  }
}
#bkg-usa lambda etl
module "client_bkg_usa_lambda_etl" {
  source = "../../../modules/clients/bkg_usa/lambda_etl"
  providers = {
    aws = aws.us_e1
  }

  secrets_manager_arns = [
    "arn:aws:secretsmanager:us-east-1:************:secret:main-dw-us-east-1-prod-credential-fDwwJy"
  ]

  redshift_cluster_id = "main-dw-us-east-1-prod"
  database            = "prod"
  db_user             = "dw_master"
  secret_name         = "main-dw-us-east-1-prod-credential"
  region_name         = "us-east-1"
  s3_bucket_name      = "etl-lambda-code-bkg-usa"
  notification_arns   = [aws_sns_topic.data_engineering_slack_message_us_e1.arn]
  tags = {
    "Environment" = "prod"
  }
}
#pop-usa lambda etl
module "client_pop_usa_lambda_etl" {
  source = "../../../modules/clients/pop_usa/lambda_etl"
  providers = {
    aws = aws.us_e1
  }

  secrets_manager_arns = [
    "arn:aws:secretsmanager:us-east-1:************:secret:main-dw-us-east-1-prod-credential-fDwwJy"
  ]

  redshift_cluster_id = "main-dw-us-east-1-prod"
  database            = "prod"
  db_user             = "dw_master"
  secret_name         = "main-dw-us-east-1-prod-credential"
  region_name         = "us-east-1"
  s3_bucket_name      = "etl-lambda-code-pop-usa"
  notification_arns   = [aws_sns_topic.data_engineering_slack_message_us_e1.arn]
  tags = {
    "Environment" = "prod"
  }
}

module "quicksight_reporting_setup_cfa_usa_prod" {

  source = "../../../modules/quicksight_reporting_setup"
  providers = {
    aws = aws.us_e1
  }

  environment_code                       = var.APP_STAGE
  client_name                            = "cfa-usa"
  report_tracking_dynamodb_table_name    = "quicksight-report-export-details-cfa-usa"
  execution_tracking_dynamodb_table_name = "quicksight-report-execution-tracker-cfa-usa"
  dynamodb_pitr_enabled                  = true
  sqs_queue_names = [
    "quicksight-report-initialization-queue-cfa-usa",
    "quicksight-report-email-send-queue-cfa-usa"
  ]
  sqs_report_initialization_queue_name       = "quicksight-report-initialization-queue-cfa-usa"
  sqs_report_email_queue_name                = "quicksight-report-email-send-queue-cfa-usa"
  redshift_cluster_id                        = "main-dw-us-east-1-prod"
  database                                   = "prod"
  db_user                                    = "dw_master"
  region_name                                = "us-east-1"
  quicksight_reporting_s3_bucket_name        = "quicksight-pdf-report-exporting-cfa-usa-prod"
  quicksight_report_pdf_init_lambda_name     = "quicksight-report-generation-initializer-cfa-usa"
  ec2_ami_id                                 = "ami-0e1bed4f06a3b463d"
  ec2_instance_type                          = "t3a.xlarge"
  ec2_root_block_device_encrypted            = true
  ec2_root_block_device_volume_size          = 30
  ec2_server_name                            = "quicksight-report-generator-cfa-usa"
  ec2_ssh_public_key_full_path               = "users/admin_public_keys/kasun.bamunusingha"
  sns_topic_name_for_errors                  = "quicksight-report-generation-notifications-cfa-usa"
  aws_account_id                             = data.aws_caller_identity.current.account_id
  embed_url_generation_lambda_name           = "quicksight-embed-url-for-pdf-generation-cfa-usa"
  quicksight_dashboard_id                    = "cf5bb8c7-cdde-4ea8-ac48-a2e0ccf7b922"
  dashboard_sheet_id                         = ""
  ses_region_name                            = "us-east-1"
  ses_sender_email                           = "<EMAIL>"
  bcc_email                                  = "<EMAIL>"
  report_generation_finalization_lambda_name = "quicksight-report-generation-finalization-cfa-usa"
  email_sending_lambda_name                  = "quicksight-report-generation-email-sender-cfa-usa"
  execution_time_zone                        = "America/New_York"
  execution_time_window_start_time           = "04:50"
  execution_time_window_end_time             = "05:50"
  pdf_render_view_width_px                   = "1920"
  pdf_render_view_height_px                  = "4000"
  pdf_render_waiting_time_ms                 = 180000
  pdf_print_page_size                        = "A2"
  row_level_security_enabled_for_dashboard   = "False"

  tags = merge(
    local.common_tags,
    {
      "Application"     = "Quicksight-Reporting-For-CFA-USA"
      "Terraform"       = "True"
      Environment       = "prod"
      Product           = "Eyecue"
      Customer          = "CFA-USA"
      Serverless        = "False"
      Stack             = "Application"
      Owner             = "data-team"
      Squad             = "Data"
      "Customer Facing" = "True"
      System            = "Ubuntu 22.04 - Node 22.13.0 - Python 3.12"
      MultiRegion       = "False"
      Backup            = "False"
  })
}

module "quicksight_reporting_setup_cfa_usa_prod_weekly" {

  source = "../../../modules/quicksight_reporting_setup"
  providers = {
    aws = aws.us_e1
  }

  environment_code                       = var.APP_STAGE
  client_name                            = "cfa-usa-weekly"
  report_tracking_dynamodb_table_name    = "quicksight-report-export-details-cfa-usa-weekly"
  execution_tracking_dynamodb_table_name = "quicksight-report-execution-tracker-cfa-usa-weekly"
  dynamodb_pitr_enabled                  = true
  sqs_queue_names = [
    "quicksight-report-initialization-queue-cfa-usa-weekly",
    "quicksight-report-email-send-queue-cfa-usa-weekly"
  ]
  sqs_report_initialization_queue_name       = "quicksight-report-initialization-queue-cfa-usa-weekly"
  sqs_report_email_queue_name                = "quicksight-report-email-send-queue-cfa-usa-weekly"
  redshift_cluster_id                        = "main-dw-us-east-1-prod"
  database                                   = "prod"
  db_user                                    = "dw_master"
  region_name                                = "us-east-1"
  quicksight_reporting_s3_bucket_name        = "quicksight-pdf-report-exporting-cfa-usa-prod-weekly"
  quicksight_report_pdf_init_lambda_name     = "quicksight-report-generation-initializer-cfa-usa-weekly"
  ec2_ami_id                                 = "ami-0e1bed4f06a3b463d"
  ec2_instance_type                          = "t3a.xlarge"
  ec2_root_block_device_encrypted            = true
  ec2_root_block_device_volume_size          = 30
  ec2_server_name                            = "quicksight-report-generator-cfa-usa-weekly"
  ec2_ssh_public_key_full_path               = "users/admin_public_keys/kasun.bamunusingha"
  sns_topic_name_for_errors                  = "quicksight-report-generation-notifications-cfa-usa-weekly"
  aws_account_id                             = data.aws_caller_identity.current.account_id
  embed_url_generation_lambda_name           = "quicksight-embed-url-for-pdf-generation-cfa-usa-weekly"
  quicksight_dashboard_id                    = "1ae4c135-be1e-48cb-8eae-60c6014027e4"
  dashboard_sheet_id                         = ""
  ses_region_name                            = "us-east-1"
  ses_sender_email                           = "<EMAIL>"
  bcc_email                                  = "<EMAIL>"
  report_generation_finalization_lambda_name = "quicksight-report-generation-finalization-cfa-usa-weekly"
  email_sending_lambda_name                  = "quicksight-report-generation-email-sender-cfa-usa-weekly"
  execution_time_zone                        = "America/New_York"
  execution_time_window_start_time           = "04:50"
  execution_time_window_end_time             = "05:50"
  pdf_render_view_width_px                   = "1920"
  pdf_render_view_height_px                  = "10000"
  pdf_render_waiting_time_ms                 = 180000
  pdf_print_page_size                        = "A2"
  row_level_security_enabled_for_dashboard   = "False"

  tags = merge(
    local.common_tags,
    {
      "Application"     = "Quicksight-Reporting-For-CFA-USA-WEEKLY"
      "Terraform"       = "True"
      Environment       = "prod"
      Product           = "Eyecue"
      Customer          = "CFA-USA"
      Serverless        = "False"
      Stack             = "Application"
      Owner             = "data-team"
      Squad             = "Data"
      "Customer Facing" = "True"
      System            = "Ubuntu 22.04 - Node 22.13.0 - Python 3.12"
      MultiRegion       = "False"
      Backup            = "False"
  })
}


module "tableau_bridge_us_e1" {
  source = "../../../modules/Tableau/Bridge"
  providers = {
    aws = aws.us_e1
  }

  vpc_id                   = module.data_vpc_us_e1.vpc_id
  subnet_id                = module.data_vpc_us_e1.private_subnets_ids[0]
  ssh_public_key_full_path = var.us_e1_ssh_public_key_full_path
  environment_code         = var.APP_STAGE
}