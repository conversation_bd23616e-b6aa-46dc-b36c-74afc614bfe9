
terraform {
  required_version = "~> 1.10"
  backend "s3" {
    encrypt        = true
    bucket         = "fingermark-data-terraform"
    region         = "ap-southeast-2"
    key            = "data-prod/terraform.tfstate"
    dynamodb_table = "terraform-state"
    assume_role = {
      role_arn     = "arn:aws:iam::047783385012:role/TerraformBackendAccess"
      session_name = "default"
    }
  }
}
