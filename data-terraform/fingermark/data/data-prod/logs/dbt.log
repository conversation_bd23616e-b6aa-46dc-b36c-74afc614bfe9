[0m11:46:53.106839 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'start', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x10877c890>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x108c4e840>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x108c4e360>]}


============================== 11:46:53.110235 | 1579a661-1c7b-4953-88e5-badf8295257d ==============================
[0m11:46:53.110235 [info ] [MainThread]: Running with dbt=1.9.0
[0m11:46:53.110636 [debug] [MainThread]: running dbt with arguments {'printer_width': '80', 'indirect_selection': 'eager', 'log_cache_events': 'False', 'write_json': 'True', 'partial_parse': 'True', 'cache_selected_only': 'False', 'profiles_dir': '/Users/<USER>/.dbt', 'fail_fast': 'False', 'version_check': 'True', 'log_path': 'logs', 'debug': 'False', 'warn_error': 'None', 'use_colors': 'True', 'use_experimental_parser': 'False', 'no_print': 'None', 'quiet': 'False', 'empty': 'False', 'warn_error_options': 'WarnErrorOptions(include=[], exclude=[])', 'static_parser': 'True', 'invocation_command': 'dbt run', 'log_format': 'default', 'target_path': 'None', 'introspect': 'True', 'send_anonymous_usage_stats': 'True'}
[0m11:46:53.111051 [error] [MainThread]: Encountered an error:
Runtime Error
  No dbt_project.yml found at expected path /Users/<USER>/Documents/FIngermark/projects/data-terraform/fingermark/data/data-prod/dbt_project.yml
  Verify that each entry within packages.yml (and their transitive dependencies) contains a file named dbt_project.yml
  
[0m11:46:53.120055 [debug] [MainThread]: Resource report: {"command_name": "run", "command_success": false, "command_wall_clock_time": 0.08388117, "process_in_blocks": "0", "process_kernel_time": 0.191376, "process_mem_max_rss": "102072320", "process_out_blocks": "0", "process_user_time": 0.61602}
[0m11:46:53.120380 [debug] [MainThread]: Command `dbt run` failed at 11:46:53.120317 after 0.08 seconds
[0m11:46:53.120688 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'end', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x108bf1760>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x1082c8380>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x108c4e7e0>]}
[0m11:46:53.120937 [debug] [MainThread]: Flushing usage events
[0m11:46:54.164000 [debug] [MainThread]: An error was encountered while trying to flush usage events
