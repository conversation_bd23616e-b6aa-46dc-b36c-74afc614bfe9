# Setup VPC and 3 public subnets and 3 private subnets
module "data_vpc" {
  source                 = "../../../modules/data_vpc"
  vpc_cidr_block         = "10.0.0.0/16"
  aws_availability_zones = ["ap-southeast-2a", "ap-southeast-2b", "ap-southeast-2c"]
  environment_code       = var.APP_STAGE
  vpc_name               = "data-${var.APP_STAGE}"
  tags                   = merge(local.common_tags, { Stack = "Data-VPC", Application = "Data-Infrastructure" })
}

# Setup Data Lake
module "datalake" {
  source                   = "../../../modules/datalake"
  prefix                   = "fm-${var.APP_STAGE}-datalake-1"
  lifecycle_configurations = local.datalake_lifecycle
  tags                     = merge(local.common_tags, { Application = "Data-Lake" })
}

module "athena" {
  source = "../../../modules/athena"
  # To update primary workgroup, import it first by running:
  # terraform import module.athena.aws_athena_workgroup.primary primary
  workgroup_name   = "primary"
  environment_code = var.APP_STAGE
  tags             = merge(local.common_tags, { Application = "Data-Lake" })
}

module "bastion" {
  source                   = "../../../modules/bastion"
  vpc_id                   = module.data_vpc.vpc_id
  ami_id                   = var.aps2_ami_id
  user_data_template       = "users/amazon-linux.sh"
  ssh_public_key_full_path = "users/main_key.pub"
  users_path               = "users/public_keys/"
  allowed_cidr_blocks      = ["**************/32"] # allow Havelock office
  subnets                  = module.data_vpc.public_subnets_ids
  tags                     = merge(local.common_tags, { Application = "Bastion" })
}

module "redshift" {
  source             = "../../../modules/redshift"
  cluster_identifier = "primary-dw"
  aws_account_id     = data.aws_caller_identity.current.account_id
  environment_code   = var.APP_STAGE
  vpc_id             = module.data_vpc.vpc_id
  vpc_cidr           = module.data_vpc.vpc_cidr
  subnet_ids         = module.data_vpc.private_subnets_ids
  iam_role_policies = [
    module.datalake.datalake_read_write_delete_policy_arn,
    module.datalake.datalake_kms_key_policy_arn,
    "arn:aws:iam::aws:policy/AWSGlueConsoleFullAccess"
  ]
  additional_iam_roles         = [module.redshift_kinesis_stream_access.kinesis_iam_role_arn]
  allow_ingress_by_sg_ids      = [module.bastion_v2.bastion_sg_id, module.batch_jobs.batch_sg_id]
  security_group_ids           = [module.data_vpc.private_resources_sg]
  enable_logging               = false
  automated_snapshot_rate      = 12 # 1 snapshot every 12 hours
  automated_snapshot_retention = 7  # keep snapshot for 7 days
  tags                         = merge(local.common_tags, { Application = "Data-Warehouse" })
}

module "batch_jobs" {
  source                = "../../../modules/batch_jobs"
  environment_code      = var.APP_STAGE
  batch_id              = "dbt"
  vpc_id                = module.data_vpc.vpc_id
  compute_subnet_ids    = module.data_vpc.private_subnets_ids
  security_group_ids    = [module.data_vpc.private_resources_sg]
  create_static_website = true
  tags                  = merge(local.common_tags, { Application = "Data-Images" })
}

module "sagemaker_notebook" {
  source             = "../../../modules/sagemaker_notebook"
  environment_code   = var.APP_STAGE
  aws_account_id     = data.aws_caller_identity.current.account_id
  notebook_prefix    = "analyst-notebook"
  vpc_id             = module.data_vpc.vpc_id
  subnet_id          = module.data_vpc.private_subnets_ids[0]
  security_group_ids = [module.data_vpc.private_resources_sg]
  iam_role_policies = [
    module.datalake.datalake_read_policy_arn,
    module.datalake.datalake_kms_key_policy_arn,
    "arn:aws:iam::aws:policy/AWSCodeCommitPowerUser",
    "arn:aws:iam::aws:policy/AmazonRedshiftReadOnlyAccess",
    "arn:aws:iam::aws:policy/service-role/AWSGlueServiceNotebookRole"
  ]
  on_start_script  = base64encode(file("scripts/sagemaker-notebook-on-start.sh"))
  on_create_script = base64encode(file("scripts/sagemaker-notebook-on-create.sh"))
  tags             = merge(local.common_tags, { Application = "Data-Notebooks" })
}


module "bastion_v2" {
  source                   = "../../../modules/bastion_v2"
  ACCOUNT_ID               = data.aws_caller_identity.current.account_id
  vpc_id                   = module.data_vpc.vpc_id
  ami_id                   = var.aps2_ami_id
  ssh_public_key_full_path = "users/main_key_v2.pub"
  users_path               = "users/user_public_keys/"
  admin_users_path         = "users/admin_public_keys/"
  allowed_cidr_blocks      = ["**************/32"] # allow Havelock office
  subnets                  = module.data_vpc.public_subnets_ids
  bucket_name              = var.bastion_s3_bucket_name
  tags                     = merge(local.common_tags, { Application = "Bastion V2", "Customer Facing" = "false" })
}
module "redshift_kinesis_stream_access" {
  source                      = "../../../modules/redshift-cross-acc-kinesis-stream-access"
  cross_account_kinesis_roles = var.cross_account_kinesis_roles_ap_se2
  redshift_cluster_identifier = module.redshift.redshift_cluster_identifier
  default_iam_role_arn        = module.redshift.default_iam_role_arn
  redshift_stream_role_name   = var.redshift_stream_role_name
}

module "datalake_s3_bucket_policy" {
  source              = "../../../modules/kinesis_firehose_cross_account_s3_access"
  cross_account_roles = var.cross_account_roles
  datalake_name       = "data-dev-rnd"
}


module "vpn_au" {
  source                   = "../../../modules/data_vpn"
  environment_code         = var.APP_STAGE
  ACCOUNT_ID               = data.aws_caller_identity.current.account_id
  AWS_REGION               = data.aws_region.current.name
  vpc_id                   = module.data_vpc.vpc_id
  ami_id                   = var.aps2_ami_id
  ssh_public_key_full_path = var.ap_se2_ssh_public_key_full_path
  users_path               = "users/user_public_keys/"
  admin_users_path         = "users/admin_public_keys/"
  allowed_cidr_blocks      = ["**************/32"] # allow Havelock office
  subnets                  = module.data_vpc.public_subnets_ids
  bucket_name              = var.VPN_AU_S3_BUCKET_NAME
  tags                     = merge(local.common_tags, { Application = "vpn+bastion", "Customer Facing" = "false" })
}

# module "quicksight_reporting_setup" {
#   source                                 = "../../../modules/quicksight_reporting_setup"
#   environment_code                       = var.APP_STAGE
#   client_name                            = "bkg-nzl"
#   report_tracking_dynamodb_table_name    = "quicksight-report-export-details-bkg-nzl"
#   execution_tracking_dynamodb_table_name = "quicksight-report-execution-tracker-bkg-nzl"
#   dynamodb_pitr_enabled                  = false
#   sqs_queue_names = [
#     "quicksight-report-initialization-queue-bkg-nzl",
#     "quicksight-report-email-send-queue-bkg-nzl"
#   ]
#   sqs_report_initialization_queue_name       = "quicksight-report-initialization-queue-bkg-nzl"
#   sqs_report_email_queue_name                = "quicksight-report-email-send-queue-bkg-nzl"
#   redshift_cluster_id                        = "primary-dw-dev"
#   database                                   = "dev"
#   db_user                                    = "dw_master"
#   region_name                                = var.AWS_REGION
#   quicksight_reporting_s3_bucket_name        = "quicksight-pdf-report-exporting-bkg-nzl"
#   quicksight_report_pdf_init_lambda_name     = "quicksight-report-generation-initializer-bkg-nzl"
#   ec2_server_name                            = "quicksight-report-generator-bkg-nzl"
#   ec2_ami_id                                 = "ami-040e71e7b8391cae4"
#   ec2_instance_type                          = "t3a.xlarge"
#   ec2_root_block_device_encrypted            = true
#   ec2_root_block_device_volume_size          = 30
#   ec2_ssh_public_key_full_path               = "users/admin_public_keys/kasun.bamunusingha"
#   sns_topic_name_for_errors                  = "quicksight-report-generation-notifications-bkg-nzl"
#   aws_account_id                             = data.aws_caller_identity.current.account_id
#   embed_url_generation_lambda_name           = "quicksight-embed-url-for-pdf-generation-bkg-nzl"
#   quicksight_dashboard_id                    = "53f413bc-91f3-4a99-af4e-29991ab06df2"
#   dashboard_sheet_id                         = "53f413bc-91f3-4a99-af4e-29991ab06df2_f24af872-31e9-48d3-b946-c32254528473"
#   ses_region_name                            = "ap-southeast-2"
#   ses_sender_email                           = "<EMAIL>"
#   bcc_email                                  = "<EMAIL>"
#   report_generation_finalization_lambda_name = "quicksight-report-generation-finalization-bkg-nzl"
#   email_sending_lambda_name                  = "quicksight-report-generation-email-sender-bkg-nzl"
#   execution_time_zone                        = "Pacific/Auckland"
#   execution_time_window_start_time           = "06:30"
#   execution_time_window_end_time             = "07:30"
#   pdf_render_view_width_px                   = "1920"
#   pdf_render_view_height_px                  = "4000"
#   pdf_render_waiting_time_ms                 = 120000
#   pdf_print_page_size                        = "A2"
#   row_level_security_enabled_for_dashboard   = "True"

#   tags = merge(
#     local.common_tags,
#     {
#       "Application"     = "Quicksight-Reporting"
#       "Customer Facing" = "false"
#       "Author"          = "Data-Team"
#       "MultiRegion"     = "false"
#       "Product"         = "Data"
#       "Squad"           = "Data"
#       "Stack"           = "ETL"
#       "Terraform"       = "true"
#   })
# }
