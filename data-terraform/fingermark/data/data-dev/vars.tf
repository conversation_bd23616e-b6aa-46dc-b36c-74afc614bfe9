###  Destination AWS Account  ###
variable "AWS_REGION" {
  default = "ap-southeast-2"
}

variable "APP_STAGE" {
  description = "The environment indicator. Options [dev, prod]"
  default     = "dev"
}

variable "cross_account_kinesis_roles_ap_se2" {
  type = list(object({
    account_id = string
    role_name  = string
  }))
  default = [
    { account_id = "************", role_name = "redshift_stream_aaccess_role" }
  ]
}

variable "cross_account_kinesis_roles_us_e1" {
  type = list(object({
    account_id = string
    role_name  = string
  }))
  default = [
    { account_id = "************", role_name = "redshift_stream_aaccess_role" }
  ]
}

variable "bastion_s3_bucket_name" {
  description = "S3 bucket name use"
  default     = "data-dev-bastion-v2-ap-southeast-2"
}

variable "bastion_us_e1_s3_bucket_name" {
  description = "S3 bucket name use"
  default     = "data-dev-bastion-v2-us-east-1"
}

variable "aps2_ami_id" {
  default = "ami-0578ad653d72d7de4"
}

variable "use1_ami_id" {
  default = "ami-03c7d01cf4dedc891"
}


variable "redshift_stream_role_name" {
  default = "redshift_stream_role"
}


variable "ENV" {
  default = "development"
}
variable "cfa_data_unload_role" {
  type    = string
  default = "arn:aws:iam::************:role/cfa_redshift_unload"
}


variable "cross_account_roles" {
  type = list(string)
  default = ["arn:aws:iam::************:root"
  ]
}


variable "cross_account_roles_us_e1" {
  type = list(string)
  default = ["arn:aws:iam::************:root"
  ]
}


variable "us_e1_ssh_public_key_full_path" {
  description = "The full path to the SSH public key file"
  default     = "users/admin_public_keys/rusiru.bulathgamage"

}

variable "VPN_US_E1_S3_BUCKET_NAME" {
  description = "S3 bucket name use"
  default     = "data-dev-vpn-us-east-1"
}

variable "ap_se2_ssh_public_key_full_path" {
  description = "The full path to the SSH public key file"
  default     = "users/admin_public_keys/rusiru.bulathgamage"

}

variable "VPN_AU_S3_BUCKET_NAME" {
  description = "S3 bucket name AU"
  default     = "data-dev-au-vpn"
}

variable "vpc_flow_logs_destination" {
  description = "Arn of the S3 bucket to store VPC flow logs."
  default     = "arn:aws:s3:::fingermark-vpc-logs"
}

# ===============================================
# CloudWatch Alarms
# ===============================================
variable "cw_alarms_sns_topic_arns_region_lookup" {
  description = <<-DOC
    Map of region names to SNS topic ARNs for CloudWatch alarm notifications. The SNS topic ARNs
    should have already been created in a centralised AWS account (infra #************) using the
    module `modules/cw_alarm_notifications_sns_topic`.
  DOC
  type        = map(string)
  default = {
    "ap-southeast-2" = "arn:aws:sns:ap-southeast-2:************:cloudwatch-alarms-org"
    "ca-central-1"   = "arn:aws:sns:ca-central-1:************:cloudwatch-alarms-org"
    "us-east-1"      = "arn:aws:sns:us-east-1:************:cloudwatch-alarms-org"
    "us-west-1"      = "arn:aws:sns:us-west-1:************:cloudwatch-alarms-org"
    "us-west-2"      = "arn:aws:sns:us-west-2:************:cloudwatch-alarms-org"
  }
}
