import sys
import awswrangler as wr


with wr.redshift.connect("redshift_us_east_1") as connection:
    with connection.cursor() as cursor:
        cursor.execute("call dev.dev_stg.sp_sync_fact_cfa_eyecue_hvi();")
        cursor.execute("call dev.dev_stg.sp_sync_fact_cfa_eyecue_roi();")
        cursor.execute("call dev.dev_stg.sp_sync_agg_json_data();")
        cursor.execute("commit")
        cursor.execute("refresh MATERIALIZED VIEW dev.dev_prod.mv_cfa_eyecue_roi_lengths;")
        cursor.execute("commit")
        print("Stored procedure executed successfully!")