# =======================================================
# CloudWatch Alarms: DynamoDB Tables in ap-southeast-2 (SOC2 requirement)
# =======================================================

# Define the DynamoDB tables to monitor in ap-southeast-2
locals {
  dynamodb_tables_ap_southeast_2 = {
    "data_api_master_privileges" = "data_api_master_privileges"
    "data_api_master_roles"      = "data_api_master_roles"
    "data_api_user_roles"        = "data_api_user_roles"
  }
}

module "dynamodb_cw_alarms_ap_southeast_2" {
  source = "git::*****************:fingermarkltd/fingermark-terraform.git//modules/dynamodb_cw_alarms?ref=master"

  sns_topic_arns = [var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]]

  # Configure RCU alarms for all tables
  cw_alarm_config_ddb_table_consumed_rcu = {
    for id, table in local.dynamodb_tables_ap_southeast_2 : id => {
      table_name = table
    }
  }

  # Configure WCU alarms for all tables
  cw_alarm_config_ddb_table_consumed_wcu = {
    for id, table in local.dynamodb_tables_ap_southeast_2 : id => {
      table_name = table
    }
  }

  # We're not configuring GSI alarms here as we don't have specific GSI information for these tables
  # If GSIs need to be monitored, they can be added later

  tags = {
    Environment = "Development"
    Application = "DynamoDB-Monitoring"
    Squad       = "Platform team"
  }
}
