# =======================================================
# CloudWatch Alarms: RDS instances (SOC2 requirement)
# =======================================================

# Define the RDS instances to monitor by region
locals {
  rds_instances = {
    ap_southeast_2 = {
      "demo-1-instance-1" = "demo-1-instance-1"
    }
  }
}

module "rds_alarms_au" {
  source = "git::*****************:fingermarkltd/fingermark-terraform.git//modules/rds_cw_alarms?ref=master"

  sns_topic_arns = [var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]]

  # Configure all alarm types for each instance
  cw_alarm_config_rds_cpu_util           = { for id, instance in local.rds_instances.ap_southeast_2 : id => { identifier = instance } }
  cw_alarm_config_rds_mem_free           = { for id, instance in local.rds_instances.ap_southeast_2 : id => { identifier = instance } }
  cw_alarm_config_rds_disk_queue_depth   = { for id, instance in local.rds_instances.ap_southeast_2 : id => { identifier = instance } }
  cw_alarm_config_rds_write_iops         = { for id, instance in local.rds_instances.ap_southeast_2 : id => { identifier = instance } }
  cw_alarm_config_rds_read_iops          = { for id, instance in local.rds_instances.ap_southeast_2 : id => { identifier = instance } }
  cw_alarm_config_rds_free_storage_space = { for id, instance in local.rds_instances.ap_southeast_2 : id => { identifier = instance } }

  tags = {
    Environment = "Development"
    Application = "RDS-Monitoring"
    Squad       = "Platform team"
  }
}
