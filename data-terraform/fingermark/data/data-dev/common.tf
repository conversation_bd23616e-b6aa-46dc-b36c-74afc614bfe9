# Start here
locals {
  common_tags = {
    Terraform   = "true"
    Author      = "Data-Team"
    Backup      = "false"
    Environment = var.APP_STAGE
  }

  datalake_lifecycle = [
    {
      id      = "delete-rds-copies"
      enabled = true
      filter = {
        prefix = "external/rds/"
      }

      expiration = {
        days = 60
      }

      noncurrent_version_expiration = {
        days = 7
      }
    },
    {
      id      = "transition-eyecue-archive"
      enabled = true
      filter = {
        prefix = "eyecue-archive/"
      }

      transition = [
        {
          days          = 30
          storage_class = "ONEZONE_IA"
        },
        {
          days          = 60
          storage_class = "DEEP_ARCHIVE"
        }
      ]
    },
    {
      id      = "delete-noncurrent-version"
      enabled = true
      noncurrent_version_expiration = {
        days = 30
      }
    }
  ]
}

module "assume_role" {
  source            = "git::*****************:fingermarkltd/fingermark-terraform.git//modules/fingermark_users_assume_role"
  roles             = ["AdminAccess", "PowerAccess", "DevAccess", "DeployerAccess", "StorageAccess", "DataScientist"]
  cloudcraft_access = true
}

module "budget" {
  source = "../../../modules/aws_budgets"
}


module "iam_password_policy" {
  source = "git::*****************:fingermarkltd/fingermark-terraform.git//modules/iam_password_policy"
}