module "data_vpc_us_e1" {
  source = "../../../modules/data_vpc"
  providers = {
    aws = aws.us_e1
  }
  vpc_cidr_block         = "10.1.0.0/16"
  aws_availability_zones = ["us-east-1a", "us-east-1b", "us-east-1c"]
  environment_code       = var.APP_STAGE
  vpc_name               = "data-${var.APP_STAGE}"
  tags                   = merge(local.common_tags, { Stack = "Data-VPC", Application = "Data-Infrastructure" })
}

# Setup Data Lake
module "datalake_us_e1" {
  source = "../../../modules/datalake"
  providers = {
    aws = aws.us_e1
  }
  prefix                   = "fm-${var.APP_STAGE}-datalake-1"
  lifecycle_configurations = local.datalake_lifecycle
  tags                     = merge(local.common_tags, { Application = "Data-Lake" })
}

module "bastion_v2_us_e1" {
  source = "../../../modules/bastion_v2"
  providers = {
    aws = aws.us_e1
  }
  environment_code         = var.APP_STAGE
  ACCOUNT_ID               = data.aws_caller_identity.current.account_id
  AWS_REGION               = "us-east-1"
  vpc_id                   = module.data_vpc_us_e1.vpc_id
  ami_id                   = var.use1_ami_id
  ssh_public_key_full_path = "users/main_key_v2.pub"
  users_path               = "users/user_public_keys/"
  admin_users_path         = "users/admin_public_keys/"
  allowed_cidr_blocks      = ["202.137.242.38/32"] # allow Havelock office
  subnets                  = module.data_vpc_us_e1.public_subnets_ids
  bucket_name              = var.bastion_us_e1_s3_bucket_name
  tags                     = merge(local.common_tags, { Application = "Bastion V2", "Customer Facing" = "false" })
}


module "redshift_us_e1" {
  source = "../../../modules/redshift"
  providers = {
    aws = aws.us_e1
  }
  cluster_identifier = "main-dw-us-east-1"
  aws_account_id     = data.aws_caller_identity.current.account_id
  environment_code   = var.APP_STAGE
  vpc_id             = module.data_vpc_us_e1.vpc_id
  vpc_cidr           = module.data_vpc_us_e1.vpc_cidr
  subnet_ids         = module.data_vpc_us_e1.private_subnets_ids
  iam_role_policies = [
    module.datalake_us_e1.datalake_read_write_delete_policy_arn,
    module.datalake_us_e1.datalake_kms_key_policy_arn,
    "arn:aws:iam::aws:policy/AWSGlueConsoleFullAccess"
  ]
  additional_iam_roles           = [module.redshift_kinesis_stream_access_us_e1.kinesis_iam_role_arn]
  allow_ingress_by_sg_ids        = [module.bastion_v2_us_e1.bastion_sg_id]
  security_group_ids             = [module.data_vpc_us_e1.private_resources_sg]
  enable_logging                 = false
  automated_snapshot_rate        = 24 # 1 snapshot every 12 hours
  automated_snapshot_retention   = 7  # keep snapshot for 7 days
  enable_scheduled_action_pause  = true
  enable_scheduled_action_resume = true
  tags                           = merge(local.common_tags, { Application = "Data-Warehouse" })
}


module "redshift_kinesis_stream_access_us_e1" {
  source = "../../../modules/redshift-cross-acc-kinesis-stream-access"
  providers = {
    aws = aws.us_e1
  }
  cross_account_kinesis_roles = var.cross_account_kinesis_roles_us_e1
  redshift_stream_role_name   = var.redshift_stream_role_name
  redshift_cluster_identifier = module.redshift_us_e1.redshift_cluster_identifier
  default_iam_role_arn        = module.redshift_us_e1.default_iam_role_arn
}

module "datalake_s3_bucket_policy_us_e1" {
  source = "../../../modules/kinesis_firehose_cross_account_s3_access"
  providers = {
    aws = aws.us_e1
  }
  cross_account_roles = var.cross_account_roles_us_e1
  datalake_name       = "data-dev-rnd-us-east-1"
}


module "vpn_us_e1" {
  providers = {
    aws = aws.us_e1
  }
  source                   = "../../../modules/data_vpn"
  environment_code         = var.APP_STAGE
  ACCOUNT_ID               = data.aws_caller_identity.current.account_id
  AWS_REGION               = data.aws_region.current.name
  vpc_id                   = module.data_vpc_us_e1.vpc_id
  ami_id                   = var.use1_ami_id
  ssh_public_key_full_path = var.us_e1_ssh_public_key_full_path
  users_path               = "users/user_public_keys/"
  admin_users_path         = "users/admin_public_keys/"
  allowed_cidr_blocks      = ["202.137.242.38/32"] # allow Havelock office
  subnets                  = module.data_vpc_us_e1.public_subnets_ids
  bucket_name              = var.VPN_US_E1_S3_BUCKET_NAME
  tags                     = merge(local.common_tags, { Application = "vpn+bastion", "Customer Facing" = "false" })
}

# module "quicksight_reporting_setup_cfa_usa" {
#   providers = {
#     aws = aws.us_e1
#   }
#   source                                 = "../../../modules/quicksight_reporting_setup"
#   environment_code                       = var.APP_STAGE
#   client_name                            = "cfa-usa"
#   report_tracking_dynamodb_table_name    = "quicksight-report-export-details-cfa-usa"
#   execution_tracking_dynamodb_table_name = "quicksight-report-execution-tracker-cfa-usa"
#   dynamodb_pitr_enabled                  = false
#   sqs_queue_names = [
#     "quicksight-report-initialization-queue-cfa-usa",
#     "quicksight-report-email-send-queue-cfa-usa"
#   ]
#   sqs_report_initialization_queue_name       = "quicksight-report-initialization-queue-cfa-usa"
#   sqs_report_email_queue_name                = "quicksight-report-email-send-queue-cfa-usa"
#   redshift_cluster_id                        = "primary-dw-dev"
#   database                                   = "dev"
#   db_user                                    = "dw_master"
#   region_name                                = "us-east-1"
#   quicksight_reporting_s3_bucket_name        = "quicksight-pdf-report-exporting-cfa-usa"
#   quicksight_report_pdf_init_lambda_name     = "quicksight-report-generation-initializer-cfa-usa"
#   ec2_server_name                            = "quicksight-report-generator-cfa-usa"
#   ec2_ami_id                                 = "ami-0e1bed4f06a3b463d"
#   ec2_instance_type                          = "t3a.xlarge"
#   ec2_root_block_device_encrypted            = true
#   ec2_root_block_device_volume_size          = 30
#   ec2_ssh_public_key_full_path               = "users/admin_public_keys/kasun.bamunusingha"
#   sns_topic_name_for_errors                  = "quicksight-report-generation-notifications-cfa-usa"
#   aws_account_id                             = data.aws_caller_identity.current.account_id
#   embed_url_generation_lambda_name           = "quicksight-embed-url-for-pdf-generation-cfa-usa"
#   quicksight_dashboard_id                    = "cf5bb8c7-cdde-4ea8-ac48-a2e0ccf7b922"
#   dashboard_sheet_id                         = ""
#   ses_region_name                            = "ap-southeast-2"
#   ses_sender_email                           = "<EMAIL>"
#   bcc_email                                  = "<EMAIL>"
#   report_generation_finalization_lambda_name = "quicksight-report-generation-finalization-cfa-usa"
#   email_sending_lambda_name                  = "quicksight-report-generation-email-sender-cfa-usa"
#   execution_time_zone                        = "America/New_York"
#   execution_time_window_start_time           = "04:50"
#   execution_time_window_end_time             = "05:50"
#   pdf_render_view_width_px                   = "1920"
#   pdf_render_view_height_px                  = "4000"
#   pdf_render_waiting_time_ms                 = 120000
#   pdf_print_page_size                        = "A2"
#   row_level_security_enabled_for_dashboard   = "False"

#   tags = merge(
#     local.common_tags,
#     {
#       "Application"     = "Quicksight-Reporting"
#       "Customer Facing" = "false"
#       "Author"          = "Data-Team"
#       "MultiRegion"     = "false"
#       "Product"         = "Data"
#       "Squad"           = "Data"
#       "Stack"           = "ETL"
#       "Terraform"       = "true"
#   })
# }
