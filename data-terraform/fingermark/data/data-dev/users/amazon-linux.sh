#!/usr/bin/env bash
exec > >(tee /var/log/user-data.log | logger -t user-data -s 2>/dev/console) 2>&1

##
## Setup SSH Config
##
cat <<"__EOF__" > /home/<USER>/.ssh/config
Host *
    StrictHostKeyChecking no
__EOF__

chmod 600 /home/<USER>/.ssh/config
chown ec2-user:ec2-user /home/<USER>/.ssh/config

##
## Enable SSM
##
systemctl enable amazon-ssm-agent
systemctl start amazon-ssm-agent
systemctl status amazon-ssm-agent

##
## Create multiple ec2 users
##
%{ for user, pubkey in ec2_users }
sudo adduser ${user}
sudo mkdir /home/<USER>/.ssh
sudo chown ${user}:${user} /home/<USER>/.ssh/
sudo su - ${user}

cat <<"__EOF__" > /home/<USER>/.ssh/authorized_keys
${pubkey}
__EOF__

chmod 600 /home/<USER>/.ssh/authorized_keys
cat <<"__EOF__" > /home/<USER>/.ssh/config
Host *
    StrictHostKeyChecking no
__EOF__

sudo chmod 600 /home/<USER>/.ssh/config
sudo chown ${user}:${user} /home/<USER>/.ssh/config
sudo chmod 600 /home/<USER>/.ssh/authorized_keys
sudo chown ${user}:${user} /home/<USER>/.ssh/authorized_keys
%{ endfor ~}