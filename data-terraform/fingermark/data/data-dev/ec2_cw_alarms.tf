module "ec2_instance_cw_alarms_ap_southeast_2" {
  source         = "git::*****************:fingermarkltd/fingermark-terraform.git//modules/ec2_instance_cw_alarms?ref=master"
  sns_topic_arns = [var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]]
  cw_alarm_config_ec2_by_tags_cpu_util_high = {
    "Grafana"                         = { instance_tags = { Name = "Grafana" } }
    "app-flow-test"                   = { instance_tags = { Name = "app-flow-test" } }
    "metabase-dev"                    = { instance_tags = { Name = "metabase-dev" } }
    "bastion-v2-dev-ap-southeast-2-1" = { instance_tags = { Name = "bastion-v2-dev-ap-southeast-2" } }
    "bastion-v2-dev-ap-southeast-2-2" = { instance_tags = { Name = "bastion-v2-dev-ap-southeast-2" } }
    "vpn-v2-dev-ap-southeast-2"       = { instance_tags = { Name = "vpn-v2-dev-ap-southeast-2" } }
    "bastion-dev-ap-southeast-2"      = { instance_tags = { Name = "bastion-dev-ap-southeast-2" } }
  }
  cw_alarm_config_ec2_by_tags_cpu_util_low = {}
  tags = {
    Environment = "Development"
    Application = "EC2-Monitoring"
    Squad       = "Platform team"
  }
}

module "ec2_instance_cw_alarms_us_east_1" {
  providers      = { aws = aws.us_e1 }
  source         = "git::*****************:fingermarkltd/fingermark-terraform.git//modules/ec2_instance_cw_alarms?ref=master"
  sns_topic_arns = [var.cw_alarms_sns_topic_arns_region_lookup["us-east-1"]]
  cw_alarm_config_ec2_by_tags_cpu_util_high = {
    "bastion-v2-dev-us-east-1" = { instance_tags = { Name = "bastion-v2-dev-us-east-1" } }
    "vpn-v2-dev-us-east-1"     = { instance_tags = { Name = "vpn-v2-dev-us-east-1" } }
  }
  cw_alarm_config_ec2_by_tags_cpu_util_low = {}
  tags = {
    Environment = "Development"
    Application = "EC2-Monitoring"
    Squad       = "Platform team"
  }
}
