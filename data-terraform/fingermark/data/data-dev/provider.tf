
provider "aws" {
  region = var.AWS_REGION
  assume_role {
    role_arn     = "arn:aws:iam::553643950077:role/AdminAccess"
    session_name = "fingermark-data-dev"
  }
}

provider "aws" {
  alias  = "us_w1"
  region = "us-west-1"
  assume_role {
    role_arn     = "arn:aws:iam::553643950077:role/AdminAccess"
    session_name = "fingermark-data-dev"
  }
}

provider "aws" {
  alias  = "us_w2"
  region = "us-west-2"
  assume_role {
    role_arn     = "arn:aws:iam::553643950077:role/AdminAccess"
    session_name = "fingermark-data-dev"
  }
}

provider "aws" {
  alias  = "us_e1"
  region = "us-east-1"
  assume_role {
    role_arn     = "arn:aws:iam::553643950077:role/AdminAccess"
    session_name = "fingermark-data-dev"
  }
}

provider "aws" {
  alias  = "us_e2"
  region = "us-east-2"
  assume_role {
    role_arn     = "arn:aws:iam::553643950077:role/AdminAccess"
    session_name = "fingermark-data-dev"
  }
}
