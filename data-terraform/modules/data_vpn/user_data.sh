#!/usr/bin/env bash
exec > >(tee /var/log/user-data.log | logger -t user-data -s 2>/dev/console) 2>&1

cat <<"__EOF__" > /home/<USER>/.ssh/config
Host *
    StrictHostKeyChecking no
__EOF__

systemctl enable amazon-ssm-agent
systemctl start amazon-ssm-agent
systemctl status amazon-ssm-agent


yum -y update
mkdir /var/log/bastion


chown ec2-user:ec2-user /var/log/bastion
chmod -R 770 /var/log/bastion
setfacl -Rdm other:0 /var/log/bastion

sed -i "s/#Port 22/Port ${public_ssh_port}/g" /etc/ssh/sshd_config

mkdir /usr/bin/bastion

cat > /usr/bin/bastion/shell << 'EOF'
# Check that the SSH client did not supply a command
if [[ -z $SSH_ORIGINAL_COMMAND ]]; then

  LOG_FILE="`date --date="today" "+%Y-%m-%d_%H-%M-%S"`_`whoami`"
  LOG_DIR="/var/log/bastion/"

  echo ""
  echo "NOTE: This SSH session will be recorded"
  echo "AUDIT KEY: $LOG_FILE"
  echo ""

  SUFFIX=`mktemp -u _XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX`

  script -qf --timing=$LOG_DIR$LOG_FILE$SUFFIX.time $LOG_DIR$LOG_FILE$SUFFIX.data --command=/bin/bash

else

  if [ "${allow_ssh_commands}" == "true" ]; then
    exec /bin/bash -c "$SSH_ORIGINAL_COMMAND"
  else
    echo "This VPN server support bastion interactive sessions only. Do not supply a command"
    exit 1
  fi
fi
EOF

chmod a+x /usr/bin/bastion/shell

chown root:ec2-user /usr/bin/script
chmod g+s /usr/bin/script

echo "/usr/bin/bastion/shell  >> /etc/profile"

mount -o remount,rw,hidepid=2 /proc
awk '!/proc/' /etc/fstab > temp && mv temp /etc/fstab
echo "proc /proc proc defaults,hidepid=2 0 0" >> /etc/fstab

service sshd restart

cat > /usr/bin/bastion/sync_s3 << 'EOF'
#!/usr/bin/env bash
LOG_DIR="/var/log/bastion/"
aws s3 cp $LOG_DIR s3://${bucket_name}/logs/ --sse --region ${aws_region} --recursive && find $LOG_DIR* -mtime +1 -exec rm {} \;
EOF

chmod 700 /usr/bin/bastion/sync_s3

cat > /usr/bin/bastion/sync_users << 'EOF'
#!/usr/bin/env bash
LOG_FILE="/var/log/bastion/users_changelog.txt"
get_user_name () {
  echo "$1" | sed -e "s/.*\///g" | sed -e "s/\.pub//g"
}
aws s3api list-objects --bucket ${bucket_name} --prefix user_public_keys/ --region ${aws_region} --output text --query 'Contents[?Size>`0`].Key' | tr '\t' '\n' > ~/keys_retrieved_from_s3
while read line; do
  USER_NAME="`get_user_name "$line"`"
 if [[ "$USER_NAME" =~ ^[a-z][.a-z0-9]*$ ]]; then
    # Create a user account if it does not already exist
    cut -d: -f1 /etc/passwd | grep -qx $USER_NAME
    if [ $? -eq 1 ]; then
      /usr/sbin/adduser $USER_NAME && \
      mkdir -m 700 /home/<USER>/.ssh && \
      chown $USER_NAME:$USER_NAME /home/<USER>/.ssh && \
      echo "$line" >> ~/keys_installed && \
      echo "`date --date="today" "+%Y-%m-%d %H-%M-%S"`: Creating user account for $USER_NAME ($line)" >> $LOG_FILE
    fi
    if [ -f ~/keys_installed ]; then
      grep -qx "$line" ~/keys_installed
      if [ $? -eq 0 ]; then
        aws s3 cp s3://${bucket_name}/$line /home/<USER>/.ssh/authorized_keys --region ${aws_region}
        chmod 600 /home/<USER>/.ssh/authorized_keys
        chown $USER_NAME:$USER_NAME /home/<USER>/.ssh/authorized_keys
      fi
    fi
  fi
done < ~/keys_retrieved_from_s3
if [ -f ~/keys_installed ]; then
  sort -uo ~/keys_installed ~/keys_installed
  sort -uo ~/keys_retrieved_from_s3 ~/keys_retrieved_from_s3
  comm -13 ~/keys_retrieved_from_s3 ~/keys_installed | sed "s/\t//g" > ~/keys_to_remove
  while read line; do
    USER_NAME="`get_user_name "$line"`"
    echo "`date --date="today" "+%Y-%m-%d %H-%M-%S"`: Removing user account for $USER_NAME ($line)" >> $LOG_FILE
    /usr/sbin/userdel -r -f $USER_NAME
  done < ~/keys_to_remove
  comm -3 ~/keys_installed ~/keys_to_remove | sed "s/\t//g" > ~/tmp && mv ~/tmp ~/keys_installed
fi
EOF

cat > /usr/bin/bastion/sync_admin_users << 'EOF'
#!/usr/bin/env bash
LOG_FILE="/var/log/bastion/users_changelog.txt"
get_user_name () {
  echo "$1" | sed -e "s/.*\///g" | sed -e "s/\.pub//g"
}
aws s3api list-objects --bucket ${bucket_name} --prefix admin_public_keys/ --region ${aws_region} --output text --query 'Contents[?Size>`0`].Key' | tr '\t' '\n' > ~/admin_keys_retrieved_from_s3
while read line; do
  USER_NAME="`get_user_name "$line"`"
  if [[ "$USER_NAME" =~ ^[a-z][.a-z0-9]*$ ]]; then
    # Create a user account if it does not already exist
    cut -d: -f1 /etc/passwd | grep -qx $USER_NAME
    if [ $? -eq 1 ]; then
      /usr/sbin/adduser $USER_NAME && \
      mkdir -m 700 /home/<USER>/.ssh && \
      chown $USER_NAME:$USER_NAME /home/<USER>/.ssh && \
      echo $USER_NAME " ALL=(ALL) NOPASSWD:ALL" >> /etc/sudoers && \
      /usr/sbin/usermod -G wheel $USER_NAME && \
      echo "$line" >> ~/admin_keys_installed && \
      echo "`date --date="today" "+%Y-%m-%d %H-%M-%S"`: Creating admin user account for $USER_NAME ($line)" >> $LOG_FILE
    fi
    if [ -f ~/admin_keys_installed ]; then
      grep -qx "$line" ~/admin_keys_installed
      if [ $? -eq 0 ]; then
        aws s3 cp s3://${bucket_name}/$line /home/<USER>/.ssh/authorized_keys --region ${aws_region}
        chmod 600 /home/<USER>/.ssh/authorized_keys
        chown $USER_NAME:$USER_NAME /home/<USER>/.ssh/authorized_keys
      fi
    fi
  fi
done < ~/admin_keys_retrieved_from_s3
if [ -f ~/admin_keys_installed ]; then
  sort -uo ~/admin_keys_installed ~/admin_keys_installed
  sort -uo ~/admin_keys_retrieved_from_s3 ~/admin_keys_retrieved_from_s3
  comm -13 ~/admin_keys_retrieved_from_s3 ~/admin_keys_installed | sed "s/\t//g" > ~/admin_keys_to_remove
  while read line; do
    USER_NAME="`get_user_name "$line"`"
    echo "`date --date="today" "+%Y-%m-%d %H-%M-%S"`: Removing user account for $USER_NAME ($line)" >> $LOG_FILE
    /usr/sbin/userdel -r -f $USER_NAME
  done < ~/admin_keys_to_remove
  comm -3 ~/admin_keys_installed ~/admin_keys_to_remove | sed "s/\t//g" > ~/tmp && mv ~/tmp ~/admin_keys_installed
fi
EOF

chmod 700 /usr/bin/bastion/sync_admin_users
chmod 700 /usr/bin/bastion/sync_users


cat > ~/mycron << EOF
*/5 * * * * /usr/bin/bastion/sync_users
*/5 * * * * /usr/bin/bastion/sync_admin_users
0 0 * * * yum -y update --security
${sync_logs_cron_job}
EOF
crontab ~/mycron
rm ~/mycron

echo "/usr/bin/bastion/shell  >> /etc/profile"
service sshd restart

# VPN Setup

yum -y install openvpn easy-rsa

cat > /root/vpn_init.sh<< 'EOF'
#!/usr/bin/env bash
aws s3 cp s3://${bucket_name}/vpn/openvpn-install.sh /root/openvpn-install.sh  --region ${aws_region}
chmod +x /root/openvpn-install.sh
export AUTO_INSTALL=y
./root/openvpn-install.sh
EOF



export AUTO_INSTALL=y
chmod +x /root/openvpn-install.sh
./root/openvpn-install.sh