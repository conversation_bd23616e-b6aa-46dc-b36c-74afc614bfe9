data "aws_region" "current" {}

data "aws_iam_role" "batch_role" {
  name = "AWSServiceRoleForBatch"
}

data "aws_iam_policy_document" "assume_role_policy" {
  statement {
    actions = ["sts:AssumeRole"]

    principals {
      type        = "Service"
      identifiers = ["ecs-tasks.amazonaws.com"]
    }
  }
}

data "aws_iam_policy_document" "website_bucket_policy" {
  count = var.create_static_website ? 1 : 0
  statement {
    sid    = "PublicAccess"
    effect = "Allow"
    principals {
      type        = "*"
      identifiers = ["*"]
    }
    actions = [
      "s3:GetObject"
    ]
    resources = [
      "${module.s3_bucket[0].s3_bucket_arn}/*"
    ]
  }
}