variable "batch_id" {
  type        = string
  description = "Batch ID, define what batch compute environment and other resources for"
}

variable "environment_code" {
  type        = string
  description = "The environment indicator. Options [dev, prod]"
  default     = "dev"
}

variable "vpc_id" {
  type        = string
  description = "VPC ID"
}

variable "security_group_ids" {
  description = "Extra security group ids"
  type        = list(any)
  default     = []
}

variable "compute_subnet_ids" {
  description = "List of subnets ids"
  type        = list(any)
}

variable "create_static_website" {
  description = "Create S3 bucket with static website hosting enabled"
  type        = bool
  default     = false
}

variable "tags" {
  type        = any
  description = "a group of tags to tag resources"
  default = {
    Terraform = "true"
    Stack     = "Data"
    Author    = "Data Team"
    Backup    = "false"
  }
}