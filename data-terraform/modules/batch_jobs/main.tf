locals {
  id = "${var.batch_id}-${var.environment_code}-${data.aws_region.current.name}"
}

resource "aws_ecr_repository" "data_images" {
  name                 = local.id
  image_tag_mutability = "MUTABLE"

  image_scanning_configuration {
    scan_on_push = true
  }

  tags = var.tags
}

resource "aws_ecr_lifecycle_policy" "remove_untagged_images" {
  repository = aws_ecr_repository.data_images.name

  policy = <<EOF
{
    "rules": [
        {
            "rulePriority": 1,
            "description": "Expire images older than 14 days",
            "selection": {
                "tagStatus": "untagged",
                "countType": "sinceImagePushed",
                "countUnit": "days",
                "countNumber": 14
            },
            "action": {
                "type": "expire"
            }
        }
    ]
}
EOF
}

resource "aws_ssm_parameter" "ecr_uri" {
  name  = "/fingermark/data/batch/${local.id}/ecr/url"
  type  = "String"
  value = aws_ecr_repository.data_images.repository_url
  tags  = var.tags
}

resource "aws_security_group" "batch_sg" {
  name        = "${local.id}-securityGroup"
  description = "Bastion security group for ssh tunnels to private resources"
  vpc_id      = var.vpc_id

  ingress = [
    {
      description      = "Allow incoming connections from trusted locations"
      from_port        = 22
      to_port          = 22
      protocol         = "TCP"
      self             = true
      cidr_blocks      = null
      prefix_list_ids  = null
      ipv6_cidr_blocks = null
      security_groups  = null
    }
  ]

  egress = [
    {
      description      = "Allow all outgoing connections "
      self             = null
      from_port        = 0
      to_port          = 0
      protocol         = "-1"
      prefix_list_ids  = null
      ipv6_cidr_blocks = null
      security_groups  = null
      cidr_blocks      = ["0.0.0.0/0"]
    }
  ]

  tags = merge(var.tags, { Name = "${local.id}-securityGroup" })

}

resource "aws_batch_compute_environment" "compute_environment" {
  compute_environment_name = local.id

  compute_resources {
    max_vcpus = 16

    security_group_ids = concat(var.security_group_ids, [aws_security_group.batch_sg.id])

    subnets = var.compute_subnet_ids

    type = "FARGATE"
  }

  service_role = data.aws_iam_role.batch_role.arn
  type         = "MANAGED"
  depends_on   = [aws_security_group.batch_sg]
}

resource "aws_batch_compute_environment" "spot_compute_environment" {
  compute_environment_name = "spot-${local.id}"

  compute_resources {
    max_vcpus = 16

    security_group_ids = [
      aws_security_group.batch_sg.id
    ]

    subnets = var.compute_subnet_ids

    type = "FARGATE_SPOT"
  }

  service_role = data.aws_iam_role.batch_role.arn
  type         = "MANAGED"
  depends_on   = [aws_security_group.batch_sg]
}

resource "aws_batch_job_queue" "high_priority_queue" {
  name     = "${local.id}-high-priority-queue"
  state    = "ENABLED"
  priority = 10
  compute_environment_order {
    order               = 1
    compute_environment = aws_batch_compute_environment.compute_environment.arn
  }
}


resource "aws_batch_job_queue" "low_priority_queue" {
  name     = "${local.id}-low-priority-queue"
  state    = "ENABLED"
  priority = 1
  compute_environment_order {
    order               = 1
    compute_environment = aws_batch_compute_environment.spot_compute_environment.arn
  }
}

resource "aws_iam_role" "ecs_task_execution_role" {
  name               = "${local.id}-batch-exec-role"
  assume_role_policy = data.aws_iam_policy_document.assume_role_policy.json
}

resource "aws_iam_role_policy_attachment" "ecs_task_execution_role_policy" {
  role       = aws_iam_role.ecs_task_execution_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
}

resource "aws_batch_job_definition" "batch_default_job_definition" {
  name = "${local.id}-batch-job-default"
  type = "container"
  platform_capabilities = [
    "FARGATE"
  ]

  container_properties = <<CONTAINER_PROPERTIES
{
  "command": ["echo", "test"],
  "image": "${aws_ecr_repository.data_images.repository_url}:latest",
  "fargatePlatformConfiguration": {
    "platformVersion": "LATEST"
  },
  "resourceRequirements": [
    {"type": "VCPU", "value": "0.25"},
    {"type": "MEMORY", "value": "512"}
  ],
  "executionRoleArn": "${aws_iam_role.ecs_task_execution_role.arn}"
}
CONTAINER_PROPERTIES
}

module "s3_bucket" {
  count = var.create_static_website ? 1 : 0
  # https://github.com/terraform-aws-modules/terraform-aws-s3-bucket
  source = "terraform-aws-modules/s3-bucket/aws"

  bucket = local.id
  acl    = "private"

  server_side_encryption_configuration = {
    rule = {
      apply_server_side_encryption_by_default = {
        sse_algorithm = "AES256"
      }
    }
  }

  # S3 bucket-level Public Access Block configuration
  block_public_acls       = false
  block_public_policy     = false
  ignore_public_acls      = false
  restrict_public_buckets = false

  # S3 Bucket Ownership Controls
  control_object_ownership = true
  object_ownership         = "BucketOwnerPreferred"

  website = {
    index_document = "index.html"
    error_document = "error.html"
  }

  tags = var.tags
}

resource "aws_s3_bucket_policy" "website_bucket_policy" {
  count      = var.create_static_website ? 1 : 0
  depends_on = [module.s3_bucket]
  bucket     = module.s3_bucket[0].s3_bucket_id
  policy     = data.aws_iam_policy_document.website_bucket_policy[0].json
}

resource "aws_ssm_parameter" "batch_job_definition" {
  name  = "/fingermark/data/batch/${local.id}/job/default_def/name"
  type  = "String"
  value = aws_batch_job_definition.batch_default_job_definition.name
  tags  = var.tags
}

resource "aws_ssm_parameter" "batch_job_low_priority_queue" {
  name  = "/fingermark/data/batch/${local.id}/job/default_def/low_priority_queue/name"
  type  = "String"
  value = aws_batch_job_queue.low_priority_queue.name
  tags  = var.tags
}

resource "aws_ssm_parameter" "batch_job_high_priority_queue" {
  name  = "/fingermark/data/batch/${local.id}/job/default_def/high_priority_queue/name"
  type  = "String"
  value = aws_batch_job_queue.high_priority_queue.name
  tags  = var.tags
}
