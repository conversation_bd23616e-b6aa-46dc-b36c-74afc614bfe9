variable "vpc_name" {
  type        = string
  description = "VPC name"
}

variable "aws_availability_zones" {
  type        = list(any)
  description = "VPC Availability Zone list"
  default     = ["ap-southeast-2a", "ap-southeast-2b", "ap-southeast-2c"]
}

variable "vpc_cidr_block" {
  type        = string
  description = "CIDR block for the VPC."
  default     = "10.0.0.0/16"

  validation {
    condition     = can(regex("^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])(\\/(1[6-9]|2[0-8]))$", var.vpc_cidr_block))
    error_message = "CIDR block parameter must be in the form x.x.x.x/16-28."
  }
}

variable "environment_code" {
  type        = string
  description = "The environment indicator. Options [dev, prod]"
  default     = "dev"
}

variable "tags" {
  type        = any
  description = "a group of tags to tag resources"
  default = {
    Terraform = "true"
    Stack     = "Data"
    Author    = "Data Team"
    Backup    = "false"
  }
}

variable "limit_subnet" {
  type        = bool
  description = "Limit the number of subnets to 2 per AZ"
  default     = false

}

variable "extra_ingress_rules" {
  description = "Additional ingress rules for the security group"
  type = list(object({
    description      = string
    from_port        = number
    to_port          = number
    protocol         = string
    cidr_blocks      = list(string)
    ipv6_cidr_blocks = list(string)
    prefix_list_ids  = list(string)
    security_groups  = list(string)
    self             = bool
  }))
  default = []
}

variable "extra_egress_rules" {
  description = "Additional egress rules for the security group"
  type = list(object({
    description      = string
    from_port        = number
    to_port          = number
    protocol         = string
    cidr_blocks      = list(string)
    ipv6_cidr_blocks = list(string)
    prefix_list_ids  = list(string)
    security_groups  = list(string)
    self             = bool
  }))
  default = []
}
