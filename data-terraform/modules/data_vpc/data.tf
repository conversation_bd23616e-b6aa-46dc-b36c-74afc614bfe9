data "aws_region" "current" {}

data "aws_vpc" "default" {
  default = true
}

data "aws_availability_zones" "this" {
  state = "available"
}

data "aws_ec2_managed_prefix_list" "s3_prefixed_list" {
  name = "com.amazonaws.${data.aws_region.current.name}.s3"
}

data "aws_ec2_managed_prefix_list" "dynamodb_prefixed_list" {
  name = "com.amazonaws.${data.aws_region.current.name}.dynamodb"
}

locals {
  aws_availability_zones = var.aws_availability_zones != [] ? var.aws_availability_zones : data.aws_availability_zones.this.names
}
