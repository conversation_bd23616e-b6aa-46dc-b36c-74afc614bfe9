output "vpc_cidr" {
  description = "VPC CIDR Block"
  value       = aws_vpc.data_vpc.cidr_block
}

output "vpc_id" {
  description = "VPC id"
  value       = aws_vpc.data_vpc.id
}

output "public_subnets_ids" {
  description = "Public Subnets Availability Zones"
  value       = tolist(aws_subnet.public[*].id)
}

output "private_subnets_ids" {
  description = "Private Subnets Availability Zones"
  value       = tolist(aws_subnet.private[*].id)
}

output "private_resources_sg" {
  description = "Private resources Security Group"
  value       = aws_security_group.private_resources_sg.id
}
