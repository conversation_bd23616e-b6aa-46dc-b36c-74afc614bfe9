
resource "aws_vpc" "data_vpc" {
  cidr_block           = var.vpc_cidr_block
  enable_dns_support   = "true"
  enable_dns_hostnames = "true"

  tags = merge(var.tags, { Name = var.vpc_name })
}

resource "aws_subnet" "public" {
  count = length(local.aws_availability_zones)

  availability_zone       = local.aws_availability_zones[count.index]
  cidr_block              = cidrsubnet(var.vpc_cidr_block, 4, count.index)
  map_public_ip_on_launch = true
  vpc_id                  = aws_vpc.data_vpc.id
  tags = merge(
    var.tags,
    { SubnetType = "public", Name = "${var.vpc_name} - public - ${local.aws_availability_zones[count.index]}" }
  )
}

resource "aws_subnet" "private" {
  count = length(local.aws_availability_zones)

  availability_zone       = local.aws_availability_zones[count.index]
  cidr_block              = cidrsubnet(var.vpc_cidr_block, 4, count.index + 3)
  map_public_ip_on_launch = false
  vpc_id                  = aws_vpc.data_vpc.id
  tags = merge(
    var.tags,
    { SubnetType = "private", Name = "${var.vpc_name} - private - ${local.aws_availability_zones[count.index]}" }
  )
}

resource "aws_internet_gateway" "gateway" {
  vpc_id = aws_vpc.data_vpc.id
  tags   = merge(var.tags, { Name = var.vpc_name })
}

resource "aws_eip" "nat_eip" {
  domain = "vpc"
  tags   = merge(var.tags, { Name = "${var.vpc_name} - NAT EIP" })
}

resource "aws_nat_gateway" "nat" {
  allocation_id = aws_eip.nat_eip.id
  subnet_id     = aws_subnet.public[0].id
  tags          = merge(var.tags, { Name = var.vpc_name })
}

resource "aws_route_table" "public" {
  vpc_id = aws_vpc.data_vpc.id
  tags   = merge(var.tags, { Name = "${var.vpc_name} - public route" })
}

resource "aws_route" "public" {
  route_table_id         = aws_route_table.public.id
  destination_cidr_block = "0.0.0.0/0"
  gateway_id             = aws_internet_gateway.gateway.id
}

resource "aws_route_table_association" "public" {
  count          = length(local.aws_availability_zones)
  route_table_id = aws_route_table.public.id
  subnet_id      = aws_subnet.public[count.index].id
}

resource "aws_route_table" "private" {
  vpc_id = aws_vpc.data_vpc.id
  tags   = merge(var.tags, { Name = "${var.vpc_name} - private route" })
}

resource "aws_route" "private" {
  route_table_id         = aws_route_table.private.id
  destination_cidr_block = "0.0.0.0/0"
  nat_gateway_id         = aws_nat_gateway.nat.id
}

resource "aws_route_table_association" "private" {
  count          = length(local.aws_availability_zones)
  route_table_id = aws_route_table.private.id
  subnet_id      = aws_subnet.private[count.index].id
}

resource "aws_ssm_parameter" "ssm_vpc_id" {
  name  = "/fingermark/data/vpc/vpc_id"
  type  = "String"
  value = aws_vpc.data_vpc.id
  tags  = var.tags
}

resource "aws_ssm_parameter" "ssm_vpc_cidr" {
  name  = "/fingermark/data/vpc/vpc_cidr"
  type  = "String"
  value = aws_vpc.data_vpc.cidr_block
  tags  = var.tags
}

resource "aws_ssm_parameter" "subnet_ids_public" {
  count = length(local.aws_availability_zones)
  name  = "/fingermark/data/vpc/subnet/public/${local.aws_availability_zones[count.index]}/id"
  type  = "String"
  value = aws_subnet.public[count.index].id
  tags  = var.tags
}

resource "aws_ssm_parameter" "subnet_ids_private" {
  count = length(local.aws_availability_zones)
  name  = "/fingermark/data/vpc/subnet/private/${local.aws_availability_zones[count.index]}/id"
  type  = "String"
  value = aws_subnet.private[count.index].id
  tags  = var.tags
}

# default security group for private subnets
resource "aws_security_group" "private_resources_sg" {
  name        = "default-private-resources-securityGroup"
  description = "Default security group for private resources within Data VPC"
  vpc_id      = aws_vpc.data_vpc.id
  tags        = merge(var.tags, { Name = "${var.vpc_name} - default-private-resources" })
  ingress = flatten([[
    {
      description      = "Allow incoming connections from Data VPC"
      from_port        = 443
      to_port          = 443
      protocol         = "TCP"
      self             = true
      cidr_blocks      = null
      prefix_list_ids  = null
      ipv6_cidr_blocks = null
      security_groups  = null
    },
    {
      description      = "Allow incoming connections from same group"
      from_port        = 0
      to_port          = 0
      protocol         = "-1"
      self             = true
      cidr_blocks      = null
      prefix_list_ids  = null
      ipv6_cidr_blocks = null
      security_groups  = null
    }
    ], var.extra_ingress_rules]
  )

  egress = flatten([[
    {
      description      = "Allow out going connections within Data VPC"
      self             = null
      from_port        = 0
      to_port          = 0
      protocol         = "-1"
      prefix_list_ids  = null
      ipv6_cidr_blocks = null
      security_groups  = null
      cidr_blocks      = [aws_vpc.data_vpc.cidr_block]
    },
    {
      description      = "Allow out going connections within same sg"
      self             = true
      from_port        = 0
      to_port          = 0
      protocol         = "-1"
      prefix_list_ids  = null
      ipv6_cidr_blocks = null
      security_groups  = null
      cidr_blocks      = null
    },
    {
      description      = "Allow outgoing connection to S3 and dynamodb"
      self             = null
      from_port        = 0
      to_port          = 0
      protocol         = "-1"
      cidr_blocks      = null
      ipv6_cidr_blocks = null
      security_groups  = null
      prefix_list_ids = [
        data.aws_ec2_managed_prefix_list.s3_prefixed_list.id,
        data.aws_ec2_managed_prefix_list.dynamodb_prefixed_list.id
      ]
    }
    ], var.extra_egress_rules
  ])
}

resource "aws_vpc_endpoint" "glue" {
  vpc_id              = aws_vpc.data_vpc.id
  service_name        = "com.amazonaws.${data.aws_region.current.name}.glue"
  vpc_endpoint_type   = "Interface"
  security_group_ids  = [aws_security_group.private_resources_sg.id]
  subnet_ids          = tolist(aws_subnet.private[*].id)
  private_dns_enabled = true
  tags                = var.tags
}

resource "aws_vpc_endpoint" "lambda" {
  vpc_id              = aws_vpc.data_vpc.id
  service_name        = "com.amazonaws.${data.aws_region.current.name}.lambda"
  vpc_endpoint_type   = "Interface"
  security_group_ids  = [aws_security_group.private_resources_sg.id]
  subnet_ids          = tolist(aws_subnet.private[*].id)
  private_dns_enabled = true
  tags                = var.tags
}

resource "aws_vpc_endpoint" "secrets_manager" {
  vpc_id              = aws_vpc.data_vpc.id
  service_name        = "com.amazonaws.${data.aws_region.current.name}.secretsmanager"
  vpc_endpoint_type   = "Interface"
  security_group_ids  = [aws_security_group.private_resources_sg.id]
  subnet_ids          = tolist(aws_subnet.private[*].id)
  private_dns_enabled = true
  tags                = var.tags
}

resource "aws_vpc_endpoint" "redshift" {
  vpc_id              = aws_vpc.data_vpc.id
  service_name        = "com.amazonaws.${data.aws_region.current.name}.redshift"
  vpc_endpoint_type   = "Interface"
  security_group_ids  = [aws_security_group.private_resources_sg.id]
  subnet_ids          = var.limit_subnet == true ? slice(tolist(aws_subnet.private[*].id), 0, 2) : tolist(aws_subnet.private[*].id)
  private_dns_enabled = true
  tags                = var.tags
}

resource "aws_vpc_endpoint" "sqs" {
  vpc_id              = aws_vpc.data_vpc.id
  service_name        = "com.amazonaws.${data.aws_region.current.name}.sqs"
  vpc_endpoint_type   = "Interface"
  security_group_ids  = [aws_security_group.private_resources_sg.id]
  subnet_ids          = tolist(aws_subnet.private[*].id)
  private_dns_enabled = true
  tags                = var.tags
}

resource "aws_vpc_endpoint" "kinesis" {
  vpc_id              = aws_vpc.data_vpc.id
  service_name        = "com.amazonaws.${data.aws_region.current.name}.kinesis-streams"
  vpc_endpoint_type   = "Interface"
  security_group_ids  = [aws_security_group.private_resources_sg.id]
  subnet_ids          = tolist(aws_subnet.private[*].id)
  private_dns_enabled = true
  tags                = var.tags
}
resource "aws_ssm_parameter" "private_resources_sg" {
  name  = "/fingermark/data/vpc/securitygroups/private_resources/id"
  type  = "String"
  value = aws_security_group.private_resources_sg.id
  tags  = var.tags
}
