variable "workgroup_name" {
  type        = string
  description = "Athena Workgroup name"
}

variable "environment_code" {
  type        = string
  description = "The environment indicator. Options [dev, prod]"
  default     = "dev"
}

variable "tags" {
  type        = any
  description = "a group of tags to tag resources"
  default = {
    Terraform = "true"
    Stack     = "Data"
    Author    = "Data Team"
    Backup    = "false"
  }
}