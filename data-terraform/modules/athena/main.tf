# Setup <PERSON>'s S3 bucket to store result
module "s3_bucket" {
  # https://github.com/terraform-aws-modules/terraform-aws-s3-bucket
  source = "terraform-aws-modules/s3-bucket/aws"

  bucket = "athena-results-${var.workgroup_name}-${var.environment_code}-${data.aws_region.current.name}"
  acl    = "private"

  server_side_encryption_configuration = {
    rule = {
      apply_server_side_encryption_by_default = {
        sse_algorithm = "AES256"
      }
    }
  }
  # S3 bucket-level Public Access Block configuration
  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true

  tags = var.tags
}

resource "aws_athena_workgroup" "primary" {
  name       = var.workgroup_name
  depends_on = [module.s3_bucket]
  configuration {
    enforce_workgroup_configuration    = true
    publish_cloudwatch_metrics_enabled = true
    bytes_scanned_cutoff_per_query     = 10485760

    result_configuration {
      output_location = "s3://${module.s3_bucket.s3_bucket_id}/"
      encryption_configuration {
        encryption_option = "SSE_S3"
      }
    }
  }
  tags = var.tags
}