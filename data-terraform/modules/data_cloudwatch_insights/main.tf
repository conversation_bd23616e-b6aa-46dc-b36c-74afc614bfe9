resource "aws_cloudwatch_query_definition" "gateway_1" {
  name = "Gateway/Customers Records"

  log_group_names = [
    "/aws/lambda/datalake-gateway-prod-main"
  ]

  query_string = <<EOF
fields @timestamp, @message
| filter ispresent(customer) and ispresent(customer_records)
| stats sum(customer_records) as Records by customer as Customer
EOF
}

resource "aws_cloudwatch_query_definition" "gateway_2" {
  name = "Gateway/Daily Errors"

  log_group_names = [
    "/aws/lambda/datalake-gateway-prod-main"
  ]

  query_string = <<EOF
fields @message
| filter @message like /Error/
| stats count(*) as ErrorCount by bin(1d)
| sort exceptionCount desc
EOF
}

resource "aws_cloudwatch_query_definition" "gateway_3" {
  name = "Gateway/Data Stream Records"

  log_group_names = [
    "/aws/lambda/datalake-gateway-prod-main"
  ]

  query_string = <<EOF
filter ispresent(eyecue_data_stream) and ispresent(customer_records)
| fields replace(eyecue_data_stream, 'eyecue_', '') as data_stream
| stats sum(customer_records) as Events by data_stream as DataStream
EOF
}

resource "aws_cloudwatch_query_definition" "gateway_4" {
  name = "Gateway/Recent Errors"

  log_group_names = [
    "/aws/lambda/datalake-gateway-prod-main"
  ]

  query_string = <<EOF
fields @timestamp, @message
| filter @message like /ERROR/
| sort @timestamp desc
| limit 20
EOF
}

resource "aws_cloudwatch_query_definition" "gateway_5" {
  name = "Gateway/Router Executions"

  log_group_names = [
    "/aws/lambda/datalake-gateway-prod-main"
  ]

  query_string = <<EOF
filter ispresent(event_type) and event_type == 'valid_router'
| stats count(*) as ProcessedFiles by router
EOF
}

resource "aws_cloudwatch_query_definition" "gateway_6" {
  name = "Gateway/Written Files By Customer"

  log_group_names = [
    "/aws/lambda/datalake-gateway-prod-main"
  ]

  query_string = <<EOF
filter ispresent(customer)
| stats count(*) as customerFiles by customer
EOF
}

resource "aws_cloudwatch_query_definition" "gateway_7" {
  name = "Gateway/Written Files Hourly"

  log_group_names = [
    "/aws/lambda/datalake-gateway-prod-main"
  ]

  query_string = <<EOF
filter ispresent(processed_file) and processed_file == 'true'
| stats count(*) as writtenFiles by bin(1h) as hour
EOF
}

resource "aws_cloudwatch_query_definition" "general_1" {
  name = "General/Recent Lambda Errors"

  log_group_names = [
    "/aws/lambda/datalake-gateway-prod-main",
    "/aws/lambda/data-pipeline-sqs-to-s3-prod-main",
    "/aws/lambda/data-pipeline-scheduler-prod-main",
    "/aws/lambda/data-pipeline-s3-replicator-prod-main",
    "/aws/lambda/data-pipeline-files-merger-prod-main"
  ]

  query_string = <<EOF
filter @message like /Error/
| fields replace(@log, '047783385012:/aws/lambda/', '') as LogGroup
| stats count(*) as ErrorCount by LogGroup
EOF
}

resource "aws_cloudwatch_query_definition" "s3replicator_1" {
  name = "S3Replicator/Copied Files"

  log_group_names = [
    "/aws/lambda/data-pipeline-s3-replicator-prod-main"
  ]

  query_string = <<EOF
filter ispresent(task) and ispresent(copied_files)
| fields replace(task, 'eyecue-', '') as Customer
| stats sum(copied_files) as CopiedFiles by Customer
EOF
}

resource "aws_cloudwatch_query_definition" "sqstos3_1" {
  name = "SqsToS3/SQS Stats"

  log_group_names = [
    "/aws/lambda/data-pipeline-sqs-to-s3-prod-main"
  ]

  query_string = <<EOF
filter ispresent(successfully_written)
| stats sum(records) as Events, count(*) as Files by queue_name as Queue
EOF
}

resource "aws_cloudwatch_query_definition" "files_merger_1" {
  name = "FilesMerger/Merged Files"

  log_group_names = [
    "/aws/lambda/data-pipeline-files-merger-prod-main"
  ]

  query_string = <<EOF
filter ispresent(successful_merge)
| stats sum(deleted) as MergedFiles by bin(1h) as Hourly
EOF
}