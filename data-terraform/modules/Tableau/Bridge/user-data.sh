#!/bin/bash
# Install prerequisites
yum update -y
yum install docker -y
service docker start
usermod -a -G docker ec2-user

# Login to ECR
aws ecr get-login-password --region ap-southeast-2 | docker login --username AWS --password-stdin 047783385012.dkr.ecr.ap-southeast-2.amazonaws.com

# Retrieve PAT token from Secrets Manager
TOKEN=$(aws secretsmanager get-secret-value --secret-id ${secret_id} --query SecretString --output text)

# Run container
docker run -d \
  --name tableau_bridge \
  --restart unless-stopped \
  -e PAT_TOKEN="$TOKEN" \
  -e BRIDGE_CLIENT="prod_bridge" \
  ${ecr_image_uri}