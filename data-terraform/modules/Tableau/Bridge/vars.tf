# variable "ecr_image_uri" {
#   description = "ECR image URI for Tableau Bridge"
#   type        = string
# }

# variable "secret_manager_arn" {
#   description = "ARN of Secrets Manager entry containing PAT token"
#   type        = string
# }

# variable "bridge_client" {
#   description = "Tableau Bridge client identifier"
#   type        = string
# }

# variable "bridge_site" {
#   description = "Tableau site name"
#   type        = string
# }

# variable "bridge_user_email" {
#   description = "Tableau user email for Bridge"
#   type        = string
# }

variable "server_name" {
  description = "ECS Server Name"
  type        = string
  default     = "tableau_bridge"
}

variable "environment_code" {
  type        = string
  description = "The environment indicator. Options [dev, prod]"
  default     = "dev"
}

variable "tags" {
  type        = any
  description = "a group of tags to tag resources"
  default     = {}
}

variable "ssh_public_key_full_path" {
  type        = string
  description = "Name of existing SSH public key file (e.g. `id_rsa.pub`)"
  default     = null
}

variable "vpc_id" {
  type        = string
  description = "VPC ID"
}

variable "subnet_id" {
  type        = string
  default     = null
  description = "AWS subnet IDs"
}

# variable "allowed_cidr_blocks" {
#   type        = list(string)
#   description = "A list of trusted cidr blocks as a source to connect to vpn."
# }

variable "instance_type" {
  description = "EC2 instance type"
  type        = string
  default     = "t3.large"
}

variable "AWS_REGION" {
  type    = string
  default = ""
}


variable "SECRET_ID" {
  type        = string
  description = "The ID of the secret in AWS Secrets Manager that contains the Tableau Bridge PAT token"
  default     = ""

}