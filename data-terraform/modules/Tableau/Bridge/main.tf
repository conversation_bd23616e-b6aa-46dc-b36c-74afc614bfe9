locals {
  bridge_name = "${var.server_name}-${var.environment_code}-${data.aws_region.current.name}"
}

resource "aws_iam_instance_profile" "bridge_profile" {
  name = local.bridge_name
  role = aws_iam_role.vpn_role.name
  tags = var.tags
}

resource "aws_iam_role" "vpn_role" {
  name               = local.bridge_name
  path               = "/"
  tags               = var.tags
  assume_role_policy = data.aws_iam_policy_document.default.json
}

resource "aws_iam_role_policy" "main" {
  name   = local.bridge_name
  role   = aws_iam_role.vpn_role.id
  policy = data.aws_iam_policy_document.main.json
}

resource "aws_key_pair" "imported_key" {
  key_name   = local.bridge_name
  public_key = file(var.ssh_public_key_full_path)
  tags       = var.tags
  lifecycle {
    ignore_changes = [public_key]
  }
}



resource "aws_security_group" "bridge_sg" {
  name        = "${local.bridge_name}-securityGroup"
  description = "vpn security group for ssh tunnels to private resources"
  vpc_id      = var.vpc_id

  ingress = [
    {
      description      = "Allow incoming connections from trusted locations"
      from_port        = 22
      to_port          = 22
      protocol         = "TCP"
      self             = true
      cidr_blocks      = ["0.0.0.0/0"]
      prefix_list_ids  = null
      ipv6_cidr_blocks = null
      security_groups  = null
    },
    {
      description      = "Allow incoming connections from trusted locations"
      from_port        = 443
      to_port          = 443
      protocol         = "TCP"
      self             = true
      cidr_blocks      = ["0.0.0.0/0"]
      prefix_list_ids  = null
      ipv6_cidr_blocks = null
      security_groups  = null
    }
  ]

  egress = [
    {
      description      = "Allow all outgoing connections "
      self             = null
      from_port        = 0
      to_port          = 0
      protocol         = "-1"
      prefix_list_ids  = null
      ipv6_cidr_blocks = null
      security_groups  = null
      cidr_blocks      = ["0.0.0.0/0"]
    }
  ]

  tags = merge(var.tags, { Name = "${local.bridge_name}-securityGroup" })
  lifecycle {
    ignore_changes = [ingress]
  }
}

resource "aws_instance" "default" {
  ami           = data.aws_ami.linux.id
  instance_type = var.instance_type

  vpc_security_group_ids      = [aws_security_group.bridge_sg.id]
  iam_instance_profile        = aws_iam_instance_profile.bridge_profile.name
  key_name                    = aws_key_pair.imported_key.key_name
  subnet_id                   = var.subnet_id
  user_data_replace_on_change = true
  associate_public_ip_address = false

  # user_data = (templatefile("${path.module}/user_data.sh", {
  #   secret_id = var.SECRET_ID
  # }))
  root_block_device {
    encrypted   = true
    volume_size = 200
    tags        = var.tags
  }

  tags       = merge(var.tags, { Name = local.bridge_name })
  depends_on = [aws_iam_instance_profile.bridge_profile, aws_security_group.bridge_sg]
}


# resource "aws_vpc_endpoint" "secretsmanager" {
#   vpc_id              = var.vpc_id
#   service_name        = "com.amazonaws.${data.aws_region.current.name}.secretsmanager"
#   vpc_endpoint_type   = "Interface"
#   subnet_ids          = [var.subnet_id]
#   security_group_ids  = [aws_security_group.bridge_sg.id]
#   private_dns_enabled = false

#   tags = {
#     Name = "${local.bridge_name}-secretsmanager-endpoint"
#   }
# }