import json
import boto3
import os
import re
import logging
import urllib.parse

# Set up logging
logger = logging.getLogger()
logger.setLevel("INFO")


def chunk_string_by_comma(input_string, max_length):
    """Break a comma-separated string into chunks where each chunk's length is less than max_length."""
    chunks = []
    current_chunk = []
    current_length = 0

    for item in input_string.split(","):
        item_length = len(item) + 1  # +1 for the comma
        if current_length + item_length > max_length:
            chunks.append(",".join(current_chunk))
            current_chunk = [item]
            current_length = item_length
        else:
            current_chunk.append(item)
            current_length += item_length

    if current_chunk:
        chunks.append(",".join(current_chunk))

    return chunks


def get_quick_sight_dashboard_url(
    aws_account_id,
    dashboard_id_list,
    dashboard_region,
    filters,
    site_ids_csv,
    rls_enabled,
):
    """Generate QuickSight dashboard URL for an anonymous user"""
    quick_sight = boto3.client("quicksight", region_name=dashboard_region)

    dashboard_arn_list = [
        f"arn:aws:quicksight:{dashboard_region}:{aws_account_id}:dashboard/{dashboard_id}"
        for dashboard_id in dashboard_id_list
    ]

    request_params = {
        "AwsAccountId": aws_account_id,
        "Namespace": "default",
        "ExperienceConfiguration": {
            "Dashboard": {"InitialDashboardId": dashboard_id_list[0]}
        },
        "AuthorizedResourceArns": dashboard_arn_list,
        "SessionLifetimeInMinutes": 30,
        "AllowedDomains": [filters.get("allowedDomain", "")],
    }

    # Add SessionTags only if RLS is enabled and site_ids_csv is not None

    logger.info(f"site ids csv ----> {site_ids_csv}")

    if rls_enabled and site_ids_csv:
        if len(site_ids_csv) > 256:
            site_id_chunks = chunk_string_by_comma(site_ids_csv, 256)
            request_params["SessionTags"] = [
                {
                    "Key": "store_id",
                    "Value": site_id_chunks[0],
                }  # First chunk gets "store_id"
            ] + [
                {"Key": f"store_id_{i+1}", "Value": chunk}
                for i, chunk in enumerate(
                    site_id_chunks[1:]
                )  # Remaining chunks get "store_id_1", "store_id_2", ...
            ]
        else:
            request_params["SessionTags"] = [{"Key": "store_id", "Value": site_ids_csv}]

    logger.info(f"==============================")
    logger.info(f"Request params ---->>>> : {request_params}")
    logger.info(f"==============================")

    response = quick_sight.generate_embed_url_for_anonymous_user(**request_params)
    return response


def validate_site_id(site_id):
    return re.match(r"^[a-zA-Z0-9_-]+$", site_id) is not None


def get_embed_url_with_client_filters(
    embed_url, restaurant_name, restaurant_id, report_date
):
    client_name = os.getenv("CLIENT_NAME")
    deployment_env = os.getenv("DEPLOYMENT_ENVIRONMENT")

    logging.info(
        f"Start generating client-specific URL for client {client_name} in {deployment_env}"
    )
    logger.info(
        f"RQ: restaurant_name: {restaurant_name}, restaurant_id: {restaurant_id}, report_date: {report_date}"
    )

    select_restaurant = f"{restaurant_name} ({restaurant_id})"
    encoded_restaurant = urllib.parse.quote(select_restaurant)
    encoded_main_date = urllib.parse.quote(report_date)

    if client_name == "bkg-nzl":
        embed_url_with_filters = f"{embed_url}&sheetId={os.getenv('DASHBOARD_SHEET_ID')}#p.RestaurantIdentifier={encoded_restaurant}&p.MainDate={encoded_main_date}"
    elif client_name == "cfa-usa":
        embed_url_with_filters = f"{embed_url}#p.restaurantIdentifier={encoded_restaurant}&p.mainDate={encoded_main_date}"
    elif client_name == "cfa-usa-weekly":
        embed_url_with_filters = f"{embed_url}#p.restaurantIdentifier={encoded_restaurant}&p.mainDate={encoded_main_date}"
    else:
        logging.warning(f"Invalid Client Name =----> {client_name}")
        return None

    logging.info(f"EMBEDDING: {embed_url_with_filters}\n\n")
    return embed_url_with_filters


def lambda_handler(event, context):
    """Lambda function handler"""
    logger.info(event)
    try:
        aws_account_id = os.environ["DASHBOARD_AWS_ACCOUNT_ID"]

        filters = event.get("filters", {})

        logger.info(f"============================== filters: {filters}")

        site_ids = filters.get("siteIds", [])
        if site_ids == ["*"]:
            site_ids_csv = "*"
        else:
            site_ids_csv = ",".join(site_ids) if site_ids else None

            if site_ids and not all(validate_site_id(site_id) for site_id in site_ids):
                raise ValueError("Invalid siteId provided")

        client_config = {
            "region": os.environ["REGION"],
            "dashboard_ids": [os.environ["DASHBOARD_ID"]],
            "rls_enabled": os.environ["RLS_ENABLED"],
        }

        dashboard_region = client_config["region"]
        dashboard_id_list = client_config["dashboard_ids"]
        rls_enabled = client_config["rls_enabled"]

        quicksight_url = get_quick_sight_dashboard_url(
            aws_account_id,
            dashboard_id_list,
            dashboard_region,
            filters,
            site_ids_csv,
            rls_enabled,
        )

        restaurant_name = filters.get("restaurantName")
        restaurant_id = filters.get("restaurantID")
        report_date = filters.get("reportDate")

        client_embed_url = get_embed_url_with_client_filters(
            quicksight_url["EmbedUrl"], restaurant_name, restaurant_id, report_date
        )

        # Prepare response
        quicksight_embedding = {
            "allowedDomain": filters.get("allowedDomain", ""),
            "url": quicksight_url["EmbedUrl"],
            "client_embed_url": client_embed_url,
        }
        if site_ids:
            quicksight_embedding["siteIds"] = site_ids

        response = {
            "statusCode": 200,
            "headers": {
                "Content-Type": "application/json",
                "Access-Control-Allow-Origin": "*",  # Allows requests from any origin
                "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type",
            },
            "body": json.dumps({"quicksightEmbedding": quicksight_embedding}),
        }
        logger.info(response)
        return response

    except Exception as e:
        quicksight_embedding = {"error": str(e)}
        response = {
            "statusCode": 500,
            "headers": {
                "Content-Type": "application/json",
                "Access-Control-Allow-Origin": "*",  # Allows requests from any origin
                "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type",
            },
            "body": json.dumps(quicksight_embedding),
        }
        logger.error(response)
        return response
