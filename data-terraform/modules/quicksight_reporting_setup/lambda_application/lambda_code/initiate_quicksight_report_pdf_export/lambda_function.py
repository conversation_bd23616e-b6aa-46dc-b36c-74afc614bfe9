import json
import boto3
import os
import uuid
from datetime import datetime, timedelta
import pytz  # For handling time zones
import logging

# Environment variables
REGION_NAME = os.environ["REGION_NAME"]
QUEUE_URL = os.environ.get("QUEUE_URL")  # SQS Queue URL
REPORT_TRACKING_DYNAMO_TABLE_NAME = os.environ.get("REPORT_TRACKING_DYNAMO_TABLE_NAME")
EXECUTION_TRACKING_DYNAMO_TABLE_NAME = os.environ.get(
    "EXECUTION_TRACKING_DYNAMO_TABLE_NAME"
)
EC2_INSTANCE_ID = os.environ.get("EC2_INSTANCE_ID")  # EC2 Instance ID
SNS_TOPIC_ARN = os.environ.get("SNS_TOPIC_ARN")  # SNS Topic ARN
REDSHIFT_CLUSTER_ID = os.environ["REDSHIFT_CLUSTER_ID"]
DATABASE = os.environ["DATABASE"]
DB_USER = os.environ["DB_USER"]
ENV_CODE = os.environ["ENV_CODE"]
TIME_ZONE = os.environ["TIME_ZONE"]
EXECUTION_TIME_WINDOW_START_TIME = os.environ["EXECUTION_TIME_WINDOW_START_TIME"]
EXECUTION_TIME_WINDOW_END_TIME = os.environ["EXECUTION_TIME_WINDOW_END_TIME"]
CLIENT_NAME = os.environ["CLIENT_NAME"]

PDF_RENDER_VIEW_WIDTH_PX = os.environ.get("PDF_RENDER_VIEW_WIDTH_PX", "1920")
PDF_RENDER_VIEW_HEIGHT_PX = os.environ.get("PDF_RENDER_VIEW_HEIGHT_PX", "4000")
PDF_RENDER_WAITING_TIME_MS = os.environ.get("PDF_RENDER_WAITING_TIME_MS", "120000")
PDF_PRINT_PAGE_SIZE = os.environ.get("PDF_PRINT_PAGE_SIZE", "A2")

# Initialize AWS clients
sqs_client = boto3.client("sqs", region_name=REGION_NAME)
dynamodb_client = boto3.resource("dynamodb", region_name=REGION_NAME)
ec2_client = boto3.client("ec2", region_name=REGION_NAME)
sns_client = boto3.client("sns", region_name=REGION_NAME)
redshift_data = boto3.client("redshift-data", region_name=REGION_NAME)

# Set up logging
logger = logging.getLogger()
logger.setLevel("INFO")

CLIENT_NAMES_MAP = {
    "BKG_NZL": "bkg-nzl",
    "CFA_USA": "cfa-usa",
    "CFA_USA_WEEKLY": "cfa-usa-weekly",
}


def check_and_start_ec2(instance_id):
    try:
        # Check EC2 instance status
        response = ec2_client.describe_instance_status(InstanceIds=[instance_id])
        instance_statuses = response.get("InstanceStatuses", [])

        if (
            not instance_statuses
            or instance_statuses[0]["InstanceState"]["Name"] != "running"
        ):
            logger.info(f"EC2 instance {instance_id} is not running. Starting it now.")
            ec2_client.start_instances(InstanceIds=[instance_id])
            logger.info(f"Start request sent for EC2 instance {instance_id}.")
        else:
            logger.info(f"EC2 instance {instance_id} is already running.")
    except Exception as e:
        logger.info(f"Error checking or starting EC2 instance {instance_id}: {str(e)}")
        raise


def send_sns_notification(subject, message):
    try:
        response = sns_client.publish(
            TopicArn=SNS_TOPIC_ARN, Subject=subject, Message=message
        )
        logger.info(f"SNS notification sent. MessageId: {response['MessageId']}")
    except Exception as e:
        logger.info(f"Error sending SNS notification: {str(e)}")
        raise


def execute_sql(sql_statement):
    """
    Function to execute an SQL statement using the Redshift Data API.
    """
    response = redshift_data.execute_statement(
        ClusterIdentifier=REDSHIFT_CLUSTER_ID,
        Database=DATABASE,
        DbUser=DB_USER,
        Sql=sql_statement,
    )

    # Check the execution id for the SQL statement
    query_id = response["Id"]

    # Wait for query to complete
    status = redshift_data.describe_statement(Id=query_id)
    while status["Status"] in ["SUBMITTED", "PICKED", "STARTED"]:
        # time.sleep(1)  # Wait before checking the status again
        status = redshift_data.describe_statement(Id=query_id)

    # Check for errors
    if status["Status"] == "FAILED":
        raise Exception(f"SQL execution failed: {status['Error']}")

    return query_id


def get_query_results(query_id):
    records = []
    next_token = None

    while True:
        if next_token:
            result_response = redshift_data.get_statement_result(
                Id=query_id, NextToken=next_token
            )
        else:
            result_response = redshift_data.get_statement_result(Id=query_id)

        records.extend(result_response["Records"])
        next_token = result_response.get("NextToken", None)

        # If there's no next token, we've retrieved all data
        if not next_token:
            break

    return records


def get_records_for_bkg_nzl():

    blacklisted_ids = []

    db_records = []
    processed_records = []

    if ENV_CODE == "dev":
        # Mock data for development environment
        db_records = [
            [
                {"stringValue": "mock-00012"},
                {"stringValue": "Highland Park"},
                {
                    "stringValue": "<EMAIL>,<EMAIL>"
                },
            ],
            [
                {"stringValue": "mock-00008"},
                {"stringValue": "Panmure"},
                {
                    "stringValue": "<EMAIL>,<EMAIL>"
                },
            ],
            [
                {"stringValue": "mock-00075"},
                {"stringValue": "Addington"},
                {
                    "stringValue": "<EMAIL>,<EMAIL>"
                },
            ],
            [
                {"stringValue": "mock-00043"},
                {"stringValue": "Shirley"},
                {
                    "stringValue": "<EMAIL>,<EMAIL>"
                },
            ],
        ]
    else:
        # Actual SQL execution for non-development environments
        sql = "SELECT fingermark_id, restaurant_name, email FROM eyecue_bkg_nzl.dim_restaurant"
        query_id = execute_sql(sql)
        db_records = get_query_results(query_id)

    logger.info(f"No of DB records for bkg-nzl : {len(db_records)}")
    logger.info(f"Raw DB records for bkg-nzl : {db_records}")

    for record in db_records:
        fingermark_id = record[0]["stringValue"]
        restaurant_name = record[1]["stringValue"]
        email = record[2]["stringValue"]
        email_list = [e.strip() for e in email.split(",")]

        parts = fingermark_id.split("-")
        restaurant_id = parts[-1]
        # Check if the restaurantID is in the blacklist
        if restaurant_id in blacklisted_ids:
            continue  # Skip this record if blacklisted

        processed_records.append(
            {
                "restaurantName": restaurant_name,
                "restaurantID": parts[-1],
                "receiverEmails": email_list,
            }
        )

    return processed_records


def get_records_for_cfa_usa():

    blacklisted_ids = []

    db_records = []
    processed_records = []

    if ENV_CODE == "dev":
        # Mock data for development environment
        db_records = [
            [
                {"stringValue": "fm-cfa-usa-00829"},
                {"stringValue": "Carrollton FSU"},
                {
                    "stringValue": "<EMAIL>,<EMAIL>"
                },
            ],
            [
                {"stringValue": "fm-cfa-usa-02276"},
                {"stringValue": "Goose Creek FSU"},
                {
                    "stringValue": "<EMAIL>,<EMAIL>"
                },
            ],
            [
                {"stringValue": "fm-cfa-usa-02196"},
                {"stringValue": "Razorback Road FSU"},
                {
                    "stringValue": "<EMAIL>,<EMAIL>"
                },
            ],
        ]
    else:
        # Actual SQL execution for non-development environments
        sql = "SELECT store_id, store_name, email_addresses from eyecue_cfa_usa.tmp_store_email_addresses"
        query_id = execute_sql(sql)
        db_records = get_query_results(query_id)

    logger.info(f"No of DB records for cfa-usa : {len(db_records)}")
    logger.info(f"Raw DB records for cfa-usa : {db_records}")

    for record in db_records:
        fingermark_id = record[0]["stringValue"]
        restaurant_name = record[1]["stringValue"]
        email = record[2]["stringValue"]
        email_list = [e.strip() for e in email.split(",")]

        parts = fingermark_id.split("-")
        restaurant_id = parts[-1]
        # Check if the restaurantID is in the blacklist
        if restaurant_id in blacklisted_ids:
            continue  # Skip this record if blacklisted

        processed_records.append(
            {
                "restaurantName": restaurant_name,
                "restaurantID": parts[-1],
                "receiverEmails": email_list,
            }
        )

    return processed_records


def get_records_for_cfa_usa_weekly():

    blacklisted_ids = []

    db_records = []
    processed_records = []

    if ENV_CODE == "dev":
        # Mock data for development environment
        db_records = [
            [
                {"stringValue": "fm-cfa-usa-00829"},
                {"stringValue": "Carrollton FSU"},
                {
                    "stringValue": "<EMAIL>,<EMAIL>"
                },
            ],
            [
                {"stringValue": "fm-cfa-usa-02276"},
                {"stringValue": "Goose Creek FSU"},
                {
                    "stringValue": "<EMAIL>,<EMAIL>"
                },
            ],
            [
                {"stringValue": "fm-cfa-usa-02196"},
                {"stringValue": "Razorback Road FSU"},
                {
                    "stringValue": "<EMAIL>,<EMAIL>"
                },
            ],
        ]
    else:
        # Actual SQL execution for non-development environments
        sql = "SELECT store_id, store_name, email_addresses from eyecue_cfa_usa.tmp_store_email_addresses"
        query_id = execute_sql(sql)
        db_records = get_query_results(query_id)

    logger.info(f"No of DB records for cfa-usa : {len(db_records)}")
    logger.info(f"Raw DB records for cfa-usa : {db_records}")

    for record in db_records:
        fingermark_id = record[0]["stringValue"]
        restaurant_name = record[1]["stringValue"]
        email = record[2]["stringValue"]
        email_list = [e.strip() for e in email.split(",")]

        parts = fingermark_id.split("-")
        restaurant_id = parts[-1]
        # Check if the restaurantID is in the blacklist
        if restaurant_id in blacklisted_ids:
            continue  # Skip this record if blacklisted

        processed_records.append(
            {
                "restaurantName": restaurant_name,
                "restaurantID": parts[-1],
                "receiverEmails": email_list,
            }
        )

    return processed_records


def get_records_from_db():

    if CLIENT_NAME == CLIENT_NAMES_MAP["BKG_NZL"]:
        return get_records_for_bkg_nzl()
    elif CLIENT_NAME == CLIENT_NAMES_MAP["CFA_USA"]:
        return get_records_for_cfa_usa()
    elif CLIENT_NAME == CLIENT_NAMES_MAP["CFA_USA_WEEKLY"]:
        return get_records_for_cfa_usa_weekly()
    else:
        return []


def generate_report_data(local_time_now, yesterday_local_time):
    db_records = get_records_from_db()

    yesterday_str = yesterday_local_time.strftime("%Y-%m-%d")
    today_str = local_time_now.strftime("%Y-%m-%d")
    yesterday_formatted_str = yesterday_local_time.strftime("%A %d %B %Y")
    yesterday_formatted_str_for_body = yesterday_local_time.strftime("%A %d %B, %Y")

    # Determine Monday and Saturday based on yesterday_local_time for CFA Weekly Reporting
    today_weekday = yesterday_local_time.weekday()  # Monday = 0, Sunday = 6

    if today_weekday == 6:  # If today is Sunday
        monday_date = yesterday_local_time - timedelta(days=6)
        saturday_date = yesterday_local_time - timedelta(days=1)
    else:
        monday_date = yesterday_local_time - timedelta(days=today_weekday + 7)
        saturday_date = monday_date + timedelta(days=5)

    # Generate the two required string formats
    date_range_format1 = (
        f"{monday_date.strftime('%B %d')} to {saturday_date.strftime('%B %d, %Y')}"
    )
    date_range_format2 = f"{monday_date.strftime('%A %B %d')} through to {saturday_date.strftime('%A %B %d, %Y')}"

    report_init_data = []

    for record in db_records:

        report_init_data.append(
            {
                "restaurantName": record["restaurantName"],
                "restaurantID": record["restaurantID"],
                "receiverEmails": record["receiverEmails"],
                "reportType": "pdf",
                "reportFormat": PDF_PRINT_PAGE_SIZE,
                "reportDate": (
                    today_str
                    if CLIENT_NAME == CLIENT_NAMES_MAP["CFA_USA_WEEKLY"]
                    else yesterday_str
                ),
                "reportDateForEmailHeader": yesterday_formatted_str,
                "reportDateForEmailBody": yesterday_formatted_str_for_body,
                "pdfRenderWidthPX": PDF_RENDER_VIEW_WIDTH_PX,
                "pdfRenderHeightPX": PDF_RENDER_VIEW_HEIGHT_PX,
                "pdfRenderWaitingTimeMS": PDF_RENDER_WAITING_TIME_MS,
                "reportDateForEmailHeader_CFA_WEEKLY": date_range_format1,
                "reportDateForEmailBody_CFA_WEEKLY": date_range_format2,
            }
        )

    logger.info(f"Report initialization data for {CLIENT_NAME} : {report_init_data}")

    return report_init_data


def run_report_execution_logic(local_time_now, yesterday_local_time):

    local_time_now_str = local_time_now.strftime("%Y-%m-%d")
    report_data = generate_report_data(local_time_now, yesterday_local_time)

    logger.info(f"Report data: {report_data}")
    logger.info(f"Report data size: {len(report_data)}")

    # DynamoDB table
    report_tracking_dynamo_table = dynamodb_client.Table(
        REPORT_TRACKING_DYNAMO_TABLE_NAME
    )

    # Loop through each report and process
    for report in report_data:
        # Generate a unique reportId
        report_id = str(uuid.uuid4())
        # Add the generated reportId to the report data
        report["reportID"] = report_id
        # Convert report to JSON string
        message_body = json.dumps(report)
        # Send message to SQS
        sqs_response = sqs_client.send_message(
            QueueUrl=QUEUE_URL, MessageBody=message_body
        )
        logger.info(
            f"Message sent to SQS. MessageId: {sqs_response['MessageId']} with message data : {report}"
        )
        # Insert metadata into DynamoDB
        report_tracking_dynamo_table.put_item(
            Item={
                "report_id": report_id,
                "report_date": report["reportDate"],
                "restaurant_name": report["restaurantName"],
                "execution_date_time": local_time_now_str,
                "execution_time_zone": TIME_ZONE,
                "receiver_emails": ",".join(report["receiverEmails"]),
                "status": "INITIATED",
            }
        )
        logger.info(f"Record inserted into DynamoDB for reportID: {report_id}")
    # Check and start EC2 instance if necessary
    check_and_start_ec2(EC2_INSTANCE_ID)


def is_time_permits(local_time_now):
    is_in_allowed_time_window = (
        local_time_now.time()
        >= datetime.strptime(EXECUTION_TIME_WINDOW_START_TIME, "%H:%M").time()
        and local_time_now.time()
        <= datetime.strptime(EXECUTION_TIME_WINDOW_END_TIME, "%H:%M").time()
    )

    # If CLIENT_NAME is "CFA_USA", check if today is Monday
    if (
        CLIENT_NAME == CLIENT_NAMES_MAP["CFA_USA"] and local_time_now.weekday() == 0
    ):  # Monday is 0
        logger.info("Execution is skipped because today is Monday for CFA_USA.")
        return False
    elif (
        CLIENT_NAME == CLIENT_NAMES_MAP["CFA_USA_WEEKLY"]
        and local_time_now.weekday() != 0
    ):
        logger.info(
            "Execution is skipped because today is not Monday for CFA_USA_WEEKLY report."
        )
        return False

    return is_in_allowed_time_window


def lambda_handler(event, context):
    try:
        local_tz = pytz.timezone(TIME_ZONE)
        local_time_now = datetime.now(local_tz)

        logger.info(f"{TIME_ZONE} time Now ---------------->> : {local_time_now}")

        # TODO Only for Testing
        # today_local_date = local_time_now.strftime("%Y-%m-%d")
        # yesterday_local_time = local_time_now - timedelta(days=1)

        # report_data = generate_report_data(yesterday_local_time)
        # logger.info(f"Report data: {report_data}")
        # logger.info(f"Report data size: {len(report_data)}")
        # TODO Only for Testing

        if is_time_permits(local_time_now):
            # Format today's date in timezone as a string
            today_local_date = local_time_now.strftime("%Y-%m-%d")
            yesterday_local_time = local_time_now - timedelta(days=1)

            # Reference the execution tracking table
            execution_tracking_table = dynamodb_client.Table(
                EXECUTION_TRACKING_DYNAMO_TABLE_NAME
            )

            # Check if today's date already exists in the execution tracking table
            response = execution_tracking_table.get_item(Key={"date": today_local_date})

            # If today's date is not present, proceed
            if "Item" not in response:

                run_report_execution_logic(local_time_now, yesterday_local_time)

                # Insert today's date into execution tracking table
                execution_tracking_table.put_item(Item={"date": today_local_date})

                return {
                    "statusCode": 200,
                    "body": json.dumps({"message": "Reports processed successfully"}),
                }

            else:
                logger.info(
                    f"Execution already performed for today's date: {today_local_date}"
                )
                return {
                    "statusCode": 200,
                    "body": json.dumps(
                        {"message": "Execution already performed for today's date."}
                    ),
                }

        else:
            logger.info(
                f"Current time is outside the allowed execution window ({EXECUTION_TIME_WINDOW_START_TIME} - {EXECUTION_TIME_WINDOW_END_TIME} : {TIME_ZONE} time)."
            )
            return {
                "statusCode": 200,
                "body": json.dumps({"message": "Outside execution window"}),
            }
    except Exception as e:
        error_message = f"Error processing reports: {str(e)}"
        logger.info(error_message)

        # Send SNS notification for failure
        send_sns_notification("Lambda Failure: Report Initiation", error_message)

        return {"statusCode": 500, "body": json.dumps({"error": str(e)})}
