import boto3
import json
import os
import base64
import logging

region_name = os.environ["REGION"]
ses_region_name = os.environ["SES_REGION"]

# Initialize AWS clients
sqs = boto3.client("sqs", region_name=region_name)
s3 = boto3.client("s3", region_name=region_name)
ses = boto3.client("ses", region_name=ses_region_name)
dynamodb = boto3.resource("dynamodb", region_name=region_name)
sns = boto3.client("sns", region_name=region_name)

# Environment Variables
SES_SENDER_EMAIL = os.environ["SES_SENDER_EMAIL"]  # Verified SES email address
TEMP_DOWNLOAD_PATH = "/tmp/"  # Temporary directory for Lambda
DYNAMO_TABLE_NAME = os.environ["DYNAMO_TABLE_NAME"]  # DynamoDB Table Name
SNS_TOPIC_ARN = os.environ["SNS_TOPIC_ARN"]  # SNS Topic ARN
SES_BCC_EMAIL = os.environ["BCC_EMAIL"]
CLIENT_NAME = os.environ["CLIENT_NAME"]

# Initialize DynamoDB Table
dynamo_table = dynamodb.Table(DYNAMO_TABLE_NAME)

# Set up logging
logger = logging.getLogger()
logger.setLevel("INFO")

CLIENT_NAMES_MAP = {
    "BKG_NZL": "bkg-nzl",
    "CFA_USA": "cfa-usa",
    "CFA_USA_WEEKLY": "cfa-usa-weekly",
}


def send_sns_error_notification(subject, message):
    """Send error notification via SNS."""
    try:
        response = sns.publish(
            TopicArn=SNS_TOPIC_ARN, Subject=f"Email Sender: {subject}", Message=message
        )
        logger.info(f"SNS error notification sent. Message ID: {response['MessageId']}")
    except Exception as e:
        logger.info(f"Error sending SNS notification: {e}")


def parse_s3_location(s3_location):
    """Parse S3 bucket name and key from an S3 location."""
    if not s3_location.startswith("s3://"):
        raise ValueError("Invalid S3 location format")
    s3_location = s3_location.replace("s3://", "")
    bucket_name, object_key = s3_location.split("/", 1)
    return bucket_name, object_key


def download_pdf_from_s3(s3_location):
    """Download the PDF file from S3."""
    bucket_name, object_key = parse_s3_location(s3_location)
    file_name = object_key.split("/")[-1]
    local_file_path = f"{TEMP_DOWNLOAD_PATH}{file_name}"

    s3.download_file(bucket_name, object_key, local_file_path)
    logger.info(f"File downloaded from S3: {local_file_path}")
    return local_file_path


def send_email_via_ses(
    receiver_email,
    pdf_path,
    png_path,
    report_id,
    restaurant_name,
    restaurant_code,
    report_date_for_header,
    report_date_for_body,
    report_date_for_header_cfa_weekly,
    report_date_for_body_cfa_weekly,
):
    """Send an email with PDF attachment using SES."""
    with open(pdf_path, "rb") as pdf_file:
        pdf_data = pdf_file.read()

    with open(png_path, "rb") as png_file:
        png_data = base64.b64encode(png_file.read()).decode("utf-8")

    encoded_pdf = base64.b64encode(pdf_data).decode("utf-8")

    # Set the email content based on the client name
    if CLIENT_NAME == CLIENT_NAMES_MAP["BKG_NZL"]:
        inquiry_text = (
            "<p>All questions can go to the District Manager or Area Manager.</p>"
        )
    elif CLIENT_NAME == CLIENT_NAMES_MAP["CFA_USA"]:
        inquiry_text = "<p>All questions can be raised with the DTCV team at <a href='mailto:<EMAIL>'><EMAIL></a>.</p>"

    elif CLIENT_NAME == CLIENT_NAMES_MAP["CFA_USA_WEEKLY"]:
        inquiry_text = "<p>All questions can be raised with the DTCV team at <a href='mailto:<EMAIL>'><EMAIL></a>.</p>"
    else:
        inquiry_text = ""

    subject = ""
    body_date_section = ""

    if CLIENT_NAME == CLIENT_NAMES_MAP["CFA_USA_WEEKLY"]:
        subject = f"Weekly Fingermark Eyecue Drive-Thru Report for {restaurant_name} ({restaurant_code}) - {report_date_for_header_cfa_weekly}"
        body_date_section = f"<p>Please find attached the Fingermark Eyecue™ Weekly Report for your restaurant for the week {report_date_for_body_cfa_weekly}.</p>"
    else:
        subject = f"Fingermark Eyecue Drive-Thru Report for {restaurant_name} ({restaurant_code}) on {report_date_for_header}"
        body_date_section = f"<p>The following is the Fingermark Eyecue™ Drive-Thru report for the {restaurant_name} restaurant for {report_date_for_body}.</p>"

    body_html = f"""
    <html>
    <body>
        {body_date_section}
        {inquiry_text}
        <p>Thank you.</p>
        <img src="cid:report_image" alt="Report Image" style="max-width:800px; width:100%; height:auto;">
    </body>
    </html>
    """

    raw_email = f"""\
From: {SES_SENDER_EMAIL}
To: {receiver_email}
BCC: {SES_BCC_EMAIL}
Subject: {subject}
MIME-Version: 1.0
Content-Type: multipart/mixed; boundary="NextPart"

--NextPart
Content-Type: text/html; charset="UTF-8"
Content-Transfer-Encoding: 7bit

{body_html}

--NextPart
Content-Type: image/png; name="{report_id}.png"
Content-Transfer-Encoding: base64
Content-ID: <report_image>
Content-Disposition: inline; filename="{report_id}.png"

{png_data}

--NextPart
Content-Type: application/pdf; name="{report_id}.pdf"
Content-Transfer-Encoding: base64
Content-Disposition: attachment; filename="{report_id}.pdf"

{encoded_pdf}

--NextPart--
"""

    response = ses.send_raw_email(
        Source=SES_SENDER_EMAIL,
        Destinations=[receiver_email, SES_BCC_EMAIL],
        RawMessage={"Data": raw_email},
    )
    logger.info(f"Email sent successfully via SES. Message ID: {response['MessageId']}")


def update_dynamo_status(report_id, status):
    """Update the status field in the DynamoDB table."""
    update_expression = "SET #status = :status"
    expression_attribute_names = {"#status": "status"}
    expression_attribute_values = {":status": status}

    dynamo_table.update_item(
        Key={"report_id": report_id},
        UpdateExpression=update_expression,
        ExpressionAttributeNames=expression_attribute_names,
        ExpressionAttributeValues=expression_attribute_values,
    )
    logger.info(f"DynamoDB updated: {report_id}, status: {status}")


def lambda_handler(event, context):
    """Lambda function entry point."""
    for record in event["Records"]:
        try:
            # Parse SQS message body
            body = json.loads(record["body"])

            logger.info(f"Body : {body}")

            report_id = body.get("reportID")
            receiver_emails = body.get("receiverEmails")
            s3_location = body.get("s3Location")
            s3_location_png = body.get("pngS3Location")
            restaurant_name = body.get("restaurantName")
            restaurant_code = body.get("restaurantID")
            report_date_for_header = body.get("reportDateForEmailHeader")
            report_date_for_body = body.get("reportDateForEmailBody")
            full_json = body.get("fullJsonPayload")

            if not all([report_id, receiver_emails, s3_location]):
                error_message = "Message missing required fields."
                logger.info(error_message)
                send_sns_error_notification("Missing Message Fields", error_message)
                raise ValueError(error_message)

            # Download PDF report from S3
            pdf_path = download_pdf_from_s3(s3_location)
            png_path = download_pdf_from_s3(s3_location_png)

            for receiver_email in receiver_emails:
                # Send email via SES
                send_email_via_ses(
                    receiver_email,
                    pdf_path,
                    png_path,
                    report_id,
                    restaurant_name,
                    restaurant_code,
                    report_date_for_header,
                    report_date_for_body,
                    full_json["reportDateForEmailHeader_CFA_WEEKLY"],
                    full_json["reportDateForEmailBody_CFA_WEEKLY"],
                )
                logger.info(
                    f"Email sent for store {restaurant_name} : {restaurant_code} for {receiver_email}"
                )

            # Update DynamoDB status
            update_dynamo_status(report_id, "EMAIL_SENT")

        except Exception as e:
            error_message = f"Error processing message: {e}"
            logger.info(error_message)
            send_sns_error_notification("Error Processing Message", error_message)
            raise e  # Raise the exception to prevent the message from being deleted
