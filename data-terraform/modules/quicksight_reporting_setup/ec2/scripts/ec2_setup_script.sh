#!/bin/bash
set -e

user_name="quicksightuser"

# Check if the user already exists
if id "$user_name" &>/dev/null; then
    echo "User $user_name already exists. Skipping user setup."
else

sudo adduser --disabled-password --gecos "" $user_name 

sudo usermod -aG sudo,adm $user_name

echo "$user_name ALL=(ALL) NOPASSWD:ALL" | sudo tee /etc/sudoers.d/$user_name

su - $user_name <<EOF

export DEBIAN_FRONTEND=noninteractive

# Temporarily disable service restart prompts
echo -e '#!/bin/sh\nexit 101' | sudo tee /usr/sbin/policy-rc.d
sudo chmod +x /usr/sbin/policy-rc.d

# Ensure all locks are released before proceeding
while sudo fuser /var/lib/dpkg/lock-frontend >/dev/null 2>&1; do
    sleep 5
done

sudo apt-get update -yq

# Ensure all locks are released before proceeding
while sudo fuser /var/lib/dpkg/lock-frontend >/dev/null 2>&1; do
    sleep 5
done

# Suppress prompts for services to restart
sudo DEBIAN_FRONTEND=noninteractive apt-get upgrade -yq -o Dpkg::Options::="--force-confdef" -o Dpkg::Options::="--force-confold"

# Ensure all locks are released before proceeding
while sudo fuser /var/lib/dpkg/lock-frontend >/dev/null 2>&1; do
    sleep 5
done

# This is to generate images from pdf
sudo apt install graphicsmagick -y

# Install necessary packages
sudo apt-get install -yq ubuntu-desktop firefox stacer mmv unzip
sudo apt-get install -yq firefox
sudo apt-get install -yq stacer
sudo apt-get install -yq mmv
sudo apt-get install -yq unzip

# Ensure all locks are released before proceeding
while sudo fuser /var/lib/dpkg/lock-frontend >/dev/null 2>&1; do
    sleep 5
done

# Download and install xRDP
cd ~ && wget https://www.c-nergy.be/downloads/xRDP/xrdp-installer-1.4.zip
unzip xrdp-installer-1.4.zip
chmod +x xrdp-installer-1.4.sh && ./xrdp-installer-1.4.sh

# Change xRDP port
sudo sed -i 's/3389/53579/g' /etc/xrdp/xrdp.ini

# Re-enable service restarts
sudo rm -f /usr/sbin/policy-rc.d
EOF

fi

# Add your additional code here
echo "Continuing with the rest of the script..."

su - ubuntu <<'EOF'

# Check if AWS CLI is already installed
if ! command -v aws &> /dev/null; then
  echo "AWS CLI not found. Installing AWS CLI..."

  # Download the AWS CLI installer
  curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"

  # Unzip the installer
  unzip awscliv2.zip

  # Run the installer
  sudo ./aws/install

  # Verify the installation
  if command -v aws &> /dev/null; then
    echo "AWS CLI successfully installed. Version: $(aws --version)"
  else
    echo "AWS CLI installation failed."
  fi

  # Cleanup the installation files
  rm -rf awscliv2.zip aws
else
  echo "AWS CLI is already installed. Version: $(aws --version)"
fi

EOF


mkdir -p /home/<USER>/node-app
# Set permissions for the node-app directory
chown -R ubuntu:ubuntu /home/<USER>/node-app

su - ubuntu <<'EOF'
cd /home/<USER>/node-app

aws s3 cp ${node_app_s3_uri} .

unzip node-app.zip

curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.4/install.sh | bash
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"

source ~/.bashrc

nvm install v22.13.0
nvm alias default v22.13.0

source ~/.bashrc

npm i -g pm2

npm install

pm2 status > /dev/null

pm2 startup > /dev/null

cd /home/<USER>

aws s3 cp ${node_app_start_script_uri} .

EOF

chmod +x /home/<USER>/start-node-app.sh
chown ubuntu:ubuntu /home/<USER>/start-node-app.sh

# Create a systemd service to start the Node.js app on boot
cat <<EOF | sudo tee /etc/systemd/system/node-app.service
[Unit]
Description=Start Node.js app with PM2 on EC2 startup
After=network.target

[Service]
Type=simple
ExecStart=/bin/bash -c '/home/<USER>/start-node-app.sh && tail -f /dev/null'
User=ubuntu
WorkingDirectory=/home/<USER>/node-app
Environment="PATH=/usr/local/bin:/usr/bin:/home/<USER>/.nvm/versions/node/v22.13.0/bin"

[Install]
WantedBy=multi-user.target
EOF


# Enable and start the systemd service
sudo systemctl daemon-reload
sudo systemctl enable node-app.service
sudo systemctl start node-app.service

echo "Node.js application setup with PM2 systemd service completed successfully!"
