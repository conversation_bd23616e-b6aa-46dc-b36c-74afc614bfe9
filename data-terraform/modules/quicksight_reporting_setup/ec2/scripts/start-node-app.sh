#!/bin/bash

# Navigate to the Node.js application directory
cd /home/<USER>/node-app || exit

# Ensure pm2 is available in the script's PATH
export PATH=$PATH:/home/<USER>/.nvm/versions/node/v22.13.0/bin/node

# Check if the app is in the PM2 process list
if pm2 status app | grep -q "online"; then
  echo "App is already running."
else
  if pm2 status app | grep -q "stopped"; then
    echo "App exists but is stopped. Starting it now."
    pm2 start app
  else
    echo "App does not exist. Removing any stale process and starting it with a worker script."
    pm2 delete app || true
    pm2 start index.js --name 'app'
  fi
fi

# Save the PM2 process list to ensure it restarts after reboot
pm2 save

