terraform {
  required_providers {
    archive = {
      source  = "hashicorp/archive"
      version = "~> 2.0"
    }
    aws = {
      source = "hashicorp/aws"
    }
  }
}

locals {
  server_name         = "${var.server_name}-${var.environment_code}"
  node_app_build_path = "${path.module}/client-wise-node-code/${var.client_name}/${var.environment_code}/node-app/code"
}

resource "null_resource" "copy_node_app_code" {
  provisioner "local-exec" {
    command = <<EOT
      mkdir -p "${local.node_app_build_path}"
      cp -r "${path.module}/node-app/code/"* "${local.node_app_build_path}/"
    EOT
  }

  triggers = {
    env_tpl_md5      = filemd5("${path.module}/node-app/code/.env.tpl")
    index_js_md5     = filemd5("${path.module}/node-app/code/index.js")
    package_json_md5 = filemd5("${path.module}/node-app/code/package.json")
    worker_js_md5    = filemd5("${path.module}/node-app/code/worker.js")
  }
}

resource "null_resource" "verify_node_app_code" {
  provisioner "local-exec" {
    command = <<EOT
      if [ ! -d "${local.node_app_build_path}" ]; then
        echo "Error: Directory not found!"
        exit 1
      fi
    EOT
  }

  depends_on = [null_resource.copy_node_app_code]
}


resource "local_file" "node_app_env_file" {
  filename = "${local.node_app_build_path}/.env"
  content = templatefile("${path.module}/node-app/code/.env.tpl", {
    aws_region                   = var.region_name
    report_upload_s3_bucket_name = var.s3_bucket_name
    report_init_data_sqs_url     = var.sqs_report_initialization_queue_url
    dynamodb_table_name          = var.dynamodb_table_name
    email_send_sqs_url           = var.email_send_sqs_url
    error_sns_topic_arn          = var.error_sns_topic_arn
    embed_url_generate_fn        = var.embed_url_generate_fn_arn
    dashboard_sheet_id           = var.dashboard_sheet_id
    deployment_environment       = var.environment_code
    client_name                  = var.client_name
  })

  depends_on = [null_resource.copy_node_app_code]
}

resource "archive_file" "node_app_zip" {

  type        = "zip"
  source_dir  = local.node_app_build_path
  output_path = "${path.module}/client-wise-node-code/${var.client_name}/${var.environment_code}/node-app/node-app.zip"

  depends_on = [null_resource.copy_node_app_code, local_file.node_app_env_file]
}

# Create a local file with the MD5 hash of the node-app.zip
resource "local_file" "node_app_zip_md5" {
  filename   = "${path.module}/client-wise-node-code/${var.client_name}/${var.environment_code}/node-app/node_app_zip.md5"
  content    = filemd5(archive_file.node_app_zip.output_path)
  depends_on = [archive_file.node_app_zip]
}

resource "aws_s3_object" "node_app_zip_upload" {
  bucket = var.s3_bucket_name
  key    = "npm-script/node-app.zip"
  source = archive_file.node_app_zip.output_path

  # <-- Critical part:
  # We explicitly set source_hash to the same MD5 we wrote locally.
  # That means if node_app_zip_md5 changes, Terraform sees this as
  # a change and will upload the new ZIP.
  source_hash = local_file.node_app_zip_md5.content

  depends_on = [
    archive_file.node_app_zip,
    local_file.node_app_zip_md5
  ]
}

resource "aws_s3_object" "node_app_start_script" {
  bucket = var.s3_bucket_name
  key    = "scripts/start-node-app.sh"
  source = "${path.module}/scripts/start-node-app.sh"
}

locals {
  node_app_s3_uri           = "s3://${var.s3_bucket_name}/${aws_s3_object.node_app_zip_upload.key}"
  node_app_start_script_uri = "s3://${var.s3_bucket_name}/${aws_s3_object.node_app_start_script.key}"
}

# IAM Role for EC2 instance
resource "aws_iam_role" "ec2_role" {
  name = local.server_name

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          Service = "ec2.amazonaws.com"
        }
        Action = "sts:AssumeRole"
      }
    ]
  })

  tags = var.tags
}

# IAM Policy for EC2 (Modify based on your needs)
resource "aws_iam_policy" "ec2_policy" {
  name        = "${local.server_name}-policy"
  description = "Permissions for QuickSight EC2 instance"

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Action = [
          "s3:ListBucket",
          "cloudwatch:PutMetricData",
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ],
        Resource = "*"
      },

      {
        "Effect" : "Allow",
        "Action" : [
          "dynamodb:PutItem",
          "dynamodb:GetItem",
          "dynamodb:UpdateItem",
          "dynamodb:DeleteItem",
          "dynamodb:Scan",
          "dynamodb:Query"
        ],
        "Resource" : var.dynamodb_table_arn
      },
      {
        "Effect" : "Allow",
        "Action" : "lambda:InvokeFunction",
        "Resource" : var.embed_url_generate_fn_arn
      },

      {
        "Effect" : "Allow",
        "Action" : [
          "s3:PutObject",
          "s3:GetObject"
        ],
        "Resource" : "${var.s3_bucket_arn}/*"
      },

      {
        "Effect" : "Allow",
        "Action" : "sns:Publish",
        "Resource" : var.error_sns_topic_arn
      },

      {
        "Sid" : "Statement1",
        "Effect" : "Allow",
        "Action" : [
          "sqs:ReceiveMessage",
          "sqs:DeleteMessage",
          "sqs:GetQueueAttributes"
        ],
        "Resource" : var.sqs_report_initialization_queue_arn
      },
      {
        "Sid" : "Statement2",
        "Effect" : "Allow",
        "Action" : [
          "sqs:SendMessage",
          "sqs:ReceiveMessage",
          "sqs:GetQueueAttributes"
        ],
        "Resource" : var.email_send_sqs_arn
      }
    ]
  })
}

# Attach policy to role
resource "aws_iam_role_policy_attachment" "ec2_role_policy_attach" {
  role       = aws_iam_role.ec2_role.name
  policy_arn = aws_iam_policy.ec2_policy.arn
}

# Create an instance profile to attach IAM role to EC2
resource "aws_iam_instance_profile" "ec2_instance_profile" {
  name = "${local.server_name}-ec2-instance-profile"
  role = aws_iam_role.ec2_role.name
}

resource "aws_key_pair" "imported_key" {
  key_name   = local.server_name
  public_key = file(var.ssh_public_key_full_path)
  tags       = var.tags
  lifecycle {
    ignore_changes = [public_key]
  }
}


resource "aws_security_group" "ec2_sg" {
  name        = "${local.server_name}_ec2_sg"
  description = "EC2 Access"

  ingress {
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = ["**********/32"] # Allow SSH from prod au vpn
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"] # Allow all outbound traffic
  }

  tags = var.tags
}

# Create a local file with the MD5 hash of the ec2_setup_script.sh
resource "local_file" "ec2_setup_script_md5" {
  filename = "${path.module}/scripts/ec2_setup_script.md5"
  content  = filemd5("${path.module}/scripts/ec2_setup_script.sh")
}

resource "aws_instance" "quicksight_ec2" {

  ami                    = var.ami_id
  instance_type          = var.instance_type
  key_name               = aws_key_pair.imported_key.key_name
  vpc_security_group_ids = [aws_security_group.ec2_sg.id]
  iam_instance_profile   = aws_iam_instance_profile.ec2_instance_profile.name

  root_block_device {
    encrypted   = var.root_block_device_encrypted
    volume_size = var.root_block_device_volume_size
    tags        = var.tags
  }

  user_data = templatefile("${path.module}/scripts/ec2_setup_script.sh", {
    node_app_s3_uri           = local.node_app_s3_uri
    node_app_start_script_uri = local.node_app_start_script_uri
  })

  lifecycle {
    replace_triggered_by = [
      local_file.ec2_setup_script_md5.id,
      local_file.node_app_zip_md5.id
    ]
  }

  tags = merge(
    var.tags,
    {
      Name = local.server_name
    }
  )

}
