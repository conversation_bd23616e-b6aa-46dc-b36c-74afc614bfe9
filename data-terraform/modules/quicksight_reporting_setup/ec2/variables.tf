
variable "tags" {
  description = "Additional tags to apply to resources"
  type        = map(string)
  default     = {}
}

variable "environment_code" {
  type        = string
  description = "The environment indicator. Options [dev, prod]"
  default     = "dev"
}

variable "region_name" {
  description = "AWS region name"
  type        = string
}

variable "server_name" {
  type = string
}

variable "ami_id" {
  description = "The AMI ID for the Ubuntu 22.04"
  type        = string
  default     = "ami-040e71e7b8391cae4"
}

variable "instance_type" {
  description = "The instance type for the EC2 instance"
  type        = string
  default     = "t3a.xlarge"
}

variable "root_block_device_encrypted" {
  type    = bool
  default = true
}

variable "root_block_device_volume_size" {
  type    = number
  default = 30
}

variable "ssh_public_key_full_path" {
  type        = string
  description = "Name of existing SSH public key file (e.g. `id_rsa.pub`)"
  default     = null
}

variable "email_send_sqs_url" {
  type    = string
  default = ""
}


variable "error_sns_topic_arn" {
  type    = string
  default = ""
}

variable "s3_bucket_name" {
  description = "S3 bucket to upload nodejs code and sh scripts"
  type        = string
  default     = "your-s3-bucket-name"
}

variable "s3_bucket_arn" {
  description = "S3 bucket to upload nodejs code and sh scripts"
  type        = string
}

variable "sqs_report_initialization_queue_url" {
  description = "Report initialization sqs url"
  type        = string
}

variable "dynamodb_table_name" {
  description = "The name of the DynamoDB table"
  type        = string
}

variable "dynamodb_table_arn" {
  description = "The arn of the DynamoDB table"
  type        = string
}


variable "email_send_sqs_arn" {
  description = "The arn of the sqs"
  type        = string
}

variable "sqs_report_initialization_queue_arn" {
  description = "The arn of the sqs"
  type        = string
}

variable "embed_url_generate_fn_arn" {
  description = "The arn of embed url generation fn"
  type        = string
}

variable "dashboard_sheet_id" {
  type = string
}

variable "client_name" {
  type = string
}
