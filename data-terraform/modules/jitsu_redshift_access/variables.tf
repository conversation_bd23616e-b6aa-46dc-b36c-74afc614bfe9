variable "s3_bucket_name" {
  description = "Name of the S3 bucket"
  type        = string
}

variable "iam_role_name" {
  description = "Name of the IAM role"
  type        = string
}

variable "tags" {
  description = "Additional tags for resources"
  type        = map(string)
  default     = {}
}

variable "workspace_id" {
  description = "Workspace ID for external ID condition"
  type        = string
}

variable "aws_region" {
  description = "AWS Region"
  type        = string
  default     = "us-east-1"
}

variable "aws_account_id" {
  description = "AWS Account ID"
  type        = string
}

variable "cluster_id" {
  description = "Redshift Cluster Identifier"
  type        = string
}

variable "db_user" {
  description = "Database User for Redshift access"
  type        = string
}

variable "db_name" {
  description = "Database Name for Redshift access"
  type        = string
}