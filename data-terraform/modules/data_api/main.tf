#######################################
# Lambda Permission for API Gateway
#######################################
resource "aws_lambda_permission" "api_gateway_permission" {
  statement_id  = "AllowAPIGatewayInvoke"
  action        = "lambda:InvokeFunction"
  function_name = var.api_handling_lambda_name
  principal     = "apigateway.amazonaws.com"
  source_arn    = "${aws_api_gateway_rest_api.data_api_endpoint.execution_arn}/*/*/*"
}

#######################################
# REST API Gateway Setup
#######################################
resource "aws_api_gateway_rest_api" "data_api_endpoint" {
  name        = "Data-API-Public"
  description = "API Gateway to Share data with customers"
  tags        = var.tags
}

resource "aws_api_gateway_resource" "proxy" {
  rest_api_id = aws_api_gateway_rest_api.data_api_endpoint.id
  parent_id   = aws_api_gateway_rest_api.data_api_endpoint.root_resource_id
  path_part   = "{proxy+}"
}

resource "aws_api_gateway_method" "any_method" {
  rest_api_id      = aws_api_gateway_rest_api.data_api_endpoint.id
  resource_id      = aws_api_gateway_resource.proxy.id
  http_method      = "ANY"
  authorization    = "NONE"
  api_key_required = true
}

resource "aws_api_gateway_integration" "lambda_integration" {
  rest_api_id             = aws_api_gateway_rest_api.data_api_endpoint.id
  resource_id             = aws_api_gateway_resource.proxy.id
  http_method             = aws_api_gateway_method.any_method.http_method
  integration_http_method = "POST"
  type                    = "AWS_PROXY"
  uri                     = "arn:aws:apigateway:${var.region_name}:lambda:path/2015-03-31/functions/${var.api_handling_lambda_arn}/invocations"
}

# Protect the root path `/` as well
resource "aws_api_gateway_method" "any_root_method" {
  rest_api_id      = aws_api_gateway_rest_api.data_api_endpoint.id
  resource_id      = aws_api_gateway_rest_api.data_api_endpoint.root_resource_id
  http_method      = "ANY"
  authorization    = "NONE"
  api_key_required = true
}

resource "aws_api_gateway_integration" "root_lambda_integration" {
  rest_api_id             = aws_api_gateway_rest_api.data_api_endpoint.id
  resource_id             = aws_api_gateway_rest_api.data_api_endpoint.root_resource_id
  http_method             = aws_api_gateway_method.any_root_method.http_method
  integration_http_method = "POST"
  type                    = "AWS_PROXY"
  uri                     = "arn:aws:apigateway:${var.region_name}:lambda:path/2015-03-31/functions/${var.api_handling_lambda_arn}/invocations"
}

resource "aws_api_gateway_method_response" "cors_response" {
  rest_api_id = aws_api_gateway_rest_api.data_api_endpoint.id
  resource_id = aws_api_gateway_resource.proxy.id
  http_method = aws_api_gateway_method.any_method.http_method
  status_code = "200"

  response_models = {
    "application/json" = "Empty"
  }

  response_parameters = {
    "method.response.header.Access-Control-Allow-Origin"  = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Headers" = true
  }
}

resource "aws_api_gateway_integration_response" "cors_integration_response" {
  rest_api_id = aws_api_gateway_rest_api.data_api_endpoint.id
  resource_id = aws_api_gateway_resource.proxy.id
  http_method = aws_api_gateway_method.any_method.http_method
  status_code = aws_api_gateway_method_response.cors_response.status_code

  response_parameters = {
    "method.response.header.Access-Control-Allow-Origin"  = "'*'"
    "method.response.header.Access-Control-Allow-Methods" = "'*'"
    "method.response.header.Access-Control-Allow-Headers" = "'*'"
  }

  depends_on = [aws_api_gateway_integration.lambda_integration]
}

resource "aws_api_gateway_deployment" "deployment" {
  rest_api_id = aws_api_gateway_rest_api.data_api_endpoint.id

  triggers = {
    redeploy = sha1(jsonencode([
      aws_api_gateway_method.any_method.id,
      aws_api_gateway_method.any_root_method.id,
      aws_api_gateway_integration.lambda_integration.id,
      aws_api_gateway_integration.root_lambda_integration.id
    ]))
  }

  lifecycle {
    create_before_destroy = true # the stage is switched automatically
  }

  depends_on = [
    aws_api_gateway_integration.lambda_integration,
    aws_api_gateway_method_response.cors_response
  ]
}

resource "aws_api_gateway_stage" "prod" {
  stage_name    = "prod"
  rest_api_id   = aws_api_gateway_rest_api.data_api_endpoint.id
  deployment_id = aws_api_gateway_deployment.deployment.id
  tags          = var.tags
}

#######################################
# CLIENT ELJ
#######################################

resource "aws_api_gateway_usage_plan" "client_elj_plan" {
  name        = "client-elj-usage-plan"
  description = "Usage plan for Client ELJ"

  api_stages {
    api_id = aws_api_gateway_rest_api.data_api_endpoint.id
    stage  = aws_api_gateway_stage.prod.stage_name
  }

  throttle_settings {
    burst_limit = 10
    rate_limit  = 5
  }

  quota_settings {
    limit  = 10000
    period = "DAY"
  }

  tags = var.tags
}

resource "aws_api_gateway_api_key" "client_elj_key" {
  name        = "client-elj-api-key"
  description = "API key for Client ELJ"
  enabled     = true
  tags        = var.tags
}

resource "aws_api_gateway_usage_plan_key" "client_elj_usage_key" {
  key_id        = aws_api_gateway_api_key.client_elj_key.id
  key_type      = "API_KEY"
  usage_plan_id = aws_api_gateway_usage_plan.client_elj_plan.id
}

#######################################
# CLIENT Internal
#######################################

resource "aws_api_gateway_usage_plan" "client_internal_plan" {
  name        = "client-internal-usage-plan"
  description = "Usage plan for Client Internal"

  api_stages {
    api_id = aws_api_gateway_rest_api.data_api_endpoint.id
    stage  = aws_api_gateway_stage.prod.stage_name
  }

  throttle_settings {
    burst_limit = 20
    rate_limit  = 10
  }

  quota_settings {
    limit  = 20000
    period = "DAY"
  }

  tags = var.tags
}

resource "aws_api_gateway_api_key" "client_internal_key" {
  name        = "client-internal-api-key"
  description = "API key for Client Internal"
  enabled     = true
  tags        = var.tags
}

resource "aws_api_gateway_usage_plan_key" "client_internal_usage_key" {
  key_id        = aws_api_gateway_api_key.client_internal_key.id
  key_type      = "API_KEY"
  usage_plan_id = aws_api_gateway_usage_plan.client_internal_plan.id
}


# --------------------------------------------
# SECURITY GROUP FOR VPC ENDPOINT
# --------------------------------------------
resource "aws_security_group" "api_endpoint_sg" {
  name        = "data-api-gw-vpc-endpoint-sg"
  description = "Allow HTTPS to Data API Gateway endpoint"
  vpc_id      = var.vpc_id

  ingress {
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = [var.vpc_cidr]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = var.tags
}

# --------------------------------------------
# KEEP LAMBDA WARM
# --------------------------------------------

data "aws_lambda_function" "target_lambda" {
  function_name = var.warm_up_lambda_function_name
}
resource "aws_cloudwatch_event_rule" "keep_lambda_warm" {
  name                = "keep-data-api-lambda-warm-every-minute"
  description         = "Keep Lambda warm every 1 minute, 24/7"
  schedule_expression = "rate(1 minute)"
}

resource "aws_cloudwatch_event_target" "lambda_target" {
  rule      = aws_cloudwatch_event_rule.keep_lambda_warm.name
  target_id = "lambdaWarmup"
  arn       = data.aws_lambda_function.target_lambda.arn

  input = jsonencode({
    endpoint_name = "ping",
    payload       = {}
  })
}

resource "aws_lambda_permission" "allow_eventbridge" {
  statement_id  = "AllowExecutionFromEventBridge"
  action        = "lambda:InvokeFunction"
  function_name = var.warm_up_lambda_function_name
  principal     = "events.amazonaws.com"
  source_arn    = aws_cloudwatch_event_rule.keep_lambda_warm.arn
}
