# Data API Deployment

This Terraform module provisions the **API Gateway** required to expose the Data API to consumers.

## 📦 Repository Structure

- **Application Code**: The FastAPI-based application is located in the [`data-api-fastapi-implementation`](https://bitbucket.org/fingermarkltd/data-api-fastapi-implementation/src/main/) repository.
- **Deployment**: The application is deployed to an AWS Lambda function named **`data-api-fastapi-endpoint`** automatically when changes are pushed to the `main` branch.
- **Infrastructure**: This repository contains the Terraform code to configure and deploy the **API Gateway** that acts as the frontend for the Lambda-based API.

## 🔧 Components Created by Terraform

- API Gateway (REST API)
- Lambda Proxy Integration
- CORS Configuration
- ANY Method on `/{proxy+}` path
- Deployment Stage (`prod`)

## 🚀 Deployment Flow

1. Application changes are pushed to the `main` branch.
2. CI/CD pipeline deploys the FastAPI application to the `data-api-fastapi-endpoint` Lambda.
3. API Gateway forwards incoming requests to the Lambda via a proxy integration.
