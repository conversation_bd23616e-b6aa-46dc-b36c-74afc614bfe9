variable "environment_code" {
  type        = string
  description = "The environment indicator. Options [dev, prod]"
  default     = "dev"
}

variable "tags" {
  type        = any
  description = "a group of tags to tag resources"
  default = {
    Terraform = "true"
    Stack     = "Data"
    Author    = "Data Team"
    Backup    = "false"
  }
}

variable "api_handling_lambda_name" {
  description = "The name of the Lambda function to integrate with API Gateway"
  type        = string
}

variable "api_handling_lambda_arn" {
  description = "The ARN of the Lambda function to integrate with API Gateway"
  type        = string
}

variable "region_name" {
  type = string
}

variable "aws_account_id" {
  type = number
}

variable "vpc_id" {
  type = string
}

variable "vpc_cidr" {
  type = string
}

variable "private_subnet_ids" {
  type = list(string)
}

variable "warm_up_lambda_function_name" {
  type        = string
  description = "The name of the existing Lambda function to keep warm"
  default     = "data-api-lambda-for-internal-application-calls"
}
