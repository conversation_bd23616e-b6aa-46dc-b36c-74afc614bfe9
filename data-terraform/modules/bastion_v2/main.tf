locals {
  bastion_name = "${var.server_name}-${var.environment_code}-${data.aws_region.current.name}"
}

resource "aws_iam_instance_profile" "bastion_instance_profile" {
  name = local.bastion_name
  role = aws_iam_role.bastion_role.name
  tags = var.tags
}

resource "aws_iam_role" "bastion_role" {
  name               = local.bastion_name
  path               = "/"
  tags               = var.tags
  assume_role_policy = data.aws_iam_policy_document.default.json
}

resource "aws_iam_role_policy" "main" {
  name   = local.bastion_name
  role   = aws_iam_role.bastion_role.id
  policy = data.aws_iam_policy_document.main.json
}

resource "aws_key_pair" "imported_key" {
  key_name   = local.bastion_name
  public_key = file(var.ssh_public_key_full_path)
  tags       = var.tags
  lifecycle {
    ignore_changes = [public_key]
  }
}

resource "aws_security_group" "bastion_sg" {
  name        = "${local.bastion_name}-securityGroup"
  description = "Bastion security group for ssh tunnels to private resources"
  vpc_id      = var.vpc_id

  ingress = [
    {
      description      = "Allow incoming connections from trusted locations"
      from_port        = 22
      to_port          = 22
      protocol         = "TCP"
      self             = true
      cidr_blocks      = var.allowed_cidr_blocks
      prefix_list_ids  = null
      ipv6_cidr_blocks = null
      security_groups  = null
    }
  ]

  egress = [
    {
      description      = "Allow all outgoing connections "
      self             = null
      from_port        = 0
      to_port          = 0
      protocol         = "-1"
      prefix_list_ids  = null
      ipv6_cidr_blocks = null
      security_groups  = null
      cidr_blocks      = ["0.0.0.0/0"]
    }
  ]

  tags = merge(var.tags, { Name = "${local.bastion_name}-securityGroup" })
  lifecycle {
    ignore_changes = [ingress]
  }
}

resource "aws_instance" "default" {
  ami                         = var.ami_id != null ? var.ami_id : data.aws_ami.default.id
  instance_type               = var.instance_type
  vpc_security_group_ids      = [aws_security_group.bastion_sg.id]
  iam_instance_profile        = aws_iam_instance_profile.bastion_instance_profile.name
  key_name                    = aws_key_pair.imported_key.key_name
  subnet_id                   = var.subnets[0]
  user_data_replace_on_change = true

  user_data = (templatefile("${path.module}/user_data.sh", {
    aws_region              = var.AWS_REGION
    bucket_name             = var.bucket_name
    extra_user_data_content = var.extra_user_data_content
    allow_ssh_commands      = lower(var.allow_ssh_commands)
    public_ssh_port         = var.public_ssh_port
    sync_logs_cron_job      = var.enable_logs_s3_sync ? "*/5 * * * * /usr/bin/bastion/sync_s3" : ""
  }))
  root_block_device {
    encrypted   = var.root_block_device_encrypted
    volume_size = var.root_block_device_volume_size
    tags        = var.tags
  }

  tags       = merge(var.tags, { Name = local.bastion_name })
  depends_on = [aws_iam_instance_profile.bastion_instance_profile, aws_security_group.bastion_sg]
}

resource "aws_eip" "default" {
  instance = aws_instance.default.id
  domain   = "vpc"
  tags     = merge(var.tags, { Name = local.bastion_name })

  depends_on = [aws_instance.default]
}