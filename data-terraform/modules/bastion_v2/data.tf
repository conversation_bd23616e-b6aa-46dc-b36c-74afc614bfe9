data "aws_region" "current" {}

data "aws_ami" "default" {
  most_recent = "true"

  dynamic "filter" {
    for_each = var.ami_filter
    content {
      name   = filter.key
      values = filter.value
    }
  }

  owners = var.ami_owners
}

data "aws_iam_policy_document" "default" {

  statement {
    sid = ""

    actions = [
      "sts:AssumeRole",
    ]

    principals {
      type        = "Service"
      identifiers = ["ec2.amazonaws.com"]
    }

    effect = "Allow"
  }
}

data "aws_iam_policy_document" "main" {
  statement {
    effect = "Allow"

    actions = [
      "ssm:DescribeAssociation",
      "ssm:GetDocument",
      "ssm:DescribeDocument",
      "ssm:GetParameter",
      "ssm:GetParameters",
      "ssm:ListInstanceAssociations",
      "ssm:PutComplianceItems",
      "ssm:UpdateAssociationStatus",
      "ssm:UpdateInstanceAssociationStatus",
      "ssm:UpdateInstanceInformation"
    ]

    resources = ["arn:aws:ssm:*:${var.ACCOUNT_ID}:parameter/fingermark/*"]
  }

  statement {
    effect = "Allow"

    actions = [
      "ssm:PutInventory",
      "ssm:GetManifest",
      "ssm:ListAssociations",
      "ssm:GetDeployablePatchSnapshotForInstance",
      "ssm:PutConfigurePackageResult"
    ]

    resources = ["*"]
  }
  statement {
    effect = "Allow"

    actions = [
      "ssmmessages:CreateControlChannel",
      "ssmmessages:CreateDataChannel",
      "ssmmessages:OpenControlChannel",
      "ssmmessages:OpenDataChannel"
    ]

    resources = ["*"]
  }

  statement {
    effect = "Allow"

    actions = [
      "ec2messages:AcknowledgeMessage",
      "ec2messages:DeleteMessage",
      "ec2messages:FailMessage",
      "ec2messages:GetEndpoint",
      "ec2messages:GetMessages",
      "ec2messages:SendReply"
    ]

    resources = ["*"]
  }
  statement {
    actions = [
      "s3:PutObject",
      "s3:PutObjectAcl"
    ]
    resources = ["${aws_s3_bucket.public_key_storage.arn}/logs/*"]
  }

  statement {
    actions = [
      "s3:GetObject"
    ]
    resources = ["${aws_s3_bucket.public_key_storage.arn}/user_public_keys/*", "${aws_s3_bucket.public_key_storage.arn}/admin_public_keys/*"]
  }

  statement {
    actions = [
      "s3:ListBucket"
    ]
    resources = [
    aws_s3_bucket.public_key_storage.arn]

    condition {
      test     = "ForAnyValue:StringEquals"
      values   = ["admin_public_keys/", "user_public_keys/"]
      variable = "s3:prefix"
    }
  }


}