

resource "aws_s3_bucket" "public_key_storage" {
  bucket        = var.bucket_name
  force_destroy = var.bucket_force_destroy
  tags          = var.tags
}


resource "aws_s3_bucket_acl" "public_key_storage" {
  bucket = aws_s3_bucket.public_key_storage.id
  acl    = "private"
}

resource "aws_s3_bucket_versioning" "public_key_storage" {
  bucket = aws_s3_bucket.public_key_storage.id

  versioning_configuration {
    status = var.bucket_versioning ? "Enabled" : "Suspended"
  }
}

resource "aws_s3_bucket_lifecycle_configuration" "public_key_storage" {
  bucket = aws_s3_bucket.public_key_storage.id

  rule {
    id     = "log"
    status = var.enable_logs_s3_sync && var.log_auto_clean ? "Enabled" : "Disabled"

    filter {
      prefix = "logs/"
    }

    transition {
      days          = var.log_standard_ia_days
      storage_class = "STANDARD_IA"
    }

    transition {
      days          = var.log_glacier_days
      storage_class = "GLACIER"
    }

    expiration {
      days = var.log_expiry_days
    }
  }
}

resource "aws_s3_object" "user_public_keys_to_s3" {
  for_each = fileset("${var.users_path}", "**/*")
  bucket   = aws_s3_bucket.public_key_storage.id
  key      = "user_public_keys/${each.value}"
  source   = "${var.users_path}/${each.value}"
  etag     = filemd5("${var.users_path}/${each.value}")
}

resource "aws_s3_object" "admin_public_keys_to_s3" {
  for_each = fileset("${var.admin_users_path}", "**/*")
  bucket   = aws_s3_bucket.public_key_storage.id
  key      = "admin_public_keys/${each.value}"
  source   = "${var.admin_users_path}/${each.value}"
  etag     = filemd5("${var.admin_users_path}/${each.value}")
}
