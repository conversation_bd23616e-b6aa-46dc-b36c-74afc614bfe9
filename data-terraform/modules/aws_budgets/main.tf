resource "aws_budgets_budget" "monthly_cost" {
  name         = var.aws_budgets_budget_name
  budget_type  = var.aws_budgets_budget_type
  limit_amount = var.aws_budgets_limit_amount
  limit_unit   = var.aws_budgets_limit_unit
  time_unit    = var.aws_budgets_time_unit

  notification {
    comparison_operator = "GREATER_THAN"
    threshold           = var.aws_budgets_limit_amount
    threshold_type      = "ABSOLUTE_VALUE"
    notification_type   = "ACTUAL"

    subscriber_email_addresses = var.aws_budgets_notification_email_list
    # subscriber_sns_topic_arns = ["arn:aws:sns:ap-southeast-2:055313672806:SlackAlertsChannel"]
  }
}
