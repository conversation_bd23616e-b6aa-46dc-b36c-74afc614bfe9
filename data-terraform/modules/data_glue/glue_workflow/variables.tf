variable "workflow_name" {
  type        = string
  description = "Glue workflow name."
  default     = null
}

variable "workflow_description" {
  type        = string
  description = "Glue workflow description."
  default     = null
}

variable "default_run_properties" {
  type        = map(string)
  description = "A map of default run properties for this workflow. These properties are passed to all jobs associated to the workflow."
  default     = null
}

variable "max_concurrent_runs" {
  type        = number
  description = "Maximum number of concurrent runs. If you leave this parameter blank, there is no limit to the number of concurrent workflow runs."
  default     = null
}

variable "tags" {
  type        = any
  description = "a group of tags to tag resources"
  default = {
    Terraform   = "true"
    Stack       = "Data-Glue"
    Author      = "Data-Team"
    Backup      = "false"
    Application = "Data-Infrastructure"
    Environment = "dev"
  }
}
