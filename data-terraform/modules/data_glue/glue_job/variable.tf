variable "aws_region" {
  type        = string
  description = "The AWS region where the resources will be provisioned"
  default     = "current"
}

variable "tags" {
  type = any
  default = {
    Terraform   = "true"
    Stack       = "Data-VPC"
    Author      = "Data-Team"
    Application = "Data-Infrastructure"
  }
}

variable "environment" {
  type        = string
  description = "The environment name for the resources"
  default     = ""
}

variable "script_file" {
  type        = string
  description = "The path to the script file"
  default     = ""
}

variable "glue_bucket_id" {
  type        = string
  description = "The ID of the Glue bucket"
  default     = ""
}

variable "glue_s3_folder" {
  type        = string
  description = "The folder path in the Glue S3 bucket for scripts"
  default     = "glue/scripts/"
}

variable "glue_job_name" {
  type        = string
  description = "The name of the Glue job"
  default     = ""
}

variable "glue_etl_type" {
  type        = string
  default     = "pythonshell"
  description = "The type of Glue ETL job. Available values are glueetl, pythonshell, gluestreaming."
}

variable "max_capacity" {
  type        = number
  default     = 0.0625
  description = "The maximum capacity for the Glue job"
}

variable "custom_default_arguments" {
  description = "Custom default arguments for the Glue job"
  type        = map(string)
  default     = {}
}

variable "glue_connections" {
  description = "list connection"
  type        = any
  default     = []
}

