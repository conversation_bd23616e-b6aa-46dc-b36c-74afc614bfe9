data "aws_iam_policy_document" "instance_assume_role_policy" {
  statement {
    effect  = "Allow"
    actions = ["sts:AssumeRole"]

    principals {
      type        = "Service"
      identifiers = ["glue.amazonaws.com"]
    }
  }
}

resource "aws_iam_role" "glue_iam_role" {
  name               = "AWSGlueService_${var.glue_job_name}_${var.environment}_role"
  assume_role_policy = data.aws_iam_policy_document.instance_assume_role_policy.json
}

resource "aws_iam_role_policy_attachment" "glue_iam_role_policy_attachment" {
  role       = aws_iam_role.glue_iam_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSGlueServiceRole"
}

#upload script to the glue resource s3 bucket 

resource "aws_s3_object" "glue_script_file" {
  bucket = var.glue_bucket_id
  source = var.script_file
  key    = var.script_file
  etag   = filemd5("${var.script_file}")
}


resource "aws_glue_job" "fm_glue_job" {
  name         = var.glue_job_name
  role_arn     = aws_iam_role.glue_iam_role.arn
  max_capacity = var.max_capacity
  tags         = var.tags

  connections = var.glue_connections

  command {
    name            = var.glue_etl_type
    python_version  = 3.9
    script_location = "s3://${aws_s3_object.glue_script_file.bucket}/${aws_s3_object.glue_script_file.key}"
  }
  default_arguments = merge(var.custom_default_arguments, {
    "--pip-install" = "awswrangler"
  })

}

