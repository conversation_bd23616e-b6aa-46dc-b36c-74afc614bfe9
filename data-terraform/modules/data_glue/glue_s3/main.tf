resource "aws_s3_bucket" "glue_script_s3_bucket" {
  bucket        = "aws-glue-fm-assets-${var.app_stage}-${data.aws_region.current.name}"
  force_destroy = var.force_destroy
  tags          = merge(var.tags, { environment = var.environment })
}

resource "aws_s3_bucket_versioning" "glue_script_s3_bucket_versioning" {
  bucket = aws_s3_bucket.glue_script_s3_bucket.id
  versioning_configuration {
    status = var.bucket_versioning
  }
}

resource "aws_s3_bucket_ownership_controls" "glue_script_s3_bucket_ownership_controls" {
  bucket = aws_s3_bucket.glue_script_s3_bucket.id
  rule {
    object_ownership = var.bucket_ownership
  }
}

resource "aws_s3_bucket_acl" "glue_script_s3_bucket_acl" {
  depends_on = [aws_s3_bucket_ownership_controls.glue_script_s3_bucket_ownership_controls]

  bucket = aws_s3_bucket.glue_script_s3_bucket.id
  acl    = var.bucket_acl
}

resource "aws_s3_object" "glue_script_s3_bucket_folders" {
  count  = length(var.bucket_folders)
  bucket = aws_s3_bucket.glue_script_s3_bucket.id
  key    = var.bucket_folders[count.index]
  source = "/dev/null"
}