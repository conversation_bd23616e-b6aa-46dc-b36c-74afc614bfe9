data "aws_region" "current" {}

variable "tags" {
  type        = any
  description = "a group of tags to tag resources"
  default = {
    Terraform   = "true"
    Stack       = "Data-VPC"
    Author      = "Data-Team"
    Application = "Data-Infrastructure"
  }
}

variable "environment" {
  type    = string
  default = ""
}

variable "app_stage" {
  type    = string
  default = ""
}

variable "bucket_ownership" {
  type    = string
  default = "BucketOwnerPreferred"
}

variable "bucket_acl" {
  type    = string
  default = "private"
}

variable "bucket_versioning" {
  type    = string
  default = "Enabled"
}

variable "bucket_folders" {
  type        = any
  description = "List of folders to be created"
  default     = ["raw/"]
}

variable "force_destroy" {
  type    = bool
  default = false
}