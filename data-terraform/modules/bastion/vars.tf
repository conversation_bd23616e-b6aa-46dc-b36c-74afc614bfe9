variable "environment_code" {
  type        = string
  description = "The environment indicator. Options [dev, prod]"
  default     = "dev"
}

variable "tags" {
  type        = any
  description = "a group of tags to tag resources"
  default     = {}
}

variable "zone_id" {
  type        = string
  default     = "bastion"
  description = "Route53 DNS Zone ID"
}

variable "instance_type" {
  type        = string
  default     = "t2.micro"
  description = "Bastion instance type"
}

variable "vpc_id" {
  type        = string
  description = "VPC ID"
}

variable "subnets" {
  type        = list(string)
  description = "AWS subnet IDs"
}

variable "ssh_public_key_full_path" {
  type        = string
  description = "Name of existing SSH public key file (e.g. `id_rsa.pub`)"
  default     = null
}

variable "root_block_device_encrypted" {
  type        = bool
  default     = true
  description = "Whether to encrypt the root block device"
}

variable "root_block_device_volume_size" {
  type        = number
  default     = 8
  description = "The volume size (in GiB) to provision for the root block device. It cannot be smaller than the AMI it refers to."
}

variable "host_name" {
  type        = string
  default     = "bastion"
  description = "The Bastion hostname created in Route53"
}

variable "user_data_template" {
  type        = string
  default     = "user_data/amazon-linux.sh"
  description = "User Data template to use for provisioning EC2 Bastion Host"
}

variable "ami_filter" {
  description = "List of maps used to create the AMI filter for the action runner AMI."
  type        = map(list(string))

  default = {
    name = ["amzn2-ami-hvm-2.*-x86_64-ebs"]
  }
}

variable "ami_owners" {
  description = "The list of owners used to select the AMI of action runner instances."
  type        = list(string)
  default     = ["amazon"]
}

variable "security_groups" {
  type        = list(string)
  description = "A list of Security Group IDs to associate with bastion host."
  default     = []
}

variable "allowed_cidr_blocks" {
  type        = list(string)
  description = "A list of trusted cidr blocks as a source to connect to Bastion."
}

variable "instance_profile" {
  type        = string
  description = "A pre-defined profile to attach to the instance (default is to build our own)"
  default     = ""
}

variable "ami_id" {
  type        = string
  description = "Pass AMI id to avoid updating bastion server"
  default     = null
}


variable "users_path" {
  type        = string
  description = "A path to read all users [filename => user, file content => user's public key]"
  default     = null
}