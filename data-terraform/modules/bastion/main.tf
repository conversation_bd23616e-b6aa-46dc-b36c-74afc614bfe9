locals {
  bastion_name = "bastion-${var.environment_code}-${data.aws_region.current.name}"
  users_list   = fileset(var.users_path, "*")
  users_keys   = { for user in local.users_list : user => file("${var.users_path}/${user}") }
  ec2_user_data = templatefile(var.user_data_template, {
    ec2_users = local.users_keys
  })
}

resource "aws_iam_instance_profile" "bastion_instance_profile" {
  name = local.bastion_name
  role = aws_iam_role.bastion_role.name
  tags = var.tags
}

resource "aws_iam_role" "bastion_role" {
  name               = local.bastion_name
  path               = "/"
  tags               = var.tags
  assume_role_policy = data.aws_iam_policy_document.default.json
}

resource "aws_iam_role_policy" "main" {
  name   = local.bastion_name
  role   = aws_iam_role.bastion_role.id
  policy = data.aws_iam_policy_document.main.json
}

resource "aws_key_pair" "imported_key" {
  key_name   = local.bastion_name
  public_key = file(var.ssh_public_key_full_path)
  tags       = var.tags
  lifecycle {
    ignore_changes = [public_key]
  }
}

resource "aws_security_group" "bastion_sg" {
  name        = "${local.bastion_name}-securityGroup"
  description = "Bastion security group for ssh tunnels to private resources"
  vpc_id      = var.vpc_id

  ingress = [
    {
      description      = "Allow incoming connections from trusted locations"
      from_port        = 22
      to_port          = 22
      protocol         = "TCP"
      self             = true
      cidr_blocks      = var.allowed_cidr_blocks
      prefix_list_ids  = null
      ipv6_cidr_blocks = null
      security_groups  = null
    }
  ]

  egress = [
    {
      description      = "Allow all outgoing connections "
      self             = null
      from_port        = 0
      to_port          = 0
      protocol         = "-1"
      prefix_list_ids  = null
      ipv6_cidr_blocks = null
      security_groups  = null
      cidr_blocks      = ["0.0.0.0/0"]
    }
  ]

  tags = merge(var.tags, { Name = "${local.bastion_name}-securityGroup" })
  lifecycle {
    ignore_changes = [ingress]
  }
}

resource "aws_instance" "default" {
  ami                         = var.ami_id != null ? var.ami_id : data.aws_ami.default.id
  instance_type               = var.instance_type
  user_data                   = local.ec2_user_data
  vpc_security_group_ids      = [aws_security_group.bastion_sg.id]
  iam_instance_profile        = aws_iam_instance_profile.bastion_instance_profile.name
  key_name                    = aws_key_pair.imported_key.key_name
  subnet_id                   = var.subnets[0]
  user_data_replace_on_change = true
  root_block_device {
    encrypted   = var.root_block_device_encrypted
    volume_size = var.root_block_device_volume_size
    tags        = var.tags
  }

  tags = merge(var.tags, { Name = local.bastion_name })
}
resource "aws_eip" "default" {
  instance = aws_instance.default.id
  domain   = "vpc"
  tags     = merge(var.tags, { Name = local.bastion_name })
}