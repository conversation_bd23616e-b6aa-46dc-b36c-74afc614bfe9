# Create Master user password and save as secret
resource "aws_secretsmanager_secret" "rds_credentials" {
  name = "${var.rds_identifier}-${var.environment_code}-credential"
  tags = var.tags
  lifecycle {
    prevent_destroy = true
  }
}

resource "random_password" "rds_password" {
  length           = 14
  numeric          = true
  special          = true
  min_numeric      = 1
  override_special = "!#$%&*<>:"
}

resource "aws_secretsmanager_secret_version" "rds_credentials_version" {
  secret_id = aws_secretsmanager_secret.rds_credentials.id
  secret_string = jsonencode({
    username = var.rds_master_username
    password = random_password.rds_password.result
    host     = aws_db_instance.airflow_postgres.address
    port     = aws_db_instance.airflow_postgres.port
    dbname   = var.rds_database_name
  })

  lifecycle {
    ignore_changes = [secret_string]
  }
}

# Create RDS PostgreSQL instance
resource "aws_db_instance" "airflow_postgres" {
  allocated_storage       = 20
  storage_type            = "gp2"
  engine                  = "postgres"
  engine_version          = var.db_engine_version
  instance_class          = var.db_instance_class
  identifier              = "${var.rds_identifier}-${var.environment_code}"
  db_name                 = var.rds_database_name
  username                = var.rds_master_username
  password                = random_password.rds_password.result
  publicly_accessible     = false
  skip_final_snapshot     = true
  db_subnet_group_name    = aws_db_subnet_group.rds_subnet_group.name
  vpc_security_group_ids  = [aws_security_group.rds_sg.id]
  backup_retention_period = 7
  backup_window           = "02:00-03:00"
  multi_az                = var.multi_az
  deletion_protection     = true


  tags = var.tags
}

# DB Subnet group for RDS
resource "aws_db_subnet_group" "rds_subnet_group" {
  name       = "${var.rds_identifier}-${var.environment_code}-subnet-group"
  subnet_ids = var.subnet_ids

  tags = var.tags
}

# Security Group for RDS
resource "aws_security_group" "rds_sg" {
  name        = "${var.rds_identifier}-${var.environment_code}-securityGroup"
  description = "RDS security group for Airflow PostgreSQL"
  vpc_id      = var.vpc_id

  ingress {
    description = "Allow incoming connections from Airflow EC2"
    from_port   = 5432
    to_port     = 5432
    protocol    = "tcp"
    cidr_blocks = [var.vpc_cidr]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = merge(var.tags, { Name = "${var.rds_identifier}-${var.environment_code}-rds-sg" })
}

## S3 Bucket
locals {
  environment_tag = {
    "Environment" = var.APP_STAGE
  }


  all_tags = merge(var.tags, var.additional_tags, local.environment_tag)

  airflow_s3_name = "fm-airflow-${lower(var.APP_STAGE)}-${lower(var.REGION)}"
}

#log bucket

resource "aws_s3_bucket" "log_bucket" {
  bucket = "fm-airflow-access-logs-${lower(var.APP_STAGE)}-${lower(var.REGION)}"
}

resource "aws_s3_bucket_ownership_controls" "log_bucket" {
  bucket = aws_s3_bucket.log_bucket.id
  rule {
    object_ownership = "BucketOwnerPreferred"
  }
}

resource "aws_s3_bucket_public_access_block" "log_bucket" {
  bucket                  = aws_s3_bucket.log_bucket.id
  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

resource "aws_s3_bucket_acl" "log_bucket_acl" {
  depends_on = [aws_s3_bucket_ownership_controls.log_bucket]
  bucket     = aws_s3_bucket.log_bucket.id
  acl        = "log-delivery-write"
}
resource "aws_s3_bucket_versioning" "log_bucket" {
  depends_on = [aws_s3_bucket_ownership_controls.log_bucket]
  bucket     = aws_s3_bucket.log_bucket.id
  versioning_configuration {
    status = "Enabled"
  }
}


resource "aws_s3_bucket" "airflow_bucket" {
  bucket        = local.airflow_s3_name
  force_destroy = false
  tags          = local.all_tags

}
resource "aws_s3_bucket_versioning" "airflow_bucket" {
  depends_on = [aws_s3_bucket_ownership_controls.airflow_bucket]
  bucket     = aws_s3_bucket.airflow_bucket.id
  versioning_configuration {
    status = "Enabled"
  }
}
resource "aws_s3_bucket_ownership_controls" "airflow_bucket" {
  bucket = aws_s3_bucket.airflow_bucket.id
  rule {
    object_ownership = "BucketOwnerPreferred"
  }
}

resource "aws_s3_bucket_acl" "airflow_bucket" {
  depends_on = [aws_s3_bucket_ownership_controls.airflow_bucket]

  bucket = aws_s3_bucket.airflow_bucket.id
  acl    = "private"
}

resource "aws_s3_bucket_policy" "log_bucket_policy" {
  bucket = aws_s3_bucket.log_bucket.id
  policy = <<EOF
  {
    "Version": "2012-10-17",
    "Statement": [
      {
        "Effect": "Allow",
        "Principal": {
          "Service": "logging.s3.amazonaws.com"
        },
        "Action": "s3:PutObject",
        "Resource": "arn:aws:s3:::fm-airflow-access-logs-${var.APP_STAGE}-${var.REGION}/s3_log/*",
        "Condition": {
          "StringEquals": {
            "s3:x-amz-acl": "bucket-owner-full-control"
          }
        }
      }
    ]
  }
  EOF
}

resource "aws_s3_bucket_public_access_block" "airflow_bucket" {
  bucket                  = aws_s3_bucket.airflow_bucket.id
  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

resource "aws_s3_bucket_logging" "airflow_bucket" {
  bucket = aws_s3_bucket.airflow_bucket.id

  target_bucket = aws_s3_bucket.log_bucket.id
  target_prefix = "log/"
  target_object_key_format {
    partitioned_prefix {
      partition_date_source = "DeliveryTime"
    }
  }
}

resource "aws_iam_policy" "airflow-bucket-s3-read-policy" {
  name        = "${local.airflow_s3_name}-read"
  description = "Provides read access only from Supersonic Data Lake [${local.airflow_s3_name}]"
  policy      = data.aws_iam_policy_document.datalake-s3-read-policy.json
  tags        = merge(var.tags, { Stack = "Data-DataLake-${local.airflow_s3_name}" })
}

resource "aws_iam_policy" "airflow-bucket-s3-read-write-delete-policy" {
  name        = "${local.airflow_s3_name}-read-write-delete"
  description = "Provides read, write and delete access to Data Lake [${local.airflow_s3_name}]"
  policy      = data.aws_iam_policy_document.airflow-s3-read-write-delete-policy.json
  tags        = merge(var.tags, { Stack = "Data-DataLake-${local.airflow_s3_name}" })
}

