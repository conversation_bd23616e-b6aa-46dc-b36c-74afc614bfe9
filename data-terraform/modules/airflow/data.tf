data "aws_region" "current" {}



data "aws_iam_policy_document" "airflow-s3-read-write-delete-policy" {
  statement {
    actions = [
      "s3:Get*",
      "s3:List*",
      "s3:PutObject",
      "s3:PutObjectTagging",
      "s3:DeleteObject",
    ]

    resources = concat(
      [
        "arn:aws:s3:::${local.airflow_s3_name}",
        "arn:aws:s3:::${local.airflow_s3_name}/*"
      ],
      var.additional_s3_arn_list
    )

    effect = "Allow"
  }
}

data "aws_iam_policy_document" "datalake-s3-read-policy" {
  statement {
    actions = [
      "s3:Get*",
      "s3:List*",
    ]

    resources = [
      "arn:aws:s3:::${local.airflow_s3_name}",
      "arn:aws:s3:::${local.airflow_s3_name}/*",
    ]

    effect = "Allow"
  }
}