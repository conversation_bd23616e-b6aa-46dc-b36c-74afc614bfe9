variable "rds_identifier" {
  description = "Identifier name for the RDS instance"
  type        = string
  default     = "airflowdb"
}

variable "rds_master_username" {
  description = "Master username for the RDS database"
  default     = "airflowadmin"
}

variable "rds_database_name" {
  description = "Database name inside RDS"
  type        = string
  default     = "airflow"
}

variable "vpc_id" {
  description = "VPC ID where RDS and EC2 are created"
  type        = string
}

variable "subnet_ids" {
  description = "List of Subnet IDs for RDS subnet group"
  type        = list(string)
}

variable "vpc_cidr" {
  description = "CIDR block of your VPC (for allowing inbound traffic)"
}

variable "environment_code" {
  description = "Environment short code (e.g., prod, dev, staging)"
}

variable "tags" {
  description = "Default tags to apply"
  type        = map(string)
  default = {
    Terraform = "true"
    Stack     = "Data"
    Author    = "Data Team"
    Backup    = "True"
  }
}

variable "db_instance_class" {
  description = "Instance class"
  type        = string
  default     = "db.t4g.micro"
}

variable "db_engine_version" {
  description = "Database engine version, depends on engine type"
  type        = string
  default     = "16.8"
}

variable "multi_az" {
  type    = string
  default = false
}

variable "APP_STAGE" {
  type    = string
  default = "prod"
}

variable "additional_tags" {
  type    = map(string)
  default = {}
}

variable "REGION" {
  type    = string
  default = ""
}

variable "additional_s3_arn_list" {
  description = "Additional ARNs to allow access to the S3 bucket"
  type        = list(string)
  default     = []

}