locals {
  mandatory_tags = {
    "Application" = "Data-Warehouse"
    "Author"      = "Data-Team"
    "MultiRegion" = "false"
    "Terraform"   = "true"
    "Product"     = "Data"
    "Squad"       = "Data"
    "Terraform"   = "true"
  }

  all_tags = merge(local.mandatory_tags, var.tags)
}


resource "aws_iam_role" "redshift_stream_role" {
  name = "${var.redshift_stream_role_name}_${local.converted_region}"
  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [{
      Effect = "Allow"
      Principal = {
        Service = "redshift.amazonaws.com"
      }
      Action = "sts:AssumeRole"
    }]
  })
}

resource "aws_iam_policy" "redshift_stream_policy" {
  for_each = { for idx, item in var.cross_account_kinesis_roles : "${item.account_id}_${item.role_name}" => item }

  name        = "RedshiftStreamPolicy_${each.key}_${local.converted_region}"
  path        = "/"
  description = "Kinesis stream access for redshift - ${each.key}"
  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Sid    = "StmtStreamRole",
        Effect = "Allow",
        Action = [
          "sts:AssumeRole"
        ],
        Resource = "arn:aws:iam::${each.value.account_id}:role/${each.value.role_name}"
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "redshift_stream_attachment" {
  for_each = aws_iam_policy.redshift_stream_policy

  role       = aws_iam_role.redshift_stream_role.name
  policy_arn = each.value.arn
}

resource "aws_redshift_cluster_iam_roles" "redshift-poc-stream-attachment" {
  count                = var.attache_redshift_stream_role ? 1 : 0
  cluster_identifier   = var.redshift_cluster_identifier
  iam_role_arns        = [aws_iam_role.redshift_stream_role.arn, var.default_iam_role_arn]
  default_iam_role_arn = var.default_iam_role_arn
}


resource "aws_iam_role" "cfa_redshift_unload" {
  count = var.create_redshift_unload_role ? 1 : 0

  name = "cfa_redshift_unload"

  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [{
      Effect = "Allow",
      Principal = {
        Service = "redshift.amazonaws.com"
      },
      Action = "sts:AssumeRole"
    }]
  })
}

resource "aws_iam_policy" "cfa_redshift_unload_policy" {
  count = var.create_redshift_unload_role ? 1 : 0

  name = "cfa_redshift_unload_policy"

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [{
      Sid    = "CrossAccountPolicy",
      Effect = "Allow",
      Action = [
        "s3:PutObject",
        "s3:Get*",
        "s3:List*"
      ],
      Resource = [
        "arn:aws:s3:::dev-dttanalyticssb-us-east-1-fingermark",
        "arn:aws:s3:::dev-dttanalyticssb-us-east-1-fingermark/*"
      ]
    }]
  })
}

resource "aws_iam_policy_attachment" "cfa_redshift_unload_attachment" {
  count      = var.create_redshift_unload_role ? 1 : 0
  name       = "cfa_redshift_unload_attachment"
  policy_arn = aws_iam_policy.cfa_redshift_unload_policy[0].arn
  roles      = [aws_iam_role.cfa_redshift_unload[0].name]
}

#IoT to Kinesis failure data transfer

resource "aws_s3_bucket" "iot_to_kinesis_failure_bucket" {
  count  = var.iot_to_kinesis_failure_bucket_name != "" ? 1 : 0
  bucket = "${var.iot_to_kinesis_failure_bucket_name}-${data.aws_region.current.name}"

  tags = local.all_tags
}

resource "aws_s3_bucket_ownership_controls" "iot_to_kinesis_failure_bucket" {
  count  = var.iot_to_kinesis_failure_bucket_name != "" ? 1 : 0
  bucket = aws_s3_bucket.iot_to_kinesis_failure_bucket[count.index].id
  rule {
    object_ownership = "BucketOwnerPreferred"
  }
}
resource "aws_s3_bucket_acl" "iot_to_kinesis_failure_bucket_acl" {
  count      = var.iot_to_kinesis_failure_bucket_name != "" ? 1 : 0
  depends_on = [aws_s3_bucket_ownership_controls.iot_to_kinesis_failure_bucket]

  bucket = aws_s3_bucket.iot_to_kinesis_failure_bucket[count.index].id
  acl    = "private"
}

resource "aws_s3_bucket_policy" "iot_to_kinesis_failure_bucket_policy" {
  count  = (length(var.iot_to_kinesis_failure_bucket_name) > 0 && length(keys(var.cross_account_iot_kinesis_failure_lambda_roles)) > 0) ? 1 : 0
  bucket = aws_s3_bucket.iot_to_kinesis_failure_bucket[count.index].id

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Principal = {
          AWS = concat(
            [for account, role in var.cross_account_iot_kinesis_failure_lambda_roles : "arn:aws:iam::${account}:role/${role}"],
          )
        },
        Action   = "s3:PutObject",
        Resource = "arn:aws:s3:::${var.iot_to_kinesis_failure_bucket_name}-${data.aws_region.current.name}/*"
      }
    ]
  })
}

resource "aws_s3_bucket_lifecycle_configuration" "iot_to_kinesis_failure_bucket_lifecycle" {
  count  = var.iot_to_kinesis_failure_bucket_name != "" ? 1 : 0
  bucket = aws_s3_bucket.iot_to_kinesis_failure_bucket[count.index].id

  rule {
    id     = "intelligent-tiering-and-delete-rule"
    status = "Enabled"

    filter {
      prefix = ""
    }
    transition {
      days          = 30
      storage_class = "INTELLIGENT_TIERING"
    }
    expiration {
      days = 180 # Delete the object after 6 months (180 days)
    }
  }
}