terraform {
  required_providers {
    archive = {
      source  = "hashicorp/archive"
      version = "~> 2.0"
    }
    aws = {
      source = "hashicorp/aws"
    }
  }
}

locals {
  mandatory_tags = {
    "Application" = "Data-Warehouse"
    "Author"      = "Data-Team"
    "MultiRegion" = "false"
    "Terraform"   = "true"
    "Product"     = "Data"
    "Squad"       = "Data"
    "Stack"       = "ETL"
    "Terraform"   = "true"
  }

  all_tags = merge(local.mandatory_tags, var.tags)
}

resource "aws_s3_bucket" "lambda_bucket" {
  bucket = var.s3_bucket_name
  tags   = local.all_tags
}

resource "aws_s3_bucket_public_access_block" "lambda_bucket" {
  bucket                  = aws_s3_bucket.lambda_bucket.id
  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}
resource "aws_s3_bucket_ownership_controls" "lambda_bucket" {
  bucket = aws_s3_bucket.lambda_bucket.id
  rule {
    object_ownership = "BucketOwnerPreferred"
  }
}

resource "aws_s3_bucket_versioning" "lambda_bucket" {
  depends_on = [aws_s3_bucket_ownership_controls.lambda_bucket]
  bucket     = aws_s3_bucket.lambda_bucket.id
  versioning_configuration {
    status = "Enabled"
  }
}

resource "aws_cloudwatch_event_rule" "daily_running" {
  name                = "trigger-mcd-aus-daily"
  schedule_expression = "cron(0 16 * * ? *)" # At 4:00 PM UTC every day (2:00 AM AEST)
  tags                = local.all_tags
}

# lambda_zip_new_restaurant_ids

data "archive_file" "lambda_zip_dim_restaurant" {
  type        = "zip"
  source_dir  = "${path.module}/lambda/lambda_etl"
  output_path = "${path.module}/lambda/dim_restaurant.zip"
}

resource "aws_s3_object" "lambda_zip_dim_restaurant" {
  bucket      = aws_s3_bucket.lambda_bucket.bucket
  key         = "lambda/dim_restaurant.zip"
  source      = data.archive_file.lambda_zip_dim_restaurant.output_path
  source_hash = data.archive_file.lambda_zip_dim_restaurant.output_base64sha256
}

resource "aws_cloudformation_stack" "etl_lambda_application_stack" {
  name = "etl-mcd-aus-sync-stack"
  template_body = jsonencode({
    AWSTemplateFormatVersion = "2010-09-09"
    Description              = "Lambda application for ETL MCD AUS Sync"
    Resources = {
      dimRestaurantInit = {
        Type = "AWS::Lambda::Function"
        Properties = {
          FunctionName  = "etl_mcd_aus_dim_restaurant"
          Handler       = "lambda_function.lambda_handler"
          Role          = { "Fn::GetAtt" = ["LambdaExecutionRole", "Arn"] }
          Runtime       = "python3.12"
          Architectures = ["arm64"]
          Code = {
            S3Bucket        = { "Ref" = "LambdaS3Bucket" }
            S3Key           = "lambda/dim_restaurant.zip"
            S3ObjectVersion = aws_s3_object.lambda_zip_dim_restaurant.version_id
          }
          Timeout = 120
          Environment = {
            Variables = {
              REDSHIFT_CLUSTER_ID = var.redshift_cluster_id
              DATABASE            = var.database
              DB_USER             = var.db_user
              SECRET_NAME         = var.secret_name
              REGION_NAME         = var.region_name
              QUERY_STEPS         = var.query_steps_dim_restaurant
            }
          }
        }
      }
      EventBridgeRule = {
        Type = "AWS::Events::Rule"
        Properties = {
          Name               = "dimRestaurantEventBridges"
          ScheduleExpression = "rate(5 minutes)"
          Targets = [
            {
              Arn = { "Fn::GetAtt" = ["dimRestaurantInit", "Arn"] }
              Id  = "dimRestaurantInit"
            }
          ]
        }
      }
      LambdaInvokePermissionDimRestaurant = {
        Type = "AWS::Lambda::Permission"
        Properties = {
          FunctionName = { "Ref" = "dimRestaurantInit" }
          Action       = "lambda:InvokeFunction"
          Principal    = "events.amazonaws.com"
          SourceArn    = { "Fn::GetAtt" = ["EventBridgeRule", "Arn"] }
        }
      }
      LambdaExecutionRole = {
        Type = "AWS::IAM::Role"
        Properties = {
          AssumeRolePolicyDocument = {
            Version = "2012-10-17"
            Statement = [
              {
                Effect = "Allow"
                Principal = {
                  Service = "lambda.amazonaws.com"
                }
                Action = "sts:AssumeRole"
              }
            ]
          }
          Policies = [
            {
              PolicyName = "McDAusLambdaPolicy"
              PolicyDocument = {
                Version = "2012-10-17"
                Statement = [
                  {
                    Effect = "Allow"
                    Action = [
                      "redshift-data:ExecuteStatement",
                      "redshift-data:DescribeStatement",
                      "redshift-data:ListStatements",
                      "redshift:GetClusterCredentials",
                      "secretsmanager:GetSecretValue",
                      "logs:CreateLogGroup",
                      "logs:CreateLogStream",
                      "logs:PutLogEvents"
                    ]
                    Resource = "*"
                  }
                ]
              }
            }
          ]
        }
      }

    }
    Parameters = {
      LambdaS3Bucket = {
        Type = "String"
      }
    }
  })
  parameters = {
    LambdaS3Bucket = aws_s3_bucket.lambda_bucket.bucket
  }
  capabilities      = ["CAPABILITY_IAM"]
  notification_arns = var.notification_arns
  tags              = local.all_tags
}