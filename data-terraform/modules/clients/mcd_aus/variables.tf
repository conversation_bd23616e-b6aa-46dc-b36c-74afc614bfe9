variable "tags" {
  description = "Additional tags to apply to resources"
  type        = map(string)
  default     = {}
}

variable "s3_bucket_name" {
  description = "S3 bucket to upload Lambda function code"
  type        = string
  default     = "etl-lambda-code-mcd-aus"
}

variable "redshift_cluster_id" {
  description = "Redshift Cluster ID"
  type        = string
  default     = "main-dw-au-prod"
}

variable "database" {
  description = "Database name"
  type        = string
  default     = "prod"
}

variable "db_user" {
  description = "Database user"
  type        = string
  default     = "dw_master"
}

variable "secret_name" {
  description = "Secret name"
  type        = string
  default     = "main-dw-au-prod-credential"
}

variable "region_name" {
  description = "Region name"
  type        = string
}

variable "query_steps_dim_restaurant" {
  description = "Query steps"
  type        = string
  default     = "[\"insert_restaurant\"]"
}

variable "notification_arns" {
  description = "List of SNS ARNs to notify"
  type        = list(string)
  default     = null
}