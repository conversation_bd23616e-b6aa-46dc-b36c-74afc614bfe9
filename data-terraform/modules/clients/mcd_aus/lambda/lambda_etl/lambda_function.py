# lambda_handler.py

import logging
from redshift_handler import RedshiftHandler
from sql_queries import SqlQueries
import os
import json

redshift_handler = RedshiftHandler()

def lambda_handler(event, context):
    """
    AWS Lambda entry point. Executes queries based on query_steps or default steps.
    if needed can pass the query_steps to lambda function
    ex:
        {
            "query_steps": ["insert_restaurant","second_step"]
        }
    """
    try:        
        query_steps =  (json.loads(os.environ['QUERY_STEPS']) if 'QUERY_STEPS' in os.environ else SqlQueries.DEFAULT_QUERY_STEPS)
        logging.info(f"Using query steps: {query_steps}")
        queries = SqlQueries.get_queries_from_steps(query_steps)
        logging.info(f"Executing queries: {queries}")
        results = []
        for sql in queries:
            logging.info(f"Executing query: {sql}")
            result = redshift_handler.execute_query(sql)
            logging.info(f"Query executed successfully: {result}")
            results.append(result)

        return {
            'statusCode': 200,
            'body': {"message": "SQL queries executed successfully", "results": results},
        }
    except Exception as e:
        logging.error(f"Error executing SQL: {e}")
        return {
            'statusCode': 500,
            'body': {"error": "Error executing SQL", "details": str(e)},
        }