# sql_queries.py

class SqlQueries:
    """
    Encapsulates SQL query definitions and logic for retrieving queries.
    """
    # Query definitions
    QUERIES = {
        "insert_restaurant": {
            "description": "Insert new restaurants into the dim_restaurant table.",
            "sql": """
                    INSERT INTO eyecue_mcd_aus.dim_restaurant
                                (fingermark_id)
                    SELECT store_id
                    FROM   (SELECT payload.store_id::text AS store_id
                            FROM   eyecue_mcd_aus.v_aggregated
                            GROUP  BY payload.store_id::text) AS store_id
                    WHERE  store_id NOT IN (SELECT fingermark_id
                                            FROM   eyecue_mcd_aus.dim_restaurant); 
            """
        },
    }

    # Predefined query steps
    DEFAULT_QUERY_STEPS = ["insert_restaurant"]

    @classmethod
    def get_queries_from_steps(cls, query_steps=None):
        """
        Given a list of query steps (or the default steps), return the corresponding SQL queries.
        Raises ValueError for invalid steps.
        """
        query_steps = query_steps or cls.DEFAULT_QUERY_STEPS  # Use default steps if none are provided
        queries = []
        for step in query_steps:
            if step not in cls.QUERIES:
                raise ValueError(f"Invalid query step: {step}. Available steps: {list(cls.QUERIES.keys())}")
            queries.append(cls.QUERIES[step]["sql"])
        return queries

    @classmethod
    def get_query_descriptions(cls):
        """
        Returns descriptions of all available queries.
        """
        return {key: value["description"] for key, value in cls.QUERIES.items()}
