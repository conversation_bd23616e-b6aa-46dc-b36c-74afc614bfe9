import boto3
import os
import logging
from datetime import datetime

class RedshiftHandler:
    def __init__(self):
        self.redshift_data = boto3.client('redshift-data')
        self.cluster_id = os.environ['REDSHIFT_CLUSTER_ID']
        self.database = os.environ['DATABASE']
        self.db_user = os.environ['DB_USER']

    def execute_query(self, sql_statement):
        response = self.redshift_data.execute_statement(
            ClusterIdentifier=self.cluster_id,
            Database=self.database,
            DbUser=self.db_user,
            Sql=sql_statement
        )
        query_id = response['Id']
        logging.info(f"Query {query_id} submitted")
        status = self.redshift_data.describe_statement(Id=query_id)
        logging.info(f"Query {query_id} status: {status['Status']}")
        while status['Status'] in ['SUBMITTED', 'PICKED', 'STARTED']:
            status = self.redshift_data.describe_statement(Id=query_id)
            logging.info(f"Query {query_id} status: {status['Status']}")

        if status['Status'] == 'FAILED':
            logging.error(f"Query {query_id} failed: {status['Error']}")
            raise Exception(f"SQL execution failed: {status['Error']}")

        return self._make_serializable(status)
    
    def _make_serializable(self, obj):
        if isinstance(obj, dict):
            return {k: self._make_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._make_serializable(i) for i in obj]
        elif isinstance(obj, datetime):
            return obj.isoformat()
        else:
            return obj