Project mcd_aus {
    database_type: 'Redshift'
    Note: '''
    # McDonald Australia 
    https://dbdocs.io/rusiru.bulathgamage/mcd_aus
    '''
}

Table eyecue_mcd_aus.mv_aggregate [headercolor: #990D0D] {
    approximate_arrival_timestamp timestamp
    partition_key varchar
    shard_id varchar
    sequence_number varchar
    refresh_time timestamp
    payload json [unique]
    payload_size int 
    is_outlier boolean
    Note: "This is a data landing mv"

}

Table eyecue_mcd_aus.mv_ds_mcd_aus_eyecue_events [headercolor: #990D0D] {
    approximate_arrival_timestamp timestamp
    partition_key varchar
    shard_id varchar
    sequence_number varchar
    refresh_time timestamp
    payload json
    payload_size int
    is_outlier boolean
    Note: "This is a data landing mv"
}

Table eyecue_mcd_aus.dim_site_information [headercolor: #1E69FD] {
    fingermark_id varchar(20) [note: "ENCODE lzo"]
    restaurant_name varchar(255) [note: "ENCODE lzo"]
    time_zone varchar(50) [note: "ENCODE lzo"]
    address varchar(255) [note: "ENCODE lzo"]
    suburb varchar(255) [note: "ENCODE lzo"]
    city varchar(255) [note: "ENCODE lzo"]
    state varchar(255) [note: "ENCODE lzo"]
    post_code varchar(20) [note: "ENCODE lzo"]
    country varchar(100) [note: "ENCODE lzo"]
    latitude numeric(10,7) [note: "ENCODE az64"]
    longitude numeric(10,7) [note: "ENCODE az64"]
    num_order_lanes int [note: "ENCODE az64"]
    pf_blocks_lane boolean [note: "ENCODE raw"]
}

Table eyecue_mcd_aus.v_aggregated [headercolor: #EB801B] {
    meta_kinesis_datastream_arrival_datetime_utc timestamp
    meta_redshift_refresh_datetime_utc timestamp
    partition_key varchar
    shard_id varchar
    sequence_number varchar
    payload json [Ref: -eyecue_mcd_aus.mv_aggregate.payload ]
    Note: "Last 7 day aggregated data"
}