import boto3
import pandas as pd
import io
import datetime as dt
import json
import os
import time
import logging

logger = logging.getLogger()
logger.setLevel("INFO")

pd.set_option("display.max_columns", None)  # Show all columns
pd.set_option("display.width", 1000)  # Avoid line breaks

# Initialize Boto3 clients
redshift_data = boto3.client("redshift-data")

# Define the Redshift cluster and database details from environment variables
REDSHIFT_CLUSTER_ID = os.environ["REDSHIFT_CLUSTER_ID"]
DATABASE = os.environ["DATABASE"]
DB_USER = os.environ["DB_USER"]
REGION_NAME = os.environ["REGION_NAME"]
S3_BUCKET = os.environ["S3_BUCKET"]
IAM_ROLE_ARN = os.environ["IAM_ROLE_ARN"]

# Define the SQL queries
REFRESH_MV_SQL = "refresh MATERIALIZED view eyecue_ptl_usa.mv_last_day_adr;"

EXTRACT_FROM_MV_SQL = f"""
WITH
    latest_record AS (
        SELECT MAX(
                meta_kinesis_datastream_arrival_datetime_utc
            ) AS latest_datetime
        FROM eyecue_ptl_usa.fct_active_departure_rate
    )
SELECT * FROM eyecue_ptl_usa.mv_last_day_adr
WHERE
    meta_kinesis_datastream_arrival_datetime_utc > (
        SELECT latest_datetime
        FROM latest_record
    )
"""


def execute_sql(sql_statement):
    """
    Function to execute an SQL statement using the Redshift Data API.
    """
    response = redshift_data.execute_statement(
        ClusterIdentifier=REDSHIFT_CLUSTER_ID,
        Database=DATABASE,
        DbUser=DB_USER,
        Sql=sql_statement,
    )

    # Check the execution id for the SQL statement
    execution_id = response["Id"]

    # Wait for query to complete
    status = "STARTED"  # Initial status
    while status != "FINISHED":
        time.sleep(5)  # Wait for 5 seconds before checking again
        statement_result = redshift_data.describe_statement(Id=execution_id)
        status = statement_result["Status"]

        # Check for errors
        if status == "FAILED":
            raise Exception(f"SQL execution failed: {statement_result['Error']}")

    return execution_id


def get_query_results(query_id):
    """
    Retrieve the results of a previously executed query.

    Parameters:
    query_id (str): The ID of the query whose results are to be retrieved.

    Returns:
    tuple: A tuple containing the records and the column metadata of the query result.
    """

    records = []
    next_token = None

    while True:
        if next_token:
            result_response = redshift_data.get_statement_result(
                Id=query_id, NextToken=next_token
            )
        else:
            result_response = redshift_data.get_statement_result(Id=query_id)

        records.extend(result_response["Records"])
        next_token = result_response.get("NextToken", None)

        # If there's no next token, we've retrieved all data
        if not next_token:
            break

    column_metadata = result_response["ColumnMetadata"]
    return records, column_metadata


def process_result_to_dataframe(result, column_metadata):
    """
    Processes the result from Redshift and converts it to a pandas DataFrame.

    Args:
        result (dict): The result dictionary from Redshift.

    Returns:
        pd.DataFrame: The processed data as a pandas DataFrame.
    """

    # Extract column names from the result
    columns = [col["label"] for col in column_metadata]

    # Process results and convert to DataFrame
    data = []
    for row in result:
        row_data = {}
        for i, field in enumerate(row):  # Use enumerate to get index
            key = columns[i]  # Get column name using index
            value = list(field.values())[0]  # Extract value from the dictionary
            row_data[key] = value
        data.append(row_data)

    return pd.DataFrame(data)


def manipulate_adr_dataframe(df):
    """
    Manipulates the DataFrame to extract the payload information as columns,
    renames columns to align with the Redshift table,
    ensures only relevent columns are kept and datatype compatiblity,
    adds record creation time and event_time_at_utc column,
    converts datetime to EPOCH format to ensure Redshift Copy works correctly.

    Args:
        df (pd.DataFrame): The DataFrame to manipulate.

    Returns:
        pd.DataFrame: The manipulated DataFrame.
    """
    # extract the payload information into columns

    logger.info(f"START: Manipulation of dataframe  with size --> {df.shape[0]}")

    df = df.join(
        df["payload"].apply(lambda x: pd.Series(json.loads(x))), rsuffix="_duplicate"
    )

    if "is_active" in df.columns:
        df["is_active"] = df["is_active"].fillna(df["isActive"])
    else:
        df["is_active"] = df["isActive"]

    if "active_departure_rate" in df.columns:
        df["active_departure_rate"] = df["active_departure_rate"].fillna(
            df["activeDepartureRate"]
        )
    else:
        df["active_departure_rate"] = df["activeDepartureRate"]

    df = df.drop(columns=["isActive", "activeDepartureRate"])

    # Create timestamp col if not exists
    df["timestamp"] = df.get("timestamp", pd.NA)

    # Rename columns
    df = df.rename(
        columns={
            "store_id": "fingermark_id",
            "interval": "rate_interval",
            "version": "adr_version",
            "timestamp": "client_side_record_creation_time_utc",
        }
    )
    # List of columns to keep (ensure no new columns added cause failure)
    keep_columns = [
        "payload_key",
        "meta_kinesis_datastream_arrival_datetime_utc",
        "client_name",
        "fingermark_id",
        "event_type",
        "payload",
        "active_departure_rate",
        "eyeq_server_version",
        "rate_interval",
        "is_active",
        "adr_version",
        "client_side_record_creation_time_utc",
    ]

    # Drop columns not in keep_columns
    df = df[keep_columns]

    # Ensure datatype combatibility
    dtypes = {
        "payload_key": "string",
        "client_name": "string",
        "fingermark_id": "string",
        "event_type": "string",
        "active_departure_rate": "float64",
        "eyeq_server_version": "string",
        "rate_interval": "int32",
        "is_active": "bool",
        "adr_version": "int32",
    }
    df = df.astype(dtypes)
    df["payload"] = df["payload"].apply(json.loads)
    # Add record_creation column
    df["record_creation_time_at_utc"] = dt.datetime.now()

    # Create event_time_at_utc_tmp using client_side_record_creation_time_utc if available, else use meta_kinesis_datastream_arrival_datetime_utc
    df["event_time_at_utc_tmp"] = df["client_side_record_creation_time_utc"].fillna(
        df["meta_kinesis_datastream_arrival_datetime_utc"]
    )

    # Step 1: Convert all strings to datetime while preserving their timezone info
    df["meta_kinesis_datastream_arrival_datetime_utc"] = df[
        "meta_kinesis_datastream_arrival_datetime_utc"
    ].apply(pd.to_datetime, errors="coerce")

    # Step 2: Ensure all timestamps are in UTC
    df["meta_kinesis_datastream_arrival_datetime_utc"] = df[
        "meta_kinesis_datastream_arrival_datetime_utc"
    ].apply(lambda x: x if x.tzinfo else x.tz_localize("UTC"))

    df["meta_kinesis_datastream_arrival_datetime_utc"] = (
        df["meta_kinesis_datastream_arrival_datetime_utc"].astype("int64") // 10**6
    )  # Convert to epoch time (milliseconds)

    # Step 1: Convert all strings to datetime while preserving their timezone info
    df["event_time_at_utc_tmp"] = df["event_time_at_utc_tmp"].apply(
        pd.to_datetime, errors="coerce"
    )

    # Step 2: Ensure all timestamps are in UTC
    df["event_time_at_utc_tmp"] = df["event_time_at_utc_tmp"].apply(
        lambda x: x if x.tzinfo else x.tz_localize("UTC")
    )

    df["event_time_at_utc_tmp"] = (
        df["event_time_at_utc_tmp"].astype("int64") // 10**6
    )  # Convert to epoch time (milliseconds)

    # Insert event_time_at_utc
    df.insert(6, "event_time_at_utc", df["event_time_at_utc_tmp"])

    df.drop(
        columns=["event_time_at_utc_tmp", "client_side_record_creation_time_utc"],
        inplace=True,
    )

    # Convert record_creation_time_at_utc to epoch
    df["record_creation_time_at_utc"] = (
        df["record_creation_time_at_utc"].astype("int64") // 10**3
    )  # Convert to epoch time (milliseconds)

    # Fill NaN values with correct type for Redshift
    df.fillna(
        {
            "client_name": "NULL",
            "fingermark_id": "NULL",
            "event_type": "NULL",
            "payload": "NULL",
            "active_departure_rate": 0.0,
            "eyeq_server_version": "NULL",
            "rate_interval": 0,
            "adr_version": "NULL",
        },
        inplace=True,
    )

    logger.info(f"END: Manipulated dataframe size --> {df.shape[0]}")

    return df


def upload_to_s3_with_site_and_date_partitioning(df):
    """
    Uploads a DataFrame to S3 with partitioning by fingermark_id, year, month,
    and day, using datetime.now() for unique filenames. Keeps epoch time in the output.

    Args:
        df (pd.DataFrame): The DataFrame to upload.
    """
    s3_keys = []
    for site_id, site_group in df.groupby("fingermark_id"):

        # Create a temporary datetime column for partitioning
        site_group["datetime_temp"] = pd.to_datetime(
            site_group["event_time_at_utc"], unit="ms", errors="coerce"
        )

        for date, date_group in site_group.groupby(site_group["datetime_temp"].dt.date):
            year = date.strftime("%Y")
            month = date.strftime("%m")
            day = date.strftime("%d")

            timestamp = dt.datetime.now().strftime("%Y%m%d_%H%M%S")
            s3_key = f"{site_id}/{year}/{month}/{day}/data_{timestamp}.parquet"
            s3_keys.append(s3_key)

            # Remove the temporary datetime column before saving
            date_group = date_group.drop(columns=["datetime_temp"])

            parquet_buffer = io.BytesIO()
            date_group.to_parquet(
                parquet_buffer, allow_truncated_timestamps=True, index=False
            )

            s3 = boto3.client("s3")

            s3.put_object(Bucket=S3_BUCKET, Key=s3_key, Body=parquet_buffer.getvalue())

    return s3_keys


def copy_to_redshift(s3_keys):
    """
    Copies data from S3 to Redshift.

    Args:
        s3_keys (list): List of S3 keys to copy data from.
    """
    for s3_key in s3_keys:
        copy_command = f"""
        COPY prod.eyecue_ptl_usa.fct_active_departure_rate
        FROM 's3://{S3_BUCKET}/{s3_key}'
        IAM_ROLE '{IAM_ROLE_ARN}'
        FORMAT PARQUET SERIALIZETOJSON;
        """
        response = redshift_data.execute_statement(
            ClusterIdentifier=REDSHIFT_CLUSTER_ID,
            Database=DATABASE,
            DbUser=DB_USER,
            Sql=copy_command,
        )
        execution_id = response["Id"]

        # Wait for the statement to finish (using a loop and describe_statement)
        status = "STARTED"  # Initial status
        while status != "FINISHED":
            time.sleep(5)  # Wait for 5 seconds before checking again
            statement_result = redshift_data.describe_statement(Id=execution_id)
            status = statement_result["Status"]

            if status == "FAILED":
                raise Exception(f"COPY command failed: {statement_result.get('Error')}")

        logger.info(f"Data copied to Redshift from s3://{S3_BUCKET}/{s3_key}")
    return


def lambda_handler(event, context):
    try:

        logger.info("START : Refreshing Materialized View...")
        execute_sql(REFRESH_MV_SQL)
        logger.info("END : Materialized View Refreshed.")

        logger.info("START : Getting data from redshift")
        query_id = execute_sql(EXTRACT_FROM_MV_SQL)
        logger.info(f"SUBMITTED : Getting data from redshift query id {query_id}")

        records, column_metadata = get_query_results(query_id)

        logger.info(f"END : Getting data from redshift results size {len(records)}")

        if len(records) == 0:
            logger.info(f"No data to proceed. Existing ...")
            return {
                "statusCode": 200,
                "body": json.dumps("No data to proceed. Existing ..."),
            }

        logger.info(f"START : Converting {len(records)} records to a panda dataframe")
        adr_df = process_result_to_dataframe(records, column_metadata)
        logger.info(
            f"END : Converting {len(records)} records to a panda dataframe with df size {adr_df.shape[0]}"
        )

        adr_df = manipulate_adr_dataframe(adr_df)

        s3_keys = upload_to_s3_with_site_and_date_partitioning(adr_df)
        logger.info(s3_keys)

        copy_to_redshift(s3_keys)

        return {
            "statusCode": 200,
            "body": json.dumps(
                "Materialized View Refreshed, Data uploaded to s3 and Copied to Redshift Successfully!"
            ),
        }

    except Exception as e:
        logger.info(f"Error executing : {e}")
        return {"statusCode": 500, "body": json.dumps(f"Error: {str(e)}")}
