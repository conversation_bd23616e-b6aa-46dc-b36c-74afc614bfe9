import boto3
import pandas as pd
import warnings
import email
import json
import signal
from io import BytesIO
from datetime import datetime
import uuid
import re
import hashlib
import os

# --- Configuration Variables ---
SES_BUCKET_NAME = os.environ.get('SES_BUCKET_NAME', 'fingermarkdata-ses-data')
SES_PARTITION = os.environ.get('SES_PARTITION', 'ptl_usa_pos_data/')
WRITE_BUCKET = os.environ.get('WRITE_BUCKET', 'fm-prod-datalake-1-us-east-1')
SNS_TOPIC_ARN = os.environ.get('SNS_TOPIC_ARN', 'arn:aws:sns:us-east-1:047783385012:data-engineering-slack-message-us-e1')
REDSHIFT_CLUSTER_ID = os.environ.get('REDSHIFT_CLUSTER_ID', 'main-dw-us-east-1-prod')
REDSHIFT_DATABASE = os.environ.get('REDSHIFT_DATABASE', 'prod')
REDSHIFT_USER = os.environ.get('REDSHIFT_USER', 'dw_master')
IAM_ROLE_ARN = os.environ.get('IAM_ROLE_ARN', 'arn:aws:iam::047783385012:role/redshift-main-dw-us-east-1-us-east-1')
TABLE_NAME = os.environ.get('TABLE_NAME', 'eyecue_ptl_usa.pos_data')
RAW_FILE_PATH = os.environ.get('RAW_FILE_PATH', 'PTL_USA/POS_DATA/raw_files/')
PROCESSED_FILE_PATH = os.environ.get('PROCESSED_FILE_PATH', 'PTL_USA/POS_DATA/processed_files/')
MAX_FILE_SIZE = int(os.environ.get('MAX_FILE_SIZE', '10000000'))

s3_client = boto3.client('s3')
sns_client = boto3.client('sns')
redshift = boto3.client('redshift-data')

def extract_attachments(msg):
    attachments = []
    if msg.is_multipart():
        for part in msg.get_payload():
            attachments.extend(extract_attachments(part))
    else:
        filename = msg.get_filename()
        if filename:
            attachments.append(msg)
    return attachments

def generate_row_hash(row):
    row_str = ''.join(str(val) for val in row)
    return hashlib.md5(row_str.encode('utf-8')).hexdigest()

def process_excel_file(file_data):
    file_content = BytesIO(file_data)
    df = pd.read_excel(file_content, sheet_name=0, header=None)
    print(f"Excel rows read: {len(df)}")

    header = df.iloc[4]
    data = df.iloc[5:]
    data.columns = header
    data.reset_index(drop=True, inplace=True)
    data.columns = [str(col).strip() for col in data.columns]
    print(f"Raw data columns: {list(data.columns)}")

    data = data.rename(columns={
        "Store": "pos_store_full_name",
        "Day": "date",
        "Time Selection": "time",
        "Current Year": "current_year_count",
        "Last Year": "previous_year_count",
        "% Variance": "variance"
    })

    data["date"] = pd.to_datetime(data["date"], errors='coerce')
    data["current_year_count"] = pd.to_numeric(data["current_year_count"], errors='coerce')
    data["previous_year_count"] = pd.to_numeric(data["previous_year_count"], errors='coerce')
    data["variance"] = pd.to_numeric(data["variance"], errors='coerce')

    data = (
        data.loc[data['pos_store_full_name'].notna()]
        .loc[data['pos_store_full_name'] != 'Store']
        .loc[~data['time'].isin(['Early Hours', 'Late Hours', '-FIRST HALF-', '-SECOND HALF-', '--DAILY TOTAL--'])]
        .loc[data['current_year_count'].notna()]
    )
    print(f"Rows after filtering: {len(data)}")

    data["time"] = pd.to_datetime(data["time"], errors='coerce').dt.time
    data = data.reset_index(drop=True)
    data['time'] = data['time'].apply(lambda x: x.strftime('%H:%M:%S') if pd.notna(x) else '00:00:00')
    data['pos_data_at_local'] = pd.to_datetime(data['date'].astype(str) + ' ' + data['time'])

    def split_store_name(store_name):
        match = re.match(r"(\d+)\s*(.*)", store_name)
        return match.groups() if match else (None, store_name)

    data[['pos_store_id', 'pos_store_name']] = data['pos_store_full_name'].apply(
        lambda x: pd.Series(split_store_name(str(x)))
    )
    data['row_id'] = data.apply(generate_row_hash, axis=1)
    data['inserted_at'] = datetime.now()
    print(f"Final processed rows: {len(data)}")
    return data[['row_id', 'pos_store_full_name', 'pos_data_at_local', 'current_year_count',
                 'previous_year_count', 'pos_store_id', 'pos_store_name', 'inserted_at']]

def load_to_redshift(df, message_id):
    if df.empty:
        print("No data to load to Redshift")
        return None

    # Step 1: Upload data to S3 as CSV
    csv_buffer = BytesIO()
    df.to_csv(csv_buffer, index=False)
    csv_buffer.seek(0)

    staging_key = f"{PROCESSED_FILE_PATH}staging_{message_id}_{uuid.uuid4()}.csv"
    s3_client.put_object(Bucket=WRITE_BUCKET, Key=staging_key, Body=csv_buffer.getvalue())
    print(f"CSV file uploaded to s3://{WRITE_BUCKET}/{staging_key}")

    # Step 2: Execute all SQL commands in one multi-statement transaction
    temp_table = f"temp_pos_data_{message_id.replace('-', '_')}"
    multi_statement_sql = f"""
    CREATE TEMP TABLE {temp_table} (LIKE {TABLE_NAME});
    COPY {temp_table}
      FROM 's3://{WRITE_BUCKET}/{staging_key}'
      IAM_ROLE '{IAM_ROLE_ARN}'
      FORMAT AS CSV
      IGNOREHEADER 1;
    INSERT INTO {TABLE_NAME}
    SELECT t.*
    FROM {temp_table} t
    WHERE NOT EXISTS (
        SELECT 1 FROM {TABLE_NAME} m WHERE m.row_id = t.row_id
    );
    COMMIT;
    """
    print(f"Executing multi-statement SQL: {multi_statement_sql}")

    try:
        response = redshift.execute_statement(
            ClusterIdentifier=REDSHIFT_CLUSTER_ID,
            Database=REDSHIFT_DATABASE,
            DbUser=REDSHIFT_USER,
            Sql=multi_statement_sql
        )
        print(f"Redshift load started with execution ID: {response['Id']}")
        return response['Id']
    except Exception as e:
        print(f"Failed to load data to Redshift: {str(e)}")
        raise

def timeout_handler(signum, frame):
    raise Exception("Lambda timeout imminent")

def get_email_payload(event):
    try:
        ses_mail = event['Records'][0]['ses']['mail']
        receipt = event['Records'][0]['ses']['receipt']
        message_id = ses_mail['messageId']
        print(f'Commencing processing for message {message_id}')

        if 'FAIL' in [receipt['spamVerdict']['status'], receipt['virusVerdict']['status'],
                      receipt['spfVerdict']['status'], receipt['dkimVerdict']['status']]:
            raise Exception('Message failed security checks')

        key_path = SES_PARTITION + message_id
        raw_email = s3_client.get_object(Bucket=SES_BUCKET_NAME, Key=key_path)
        print(f"Raw email size: {raw_email['ContentLength']} bytes")

        if raw_email['ContentLength'] > MAX_FILE_SIZE:
            raise Exception("File too large for Lambda - reroute to Step Function")

        object_content = raw_email['Body'].read().decode('utf-8')
        msg = email.message_from_string(object_content)
        # payloads = msg.get_payload()
        return msg, message_id
    except Exception as e:
        print(f"Exception: {e}")
        sns_payload = {
            'message_id': message_id if 'message_id' in locals() else 'unknown',
            'message': f'Error: {str(e)}'
        }
        sns_client.publish(TopicArn=SNS_TOPIC_ARN, Message=json.dumps(sns_payload))
        raise

def lambda_handler(event, context):
    signal.signal(signal.SIGALRM, timeout_handler)
    signal.alarm(840)
    msg, message_id = get_email_payload(event)
    attachments = extract_attachments(msg)
    if not attachments:
        raise ValueError("No attachments found in the email")

    excel_file_names = []
    for attachment in attachments:
        name = attachment.get_filename()
        print(f"Processing attachment: {name}")
        if name and name.lower().endswith('.xlsx'):
            file_name = f'{message_id}_{name.replace(" ", "")}'
            data = attachment.get_payload(decode=True)

            raw_key = f'{RAW_FILE_PATH}{file_name}'
            s3_client.put_object(Bucket=WRITE_BUCKET, Key=raw_key, Body=data)
            excel_file_names.append(file_name)
            processed_data = process_excel_file(data)

            if not processed_data.empty:
                redshift_execution_id = load_to_redshift(processed_data, message_id)
                if redshift_execution_id:
                    print(f"Started Redshift load with execution ID: {redshift_execution_id}")
                else:
                    print("No Redshift load initiated due to empty data")

    sns_payload = {
        'message_id': message_id,
        'excel_file_names': excel_file_names,
        'message': 'Successfully processed and loaded to Redshift (new records only)'
    }
    sns_client.publish(TopicArn=SNS_TOPIC_ARN, Message=json.dumps(sns_payload))
    print(f'Finished processing message {message_id}')
    return {
        'statusCode': 200,
        'body': json.dumps('Email Processed Successfully')
    }