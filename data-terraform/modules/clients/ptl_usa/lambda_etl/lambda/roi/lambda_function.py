import json
import boto3
import time
import os

# Initialize Redshift Data API client
redshift_data = boto3.client('redshift-data')

# Define the Redshift cluster and database details from environment variables
REDSHIFT_CLUSTER_ID = os.environ['REDSHIFT_CLUSTER_ID']
DATABASE = os.environ['DATABASE']
DB_USER = os.environ['DB_USER']
SECRET_NAME = os.environ['SECRET_NAME']
REGION_NAME = os.environ['REGION_NAME']

# Define the SQL queries
REFRESH_MV_SQL = "refresh MATERIALIZED view eyecue_ptl_usa.mv_last_7_days_roi_events;"
INSERT_SQL = """
   INSERT INTO eyecue_ptl_usa.fct_roi_events
    SELECT client_name::text,
           store_id::text,
           camera_id::text,
           event_type::text,
           roi_type::text,
           roi_id::text,
           roi_event::text,
           vehicle_id::text,
           event_time_utc::TIMESTAMPTZ,
           event_time_local::TIMESTAMP,
           order_end_utc::TIMESTAMPTZ,
           order_end_local::TIMESTAMP,
           tracker_id::text,
           message_id::text,
           lane_number::text,
           car_count::text,
           event_order::text,
           overtaken::text,
           queue_level::text,
           valid_journey,
           total_time::text,
           event_label::text,
           functional_zone::text,
           extra::text,
           complete,
           artificial,
           confidence::FLOAT,
           coord_x1::FLOAT,
           coord_x2::FLOAT,
           coord_xc::FLOAT,
           coord_y1::FLOAT,
           coord_y2::FLOAT,
           coord_yc::FLOAT,
           creation_time::TIMESTAMP,
           event_id::text,
           eyeq_server_version::text,
           frames_life::text,
           image_url::text,
           vehicle_db_id::INT,
           tracker_version::text,
           meta_kinesis_datastream_arrival_datetime_utc  
    FROM eyecue_ptl_usa.mv_last_7_days_roi_events
    WHERE meta_kinesis_datastream_arrival_datetime_utc > (
        SELECT NVL(MAX(meta_kinesis_datastream_arrival_datetime_utc), trunc(sysdate) - 7)
        FROM eyecue_ptl_usa.fct_roi_events
    );
"""

def execute_sql(sql_statement):
    """
    Function to execute an SQL statement using the Redshift Data API.
    """
    response = redshift_data.execute_statement(
        ClusterIdentifier=REDSHIFT_CLUSTER_ID,
        Database=DATABASE,
        DbUser=DB_USER,
        Sql=sql_statement
    )
    
    # Check the execution id for the SQL statement
    query_id = response['Id']
    
    # Wait for query to complete
    status = redshift_data.describe_statement(Id=query_id)
    while status['Status'] in ['SUBMITTED', 'PICKED', 'STARTED']:
        # time.sleep(1)  # Wait before checking the status again
        status = redshift_data.describe_statement(Id=query_id)
    
    # Check for errors
    if status['Status'] == 'FAILED':
        raise Exception(f"SQL execution failed: {status['Error']}")
    
    return status

def lambda_handler(event, context):
    try:
        # Step 1: Refresh Materialized View
        print("Refreshing Materialized View...")
        refresh_status = execute_sql(REFRESH_MV_SQL)
        print("Materialized View Refreshed Successfully.")
        
        # Step 2: Insert Data into Fact Table
        print("Inserting Data into Fact Table...")
        insert_status = execute_sql(INSERT_SQL)
        print("Data Inserted Successfully.")
        
        return {
            'statusCode': 200,
            'body': json.dumps('Materialized View Refreshed and Data Inserted Successfully!')
        }
    
    except Exception as e:
        print(f"Error executing SQL: {e}")
        return {
            'statusCode': 500,
            'body': json.dumps(f"Error: {str(e)}")
        }