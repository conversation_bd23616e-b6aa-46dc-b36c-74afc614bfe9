variable "secrets_manager_arns" {
  description = "List of ARNs of the Secrets Manager secrets"
  type        = list(string)
}

variable "tags" {
  description = "Additional tags to apply to resources"
  type        = map(string)
  default     = {}
}
variable "redshift_cluster_id" {
  description = "Redshift cluster identifier"
  type        = string
}

variable "database" {
  description = "Database name"
  type        = string
}

variable "db_user" {
  description = "Database user"
  type        = string
}

variable "secret_name" {
  description = "Secrets Manager secret name"
  type        = string
}

variable "region_name" {
  description = "AWS region name"
  type        = string
}

variable "s3_bucket_name" {
  description = "S3 bucket to upload Lambda function code"
  type        = string
  default     = "lambda_bucket_ptl_usa"
}

variable "notification_arns" {
  description = "ARN for SNS topic to send notifications"
  type        = list(string)
  default     = null
}

variable "redshift_copy_iam" {
  description = "IAM Role for Redshift COPY"
  type        = string
}

variable "parquet_unload_s3_bucket" {
  description = "S3 bucket to unload parquet files"
  type        = string
}

variable "pandas_layer_arn" {
  type = list(string)
  #https://aws-sdk-pandas.readthedocs.io/en/3.9.1/layers.html
  default = ["arn:aws:lambda:us-east-1:************:layer:AWSSDKPandas-Python312-Arm64:13"]
}

variable "aws_account_id" {
  type        = string
  description = "AWS Account ID"
}
