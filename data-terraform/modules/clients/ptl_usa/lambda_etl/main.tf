terraform {
  required_providers {
    archive = {
      source  = "hashicorp/archive"
      version = "~> 2.0"
    }
    aws = {
      source = "hashicorp/aws"
    }
  }
}

locals {
  mandatory_tags = {
    "Application" = "Data-Warehouse"
    "Author"      = "Data-Team"
    "MultiRegion" = "false"
    "Terraform"   = "true"
    "Product"     = "Data"
    "Squad"       = "Data"
    "Stack"       = "ETL"
    "Terraform"   = "true"
  }

  all_tags = merge(local.mandatory_tags, var.tags)
}

# Create S3 bucket
resource "aws_s3_bucket" "lambda_bucket" {
  bucket = var.s3_bucket_name
  tags   = local.all_tags

}

resource "aws_s3_bucket_public_access_block" "lambda_bucket" {
  bucket                  = aws_s3_bucket.lambda_bucket.id
  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}
resource "aws_s3_bucket_ownership_controls" "lambda_bucket" {
  bucket = aws_s3_bucket.lambda_bucket.id
  rule {
    object_ownership = "BucketOwnerPreferred"
  }
}

resource "aws_s3_bucket_versioning" "lambda_bucket" {
  depends_on = [aws_s3_bucket_ownership_controls.lambda_bucket]
  bucket     = aws_s3_bucket.lambda_bucket.id
  versioning_configuration {
    status = "Enabled"
  }
}

# EventBridge Rule to trigger Lambda every 5 minutes
resource "aws_cloudwatch_event_rule" "every_5_minutes" {
  name                = "trigger-ptl-usa-every-5-minutes"
  schedule_expression = "rate(5 minutes)"
  tags                = local.all_tags
}

# Lambda Function - Lead Car

data "archive_file" "lambda_zip_lead_car" {
  type        = "zip"
  source_dir  = "${path.module}/lambda/lead_car"
  output_path = "${path.module}/lambda/lead_car.zip"
}

resource "aws_s3_object" "lambda_zip_lead_car" {
  bucket      = aws_s3_bucket.lambda_bucket.bucket
  key         = "lambda/lead_car.zip"
  source      = data.archive_file.lambda_zip_lead_car.output_path
  source_hash = data.archive_file.lambda_zip_lead_car.output_base64sha256
}

data "archive_file" "lambda_zip_departure" {
  type        = "zip"
  source_dir  = "${path.module}/lambda/departure"
  output_path = "${path.module}/lambda/departure.zip"
}

resource "aws_s3_object" "lambda_zip_departure" {
  bucket      = aws_s3_bucket.lambda_bucket.bucket
  key         = "lambda/departure.zip"
  source      = data.archive_file.lambda_zip_departure.output_path
  source_hash = data.archive_file.lambda_zip_departure.output_base64sha256
}

data "archive_file" "lambda_zip_dangerzone" {
  type        = "zip"
  source_dir  = "${path.module}/lambda/danger_zone"
  output_path = "${path.module}/lambda/danger_zone.zip"
}

resource "aws_s3_object" "lambda_zip_dangerzone" {
  bucket      = aws_s3_bucket.lambda_bucket.bucket
  key         = "lambda/danger_zone.zip"
  source      = data.archive_file.lambda_zip_dangerzone.output_path
  source_hash = data.archive_file.lambda_zip_dangerzone.output_base64sha256
}

data "archive_file" "lambda_zip_roi" {
  type        = "zip"
  source_dir  = "${path.module}/lambda/roi"
  output_path = "${path.module}/lambda/roi.zip"
}

resource "aws_s3_object" "lambda_zip_roi" {
  bucket      = aws_s3_bucket.lambda_bucket.bucket
  key         = "lambda/roi.zip"
  source      = data.archive_file.lambda_zip_roi.output_path
  source_hash = data.archive_file.lambda_zip_roi.output_base64sha256
}

data "archive_file" "lambda_zip_hvi" {
  type        = "zip"
  source_dir  = "${path.module}/lambda/hvi"
  output_path = "${path.module}/lambda/hvi.zip"
}

resource "aws_s3_object" "lambda_zip_hvi" {
  bucket      = aws_s3_bucket.lambda_bucket.bucket
  key         = "lambda/hvi.zip"
  source      = data.archive_file.lambda_zip_hvi.output_path
  source_hash = data.archive_file.lambda_zip_hvi.output_base64sha256
}

data "archive_file" "lambda_zip_active_departure_rate" {
  type        = "zip"
  source_dir  = "${path.module}/lambda/active_departure_rate"
  output_path = "${path.module}/lambda/active_departure_rate.zip"
}

resource "aws_s3_object" "lambda_zip_active_departure_rate" {
  bucket      = aws_s3_bucket.lambda_bucket.bucket
  key         = "lambda/active_departure_rate.zip"
  source      = data.archive_file.lambda_zip_active_departure_rate.output_path
  source_hash = data.archive_file.lambda_zip_active_departure_rate.output_base64sha256
}

data "archive_file" "lambda_extract_email_attachment" {
  type        = "zip"
  source_dir  = "${path.module}/lambda/extract_email_attachment"
  output_path = "${path.module}/lambda/extract_email_attachment_ptl_usa.zip"
}

resource "aws_s3_object" "lambda_extract_email_attachment" {
  bucket      = aws_s3_bucket.lambda_bucket.bucket
  key         = "lambda/extract_email_attachment_ptl_usa.zip"
  source      = data.archive_file.lambda_extract_email_attachment.output_path
  source_hash = data.archive_file.lambda_extract_email_attachment.output_base64sha256
}

# Deploy CloudFormation Stack
resource "aws_cloudformation_stack" "lambda_application_stack" {
  name = "etl-ptl-usa-sync-stack"
  template_body = jsonencode({
    AWSTemplateFormatVersion = "2010-09-09"
    Description              = "Lambda application for ETL PTL USA Sync"
    Resources = {
      LeadCarFctSyncFunction = {
        Type = "AWS::Lambda::Function"
        Properties = {
          FunctionName  = "etl_ptl_usa_lead_car_fct_sync"
          Handler       = "lambda_function.lambda_handler"
          Role          = { "Fn::GetAtt" = ["LambdaExecutionRole", "Arn"] }
          Runtime       = "python3.12"
          Architectures = ["arm64"]
          Code = {
            S3Bucket        = { "Ref" = "LambdaS3Bucket" }
            S3Key           = "lambda/lead_car.zip"
            S3ObjectVersion = aws_s3_object.lambda_zip_lead_car.version_id
          }
          Timeout = 60
          Environment = {
            Variables = {
              REDSHIFT_CLUSTER_ID = { "Ref" = "RedshiftClusterId" }
              DATABASE            = { "Ref" = "Database" }
              DB_USER             = { "Ref" = "DbUser" }
              SECRET_NAME         = { "Ref" = "SecretName" }
              REGION_NAME         = { "Ref" = "RegionName" }
            }
          }
        }
      }
      DeparturectSyncFunction = {
        Type = "AWS::Lambda::Function"
        Properties = {
          FunctionName  = "etl_ptl_usa_departure_fct_sync"
          Handler       = "lambda_function.lambda_handler"
          Role          = { "Fn::GetAtt" = ["LambdaExecutionRole", "Arn"] }
          Runtime       = "python3.12"
          Architectures = ["arm64"]
          Code = {
            S3Bucket        = { "Ref" = "LambdaS3Bucket" }
            S3Key           = "lambda/departure.zip"
            S3ObjectVersion = aws_s3_object.lambda_zip_departure.version_id
          }
          Timeout = 60
          Environment = {
            Variables = {
              REDSHIFT_CLUSTER_ID = { "Ref" = "RedshiftClusterId" }
              DATABASE            = { "Ref" = "Database" }
              DB_USER             = { "Ref" = "DbUser" }
              SECRET_NAME         = { "Ref" = "SecretName" }
              REGION_NAME         = { "Ref" = "RegionName" }
            }
          }
        }
      }
      DangerZoneSyncFunction = {
        Type = "AWS::Lambda::Function"
        Properties = {
          FunctionName  = "etl_ptl_usa_danger_zone_fct_sync"
          Handler       = "lambda_function.lambda_handler"
          Role          = { "Fn::GetAtt" = ["LambdaExecutionRole", "Arn"] }
          Runtime       = "python3.12"
          Architectures = ["arm64"]
          Code = {
            S3Bucket        = { "Ref" = "LambdaS3Bucket" }
            S3Key           = "lambda/danger_zone.zip"
            S3ObjectVersion = aws_s3_object.lambda_zip_dangerzone.version_id
          }
          Timeout = 60
          Environment = {
            Variables = {
              REDSHIFT_CLUSTER_ID = { "Ref" = "RedshiftClusterId" }
              DATABASE            = { "Ref" = "Database" }
              DB_USER             = { "Ref" = "DbUser" }
              SECRET_NAME         = { "Ref" = "SecretName" }
              REGION_NAME         = { "Ref" = "RegionName" }
            }
          }
        }
      }
      RoiSyncFunction = {
        Type = "AWS::Lambda::Function"
        Properties = {
          FunctionName  = "etl_ptl_usa_roi_fct_sync"
          Handler       = "lambda_function.lambda_handler"
          Role          = { "Fn::GetAtt" = ["LambdaExecutionRole", "Arn"] }
          Runtime       = "python3.12"
          Architectures = ["arm64"]
          Code = {
            S3Bucket        = { "Ref" = "LambdaS3Bucket" }
            S3Key           = "lambda/roi.zip"
            S3ObjectVersion = aws_s3_object.lambda_zip_roi.version_id
          }
          Timeout = 60
          Environment = {
            Variables = {
              REDSHIFT_CLUSTER_ID = { "Ref" = "RedshiftClusterId" }
              DATABASE            = { "Ref" = "Database" }
              DB_USER             = { "Ref" = "DbUser" }
              SECRET_NAME         = { "Ref" = "SecretName" }
              REGION_NAME         = { "Ref" = "RegionName" }
            }
          }
        }
      }
      HviSyncFunction = {
        Type = "AWS::Lambda::Function"
        Properties = {
          FunctionName  = "etl_ptl_usa_hvi_fct_sync"
          Handler       = "lambda_function.lambda_handler"
          Role          = { "Fn::GetAtt" = ["LambdaExecutionRole", "Arn"] }
          Runtime       = "python3.12"
          Architectures = ["arm64"]
          Code = {
            S3Bucket        = { "Ref" = "LambdaS3Bucket" }
            S3Key           = "lambda/hvi.zip"
            S3ObjectVersion = aws_s3_object.lambda_zip_hvi.version_id
          }
          Timeout = 60
          Environment = {
            Variables = {
              REDSHIFT_CLUSTER_ID = { "Ref" = "RedshiftClusterId" }
              DATABASE            = { "Ref" = "Database" }
              DB_USER             = { "Ref" = "DbUser" }
              SECRET_NAME         = { "Ref" = "SecretName" }
              REGION_NAME         = { "Ref" = "RegionName" }
            }
          }
        }
      }
      EmailAttachmentExtract = {
        Type = "AWS::Lambda::Function"
        Properties = {
          FunctionName  = "etl_ptl_usa_extract_email_attachment"
          Handler       = "lambda_function.lambda_handler"
          Role          = { "Fn::GetAtt" = ["LambdaExecutionRole", "Arn"] }
          Runtime       = "python3.12"
          Architectures = ["arm64"]
          Code = {
            S3Bucket        = { "Ref" = "LambdaS3Bucket" }
            S3Key           = "lambda/extract_email_attachment_ptl_usa.zip"
            S3ObjectVersion = aws_s3_object.lambda_extract_email_attachment.version_id
          }
          Timeout = 60
          Layers = var.pandas_layer_arn
        }

      }
      LambdaExecutionRole = {
        Type = "AWS::IAM::Role"
        Properties = {
          AssumeRolePolicyDocument = {
            Version = "2012-10-17"
            Statement = [
              {
                Effect = "Allow"
                Principal = {
                  Service = "lambda.amazonaws.com"
                }
                Action = "sts:AssumeRole"
              }
            ]
          }
          Policies = [
            {
              PolicyName = "LambdaPolicy"
              PolicyDocument = {
                Version = "2012-10-17"
                Statement = [
                  {
                    Effect = "Allow"
                    Action = [
                      "redshift-data:ExecuteStatement",
                      "redshift-data:DescribeStatement",
                      "redshift-data:ListStatements",
                      "redshift:GetClusterCredentials",
                      "secretsmanager:GetSecretValue",
                      "logs:CreateLogGroup",
                      "logs:CreateLogStream",
                      "logs:PutLogEvents",
                      "s3:*",
                      "sns:*",
                      "kms:GenerateDataKey"
                    ]
                    Resource = "*"
                  }
                ]
              }
            }
          ]
        }
      }

      ActiveDepartureLambdaFunction = {
        Type = "AWS::Lambda::Function"
        Properties = {
          FunctionName  = "etl_cfa_usa_active_departure_rate_fct_sync"
          Handler       = "lambda_function.lambda_handler"
          Role          = { "Fn::GetAtt" = ["ActiveDepartureLambdaExecutionRole", "Arn"] }
          Runtime       = "python3.12"
          Architectures = ["arm64"]
          MemorySize    = 512
          Code = {
            S3Bucket        = { "Ref" = "LambdaS3Bucket" }
            S3Key           = "lambda/active_departure_rate.zip"
            S3ObjectVersion = aws_s3_object.lambda_zip_active_departure_rate.version_id
          }
          Timeout = 900
          Environment = {
            Variables = {
              REDSHIFT_CLUSTER_ID = { "Ref" = "RedshiftClusterId" }
              DATABASE            = { "Ref" = "Database" }
              DB_USER             = { "Ref" = "DbUser" }
              SECRET_NAME         = { "Ref" = "SecretName" }
              REGION_NAME         = { "Ref" = "RegionName" }
              IAM_ROLE_ARN        = var.redshift_copy_iam
              S3_BUCKET           = var.parquet_unload_s3_bucket
            }
          }
          Layers = var.pandas_layer_arn
        }
      }

      ActiveDepartureLambdaExecutionRole = {
        Type = "AWS::IAM::Role"
        Properties = {
          AssumeRolePolicyDocument = {
            Version = "2012-10-17"
            Statement = [
              {
                Effect = "Allow"
                Principal = {
                  Service = "lambda.amazonaws.com"
                }
                Action = "sts:AssumeRole"
              }
            ]
          }
          Policies = [
            {
              PolicyName = "LambdaPolicy"
              PolicyDocument = {
                Version = "2012-10-17"
                Statement = [
                  {
                    Effect = "Allow"
                    Action = [
                      "redshift-data:ExecuteStatement",
                      "redshift-data:DescribeStatement",
                      "redshift-data:ListStatements",
                      "redshift:GetClusterCredentials",
                      "redshift-data:GetStatementResult"
                    ]
                    Resource : [
                      "arn:aws:redshift:${var.region_name}:${var.aws_account_id}:cluster:${var.redshift_cluster_id}",
                      "arn:aws:redshift:${var.region_name}:${var.aws_account_id}:dbuser:${var.redshift_cluster_id}/${var.db_user}",
                      "arn:aws:redshift:${var.region_name}:${var.aws_account_id}:dbname:${var.redshift_cluster_id}/${var.database}"
                    ]
                  },

                  {
                    Effect = "Allow"
                    Action = [
                      "redshift-data:DescribeStatement",
                      "redshift-data:GetStatementResult"
                    ]
                    Resource : [
                      "*"
                    ]
                  },

                  {
                    "Effect" : "Allow",
                    "Action" : [
                      "s3:*"
                    ],
                    "Resource" : "arn:aws:s3:::${var.parquet_unload_s3_bucket}/*"
                  },
                  {
                    Effect = "Allow"
                    Action = [
                      "secretsmanager:GetSecretValue",
                      "logs:CreateLogGroup",
                      "logs:CreateLogStream",
                      "logs:PutLogEvents",
                    ]
                    Resource = "*"
                  }
                ]
              }
            }
          ]
        }
      }

      EventBridgeRule = {
        Type = "AWS::Events::Rule"
        Properties = {
          Name               = "trigger-sync-every-5-minutes"
          ScheduleExpression = "rate(5 minutes)"
          Targets = [
            {
              Arn = { "Fn::GetAtt" = ["LeadCarFctSyncFunction", "Arn"] }
              Id  = "LeadCarFctSyncFunction"
            },
            {
              Arn = { "Fn::GetAtt" = ["DeparturectSyncFunction", "Arn"] }
              Id  = "DeparturectSyncFunction"
            },
            {
              Arn = { "Fn::GetAtt" = ["DangerZoneSyncFunction", "Arn"] }
              Id  = "DangerZoneSyncFunction"
            },
            {
              Arn = { "Fn::GetAtt" = ["RoiSyncFunction", "Arn"] }
              Id  = "RoiZoneSyncFunction"
            },
            {
              Arn = { "Fn::GetAtt" = ["HviSyncFunction", "Arn"] }
              Id  = "HviZoneSyncFunction"
            }
          ]
        }
      }
      LambdaInvokePermissionLeadCar = {
        Type = "AWS::Lambda::Permission"
        Properties = {
          FunctionName = { "Ref" = "LeadCarFctSyncFunction" }
          Action       = "lambda:InvokeFunction"
          Principal    = "events.amazonaws.com"
          SourceArn    = { "Fn::GetAtt" = ["EventBridgeRule", "Arn"] }
        }
      }
      LambdaInvokePermissionDeparture = {
        Type = "AWS::Lambda::Permission"
        Properties = {
          FunctionName = { "Ref" = "DeparturectSyncFunction" }
          Action       = "lambda:InvokeFunction"
          Principal    = "events.amazonaws.com"
          SourceArn    = { "Fn::GetAtt" = ["EventBridgeRule", "Arn"] }
        }
      }
      LambdaInvokePermissionDangerZone = {
        Type = "AWS::Lambda::Permission"
        Properties = {
          FunctionName = { "Ref" = "DangerZoneSyncFunction" }
          Action       = "lambda:InvokeFunction"
          Principal    = "events.amazonaws.com"
          SourceArn    = { "Fn::GetAtt" = ["EventBridgeRule", "Arn"] }
        }
      }
      LambdaInvokePermissionRoi = {
        Type = "AWS::Lambda::Permission"
        Properties = {
          FunctionName = { "Ref" = "RoiSyncFunction" }
          Action       = "lambda:InvokeFunction"
          Principal    = "events.amazonaws.com"
          SourceArn    = { "Fn::GetAtt" = ["EventBridgeRule", "Arn"] }
        }
      }
      LambdaInvokePermissionHvi = {
        Type = "AWS::Lambda::Permission"
        Properties = {
          FunctionName = { "Ref" = "HviSyncFunction" }
          Action       = "lambda:InvokeFunction"
          Principal    = "events.amazonaws.com"
          SourceArn    = { "Fn::GetAtt" = ["EventBridgeRule", "Arn"] }
        }
      }

      EventBridgeRule = {
        Type = "AWS::Events::Rule"
        Properties = {
          Name               = "trigger-sync-every-15-minutes"
          ScheduleExpression = "rate(15 minutes)"
          Targets = [
            {
              Arn = { "Fn::GetAtt" = ["ActiveDepartureLambdaFunction", "Arn"] }
              Id  = "ActiveDepartureLambdaFunction"
            }

          ]
        }
      }

      LambdaInvokePermissionActiveDeparture = {
        Type = "AWS::Lambda::Permission"
        Properties = {
          FunctionName = { "Ref" = "ActiveDepartureLambdaFunction" }
          Action       = "lambda:InvokeFunction"
          Principal    = "events.amazonaws.com"
          SourceArn    = { "Fn::GetAtt" = ["EventBridgeRule", "Arn"] }
        }
      }

    }
    Parameters = {
      LambdaS3Bucket = {
        Type        = "String"
        Description = "S3 bucket where the Lambda function code is stored"
      }
      RedshiftClusterId = {
        Type        = "String"
        Description = "Redshift cluster identifier"
      }
      Database = {
        Type        = "String"
        Description = "Database name"
      }
      DbUser = {
        Type        = "String"
        Description = "Database user"
      }
      SecretName = {
        Type        = "String"
        Description = "Secrets Manager secret name"
      }
      RegionName = {
        Type        = "String"
        Description = "AWS region name"
      }
    }
  })

  parameters = {
    LambdaS3Bucket    = aws_s3_bucket.lambda_bucket.bucket
    RedshiftClusterId = var.redshift_cluster_id
    Database          = var.database
    DbUser            = var.db_user
    SecretName        = var.secret_name
    RegionName        = var.region_name
  }
  capabilities      = ["CAPABILITY_IAM"]
  notification_arns = var.notification_arns
  tags              = local.all_tags
}

