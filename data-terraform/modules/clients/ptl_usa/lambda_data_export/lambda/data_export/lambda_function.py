import json
import boto3
import os
import time
from datetime import datetime

# Initialize Redshift Data API client
redshift_data = boto3.client('redshift-data')

# Load environment variables safely
redshift_cluster_id = os.getenv('REDSHIFT_CLUSTER_ID', '')
database_name = os.getenv('DATABASE', '')
db_username = os.getenv('DB_USERNAME', '')
secret_name = os.getenv('SECRET_NAME', '')
aws_region = os.getenv('AWS_REGION_NAME', '')
export_iam_role = os.getenv('EXPORT_IAM_ROLE', '')
export_s3_bucket = os.getenv('EXPORT_S3_BUCKET', '')
export_base_partition = os.getenv('EXPORT_BASE_PARTITION', '')
default_export_sql = os.getenv(
    'EXPORT_SQL',
    'SELECT * FROM eyecue_ptl_usa.mv_journey_weekly_snapshot ORDER BY meta_kinesis_datastream_arrival_datetime_utc'
)

def generate_unload_sql(iam_role: str, s3_bucket: str, base_partition: str, sql_statement: str, parallel: bool = False) -> str:
    """
    Generates the UNLOAD SQL statement for exporting data to S3 with partitioned paths.

    Args:
        iam_role (str): IAM role ARN for the UNLOAD operation.
        s3_bucket (str): S3 bucket where data will be stored.
        base_partition (str): Base partition for the S3 path.
        sql_statement (str): SQL query to export data.
        parallel (bool): Whether to enable parallel UNLOAD. Default is False.

    Returns:
        str: The UNLOAD SQL statement.
    """
    today = datetime.today()
    year = today.strftime('%Y')
    month = today.strftime('%m')
    day = today.strftime('%d')
    parallel_option = "ON" if parallel else "OFF"

    s3_path = f"s3://{s3_bucket}/{base_partition}/year={year}/month={month}/day={day}/"

    unload_sql = f"""
    UNLOAD ('{sql_statement}')
    TO '{s3_path}'
    IAM_ROLE '{iam_role}'
    FORMAT AS CSV HEADER DELIMITER ','
    PARALLEL {parallel_option}
    ALLOWOVERWRITE;
    """
    return unload_sql

def execute_sql_async(sql_statement: str) -> str:
    """
    Executes an SQL statement asynchronously using Redshift Data API.

    Args:
        sql_statement (str): SQL query to execute.

    Returns:
        str: Query execution ID or None on failure.
    """
    try:
        response = redshift_data.execute_statement(
            ClusterIdentifier=redshift_cluster_id,
            Database=database_name,
            DbUser=db_username,
            Sql=sql_statement
        )
        return response.get('Id', None)
    except Exception as e:
        print(f"Error executing SQL asynchronously: {e}")
        return None

def execute_sql_sync(sql_statement: str) -> dict:
    """
    Executes an SQL statement synchronously using Redshift Data API.

    Args:
        sql_statement (str): SQL query to execute.

    Returns:
        dict: Execution status details.
    """
    try:
        response = redshift_data.execute_statement(
            ClusterIdentifier=redshift_cluster_id,
            Database=database_name,
            DbUser=db_username,
            Sql=sql_statement
        )

        query_id = response.get('Id', None)
        if not query_id:
            raise ValueError("Failed to retrieve query ID")

        while True:
            status = redshift_data.describe_statement(Id=query_id)
            if status['Status'] in ['FAILED', 'FINISHED']:
                break
            time.sleep(2)  # Wait before checking again

        if status['Status'] == 'FAILED':
            raise Exception(f"SQL execution failed: {status.get('Error', 'Unknown error')}")

        return status

    except Exception as e:
        print(f"Error executing SQL synchronously: {e}")
        return {"Status": "FAILED", "Error": str(e)}

def lambda_handler(event, context):
    """
    AWS Lambda handler for exporting data from Redshift to S3.
    """
    try:
        print("Starting Data Export Process")

        # Validate environment variables
        missing_vars = [var for var in ["EXPORT_IAM_ROLE", "EXPORT_S3_BUCKET", "EXPORT_BASE_PARTITION"] if not os.getenv(var)]
        if missing_vars:
            raise ValueError(f"Missing required environment variables: {missing_vars}")

        # Generate and execute export SQL
        export_sql = generate_unload_sql(export_iam_role, export_s3_bucket, export_base_partition, default_export_sql)
        query_id = execute_sql_async(export_sql)

        if not query_id:
            raise Exception("Failed to start Redshift export query.")

        print(f'Export Query ID: {query_id}')
        return {'statusCode': 200, 'body': json.dumps('Data Exported Successfully')}

    except Exception as e:
        print(f"Lambda Execution Error: {e}")
        return {'statusCode': 500, 'body': json.dumps(f"Error: {str(e)}")}
