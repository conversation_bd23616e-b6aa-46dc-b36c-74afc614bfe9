variable "resource_tags" {
  type        = map(string)
  default     = {}
  description = "Tags to apply to resources"
}

variable "lambda_s3_bucket" {
  type        = string
  default     = ""
  description = "S3 bucket name for storing Lambda code"
}

variable "redshift_cluster_id" {
  type        = string
  default     = ""
  description = "Redshift cluster identifier"
}

variable "database_name" {
  type        = string
  default     = ""
  description = "Database name in Redshift"
}

variable "db_username" {
  type        = string
  default     = ""
  description = "Database user for connecting to Redshift"
}

variable "secret_name" {
  type        = string
  default     = ""
  description = "Secrets Manager name containing database credentials"
}

variable "aws_region" {
  type        = string
  default     = ""
  description = "AWS region where resources are deployed"
}

variable "export_iam_role_arn" {
  type        = string
  default     = ""
  description = "IAM role ARN with permissions for exporting data"
}

variable "export_s3_bucket_name" {
  type        = string
  default     = ""
  description = "S3 bucket name where exported data will be stored"
}

variable "export_base_partition_key" {
  type        = string
  default     = ""
  description = "Base partition key for data exports"
}

variable "notification_arns" {
  description = "The list of SNS topic ARNs to publish stack related events"
  type        = list(string)
  default     = []
}