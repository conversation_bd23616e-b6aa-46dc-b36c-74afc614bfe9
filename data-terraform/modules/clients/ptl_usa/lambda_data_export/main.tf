terraform {
  required_providers {
    archive = {
      source  = "hashicorp/archive"
      version = "~> 2.0"
    }
    aws = {
      source = "hashicorp/aws"
    }
  }
}


locals {
  mandatory_tags = {
    "Application" = "Data-Warehouse"
    "Author"      = "Data-Team"
    "MultiRegion" = "false"
    "Terraform"   = "true"
    "Product"     = "Data"
    "Squad"       = "Data"
    "Stack"       = "ETL"
    "Terraform"   = "true"
  }

  all_tags = merge(local.mandatory_tags, var.resource_tags)
}

data "archive_file" "lambda_ptl_usa_data_export" {
  type        = "zip"
  source_dir  = "${path.module}/lambda/data_export"
  output_path = "${path.module}/lambda/ptl_usa_data_export.zip"
}

resource "aws_s3_object" "lambda_ptl_usa_data_export" {
  bucket      = var.lambda_s3_bucket
  key         = "lambda/ptl_usa_data_export.zip"
  source      = data.archive_file.lambda_ptl_usa_data_export.output_path
  source_hash = data.archive_file.lambda_ptl_usa_data_export.output_base64sha256
}


resource "aws_cloudformation_stack" "ptl_usa_data_share" {
  name = "ptl-usa-ftp-data-share"
  template_body = jsonencode({
    AWSTemplateFormatVersion = "2010-09-09"
    Description              = "Lambda application for ETL PTL USA data share with customer using FTP"
    Resources = {
      "DataExportToS3" = {
        Type = "AWS::Lambda::Function"
        Properties = {
          FunctionName  = "etl_ptl_usa_s3_data_export_for_ftp"
          Handler       = "lambda_function.lambda_handler"
          Role          = { "Fn::GetAtt" = ["LambdaExecutionRole", "Arn"] }
          Runtime       = "python3.12"
          Architectures = ["arm64"]
          Code = {
            S3Bucket        = var.lambda_s3_bucket
            S3Key           = aws_s3_object.lambda_ptl_usa_data_export.key
            S3ObjectVersion = aws_s3_object.lambda_ptl_usa_data_export.version_id
          }
          Timeout = 600
          Environment = {
            Variables = {
              REDSHIFT_CLUSTER_ID   = var.redshift_cluster_id
              DATABASE              = var.database_name
              DB_USERNAME           = var.db_username
              SECRET_NAME           = var.secret_name
              AWS_REGION_NAME       = var.aws_region
              EXPORT_IAM_ROLE       = var.export_iam_role_arn
              EXPORT_S3_BUCKET      = var.export_s3_bucket_name
              EXPORT_BASE_PARTITION = var.export_base_partition_key
            }
          }
        }
      }
      DataExportEventBridge = {
        Type = "AWS::Events::Rule"
        Properties = {
          Name               = "data_export_event_bridge"
          ScheduleExpression = "cron(0 1 * * ? *)"
          Targets = [
            {
              Arn = { "Fn::GetAtt" = ["DataExportToS3", "Arn"] }
              Id  = "DataExportToS3"
          }]
        }
      }
      LambdaInvokeDataExport = {
        Type = "AWS::Lambda::Permission"
        Properties = {
          FunctionName = { "Ref" = "DataExportToS3" }
          Action       = "lambda:InvokeFunction"
          Principal    = "events.amazonaws.com"
          SourceArn    = { "Fn::GetAtt" = ["DataExportEventBridge", "Arn"] }
        }
      }
      LambdaExecutionRole = {
        Type = "AWS::IAM::Role"
        Properties = {
          AssumeRolePolicyDocument = {
            Version = "2012-10-17"
            Statement = [
              {
                Effect = "Allow"
                Principal = {
                  Service = "lambda.amazonaws.com"
                }
                Action = "sts:AssumeRole"
              }
            ]
          }
          Policies = [
            {
              PolicyName = "LambdaPolicy"
              PolicyDocument = {
                Version = "2012-10-17"
                Statement = [
                  {
                    Effect = "Allow"
                    Action = [
                      "redshift-data:ExecuteStatement",
                      "redshift-data:DescribeStatement",
                      "redshift-data:ListStatements",
                      "redshift:GetClusterCredentials",
                      "secretsmanager:GetSecretValue",
                      "logs:CreateLogGroup",
                      "logs:CreateLogStream",
                      "logs:PutLogEvents"
                    ]
                    Resource = "*"
                  }
                ]
              }
            }
          ]
        }
      }
    }
  })

  capabilities      = ["CAPABILITY_IAM"]
  notification_arns = var.notification_arns
  tags              = local.all_tags
}

