terraform {
  required_providers {
    archive = {
      source  = "hashicorp/archive"
      version = "~> 2.0"
    }
    aws = {
      source = "hashicorp/aws"
    }
  }
}

locals {
  mandatory_tags = {
    "Application" = "Data-Warehouse"
    "Author"      = "Data-Team"
    "MultiRegion" = "false"
    "Terraform"   = "true"
    "Product"     = "Data"
    "Squad"       = "Data"
    "Stack"       = "ETL"
    "Terraform"   = "true"
  }

  all_tags = merge(local.mandatory_tags, var.tags)
}

# Create S3 bucket
resource "aws_s3_bucket" "lambda_bucket" {
  bucket = var.s3_bucket_name
  tags   = local.all_tags

}

resource "aws_s3_bucket_public_access_block" "lambda_bucket" {
  bucket                  = aws_s3_bucket.lambda_bucket.id
  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}
resource "aws_s3_bucket_ownership_controls" "lambda_bucket" {
  bucket = aws_s3_bucket.lambda_bucket.id
  rule {
    object_ownership = "BucketOwnerPreferred"
  }
}

resource "aws_s3_bucket_versioning" "lambda_bucket" {
  depends_on = [aws_s3_bucket_ownership_controls.lambda_bucket]
  bucket     = aws_s3_bucket.lambda_bucket.id
  versioning_configuration {
    status = "Enabled"
  }
}

resource "random_id" "suffix" {
  byte_length = 4
}

# EventBridge Rule to trigger Lambda every 5 minutes
resource "aws_cloudwatch_event_rule" "every_5_minutes" {
  name                = "trigger-cul-usa-every-5-minutes-${random_id.suffix.hex}"
  schedule_expression = "rate(5 minutes)"
  tags                = local.all_tags
}

# Lambda Function - Lead Car

# data "archive_file" "lambda_zip_lead_car" {
#   type        = "zip"
#   source_dir  = "${path.module}/lambda/lead_car"
#   output_path = "${path.module}/lambda/lead_car.zip"
# }

# resource "aws_s3_object" "lambda_zip_lead_car" {
#   bucket      = aws_s3_bucket.lambda_bucket.bucket
#   key         = "lambda/lead_car.zip"
#   source      = data.archive_file.lambda_zip_lead_car.output_path
#   source_hash = data.archive_file.lambda_zip_lead_car.output_base64sha256
# }

# data "archive_file" "lambda_zip_departure" {
#   type        = "zip"
#   source_dir  = "${path.module}/lambda/departure"
#   output_path = "${path.module}/lambda/departure.zip"
# }

# resource "aws_s3_object" "lambda_zip_departure" {
#   bucket      = aws_s3_bucket.lambda_bucket.bucket
#   key         = "lambda/departure.zip"
#   source      = data.archive_file.lambda_zip_departure.output_path
#   source_hash = data.archive_file.lambda_zip_departure.output_base64sha256
# }

data "archive_file" "lambda_zip_queue_zone" {
  type        = "zip"
  source_dir  = "${path.module}/lambda/queue_zone"
  output_path = "${path.module}/lambda/queue_zone.zip"
}

resource "aws_s3_object" "lambda_zip_queue_zone" {
  bucket      = aws_s3_bucket.lambda_bucket.bucket
  key         = "lambda/queue_zone.zip"
  source      = data.archive_file.lambda_zip_queue_zone.output_path
  source_hash = data.archive_file.lambda_zip_queue_zone.output_base64sha256
}

data "archive_file" "lambda_zip_roi" {
  type        = "zip"
  source_dir  = "${path.module}/lambda/roi"
  output_path = "${path.module}/lambda/roi.zip"
}

resource "aws_s3_object" "lambda_zip_roi" {
  bucket      = aws_s3_bucket.lambda_bucket.bucket
  key         = "lambda/roi.zip"
  source      = data.archive_file.lambda_zip_roi.output_path
  source_hash = data.archive_file.lambda_zip_roi.output_base64sha256
}

data "archive_file" "lambda_zip_hvi" {
  type        = "zip"
  source_dir  = "${path.module}/lambda/hvi"
  output_path = "${path.module}/lambda/hvi.zip"
}

resource "aws_s3_object" "lambda_zip_hvi" {
  bucket      = aws_s3_bucket.lambda_bucket.bucket
  key         = "lambda/hvi.zip"
  source      = data.archive_file.lambda_zip_hvi.output_path
  source_hash = data.archive_file.lambda_zip_hvi.output_base64sha256
}


# Deploy CloudFormation Stack
resource "aws_cloudformation_stack" "lambda_application_stack" {
  name = "etl-cul-usa-sync-stack"
  template_body = jsonencode({
    AWSTemplateFormatVersion = "2010-09-09"
    Description              = "Lambda application for ETL CUL USA Sync"
    Resources = {
      # LeadCarFctSyncFunction = {
      #   Type = "AWS::Lambda::Function"
      #   Properties = {
      #     FunctionName  = "etl_cul_usa_lead_car_fct_sync"
      #     Handler       = "lambda_function.lambda_handler"
      #     Role          = { "Fn::GetAtt" = ["LambdaExecutionRole", "Arn"] }
      #     Runtime       = "python3.12"
      #     Architectures = ["arm64"]
      #     Code = {
      #       S3Bucket        = { "Ref" = "LambdaS3Bucket" }
      #       S3Key           = "lambda/lead_car.zip"
      #       S3ObjectVersion = aws_s3_object.lambda_zip_lead_car.version_id
      #     }
      #     Timeout = 60
      #     Environment = {
      #       Variables = {
      #         REDSHIFT_CLUSTER_ID = { "Ref" = "RedshiftClusterId" }
      #         DATABASE            = { "Ref" = "Database" }
      #         DB_USER             = { "Ref" = "DbUser" }
      #         SECRET_NAME         = { "Ref" = "SecretName" }
      #         REGION_NAME         = { "Ref" = "RegionName" }
      #       }
      #     }
      #   }
      # }
      # DepartureFctSyncFunction = {
      #   Type = "AWS::Lambda::Function"
      #   Properties = {
      #     FunctionName  = "etl_cul_usa_departure_fct_sync"
      #     Handler       = "lambda_function.lambda_handler"
      #     Role          = { "Fn::GetAtt" = ["LambdaExecutionRole", "Arn"] }
      #     Runtime       = "python3.12"
      #     Architectures = ["arm64"]
      #     Code = {
      #       S3Bucket        = { "Ref" = "LambdaS3Bucket" }
      #       S3Key           = "lambda/departure.zip"
      #       S3ObjectVersion = aws_s3_object.lambda_zip_departure.version_id
      #     }
      #     Timeout = 60
      #     Environment = {
      #       Variables = {
      #         REDSHIFT_CLUSTER_ID = { "Ref" = "RedshiftClusterId" }
      #         DATABASE            = { "Ref" = "Database" }
      #         DB_USER             = { "Ref" = "DbUser" }
      #         SECRET_NAME         = { "Ref" = "SecretName" }
      #         REGION_NAME         = { "Ref" = "RegionName" }
      #       }
      #     }
      #   }
      # }
      QueueZoneSyncFunction = {
        Type = "AWS::Lambda::Function"
        Properties = {
          FunctionName  = "etl_cul_usa_queue_zone_fct_sync"
          Handler       = "lambda_function.lambda_handler"
          Role          = { "Fn::GetAtt" = ["LambdaExecutionRole", "Arn"] }
          Runtime       = "python3.12"
          Architectures = ["arm64"]
          Code = {
            S3Bucket        = { "Ref" = "LambdaS3Bucket" }
            S3Key           = "lambda/queue_zone.zip"
            S3ObjectVersion = aws_s3_object.lambda_zip_queue_zone.version_id
          }
          Timeout = 60
          Environment = {
            Variables = {
              REDSHIFT_CLUSTER_ID = { "Ref" = "RedshiftClusterId" }
              DATABASE            = { "Ref" = "Database" }
              DB_USER             = { "Ref" = "DbUser" }
              SECRET_NAME         = { "Ref" = "SecretName" }
              REGION_NAME         = { "Ref" = "RegionName" }
            }
          }
        }
      }
      RoiSyncFunction = {
        Type = "AWS::Lambda::Function"
        Properties = {
          FunctionName  = "etl_cul_usa_roi_fct_sync"
          Handler       = "lambda_function.lambda_handler"
          Role          = { "Fn::GetAtt" = ["LambdaExecutionRole", "Arn"] }
          Runtime       = "python3.12"
          Architectures = ["arm64"]
          Code = {
            S3Bucket        = { "Ref" = "LambdaS3Bucket" }
            S3Key           = "lambda/roi.zip"
            S3ObjectVersion = aws_s3_object.lambda_zip_roi.version_id
          }
          Timeout = 60
          Environment = {
            Variables = {
              REDSHIFT_CLUSTER_ID = { "Ref" = "RedshiftClusterId" }
              DATABASE            = { "Ref" = "Database" }
              DB_USER             = { "Ref" = "DbUser" }
              SECRET_NAME         = { "Ref" = "SecretName" }
              REGION_NAME         = { "Ref" = "RegionName" }
            }
          }
        }
      }
      HviSyncFunction = {
        Type = "AWS::Lambda::Function"
        Properties = {
          FunctionName  = "etl_cul_usa_hvi_fct_sync"
          Handler       = "lambda_function.lambda_handler"
          Role          = { "Fn::GetAtt" = ["LambdaExecutionRole", "Arn"] }
          Runtime       = "python3.12"
          Architectures = ["arm64"]
          Code = {
            S3Bucket        = { "Ref" = "LambdaS3Bucket" }
            S3Key           = "lambda/hvi.zip"
            S3ObjectVersion = aws_s3_object.lambda_zip_hvi.version_id
          }
          Timeout = 60
          Environment = {
            Variables = {
              REDSHIFT_CLUSTER_ID = { "Ref" = "RedshiftClusterId" }
              DATABASE            = { "Ref" = "Database" }
              DB_USER             = { "Ref" = "DbUser" }
              SECRET_NAME         = { "Ref" = "SecretName" }
              REGION_NAME         = { "Ref" = "RegionName" }
            }
          }
        }
      }
      LambdaExecutionRole = {
        Type = "AWS::IAM::Role"
        Properties = {
          AssumeRolePolicyDocument = {
            Version = "2012-10-17"
            Statement = [
              {
                Effect = "Allow"
                Principal = {
                  Service = "lambda.amazonaws.com"
                }
                Action = "sts:AssumeRole"
              }
            ]
          }
          Policies = [
            {
              PolicyName = "LambdaPolicy"
              PolicyDocument = {
                Version = "2012-10-17"
                Statement = [
                  {
                    Effect = "Allow"
                    Action = [
                      "redshift-data:ExecuteStatement",
                      "redshift-data:DescribeStatement",
                      "redshift-data:ListStatements",
                      "redshift:GetClusterCredentials",
                      "secretsmanager:GetSecretValue",
                      "logs:CreateLogGroup",
                      "logs:CreateLogStream",
                      "logs:PutLogEvents"
                    ]
                    Resource = "*"
                  }
                ]
              }
            }
          ]
        }
      }
      EventBridgeRule = {
        Type = "AWS::Events::Rule"
        Properties = {
          Name               = "trigger-cul-usa-every-5-minutes"
          ScheduleExpression = "rate(5 minutes)"
          Targets = [
            # {
            #   Arn = { "Fn::GetAtt" = ["LeadCarFctSyncFunction", "Arn"] }
            #   Id  = "LeadCarFctSyncFunction"
            # },
            # {
            #   Arn = { "Fn::GetAtt" = ["DeparturectSyncFunction", "Arn"] }
            #   Id  = "DeparturectSyncFunction"
            # },
            {
              Arn = { "Fn::GetAtt" = ["QueueZoneSyncFunction", "Arn"] }
              Id  = "QueueZoneSyncFunction"
            },
            {
              Arn = { "Fn::GetAtt" = ["RoiSyncFunction", "Arn"] }
              Id  = "RoiZoneSyncFunction"
            },
            {
              Arn = { "Fn::GetAtt" = ["HviSyncFunction", "Arn"] }
              Id  = "HviZoneSyncFunction"
            }
          ]
        }
      }
      # LambdaInvokePermissionLeadCar = {
      #   Type = "AWS::Lambda::Permission"
      #   Properties = {
      #     FunctionName = { "Ref" = "LeadCarFctSyncFunction" }
      #     Action       = "lambda:InvokeFunction"
      #     Principal    = "events.amazonaws.com"
      #     SourceArn    = { "Fn::GetAtt" = ["EventBridgeRule", "Arn"] }
      #   }
      # }
      # LambdaInvokePermissionDeparture = {
      #   Type = "AWS::Lambda::Permission"
      #   Properties = {
      #     FunctionName = { "Ref" = "DeparturectSyncFunction" }
      #     Action       = "lambda:InvokeFunction"
      #     Principal    = "events.amazonaws.com"
      #     SourceArn    = { "Fn::GetAtt" = ["EventBridgeRule", "Arn"] }
      #   }
      # }
      LambdaInvokePermissionQueueZone = {
        Type = "AWS::Lambda::Permission"
        Properties = {
          FunctionName = { "Ref" = "QueueZoneSyncFunction" }
          Action       = "lambda:InvokeFunction"
          Principal    = "events.amazonaws.com"
          SourceArn    = { "Fn::GetAtt" = ["EventBridgeRule", "Arn"] }
        }
      }
      LambdaInvokePermissionRoi = {
        Type = "AWS::Lambda::Permission"
        Properties = {
          FunctionName = { "Ref" = "RoiSyncFunction" }
          Action       = "lambda:InvokeFunction"
          Principal    = "events.amazonaws.com"
          SourceArn    = { "Fn::GetAtt" = ["EventBridgeRule", "Arn"] }
        }
      }
      LambdaInvokePermissionHvi = {
        Type = "AWS::Lambda::Permission"
        Properties = {
          FunctionName = { "Ref" = "HviSyncFunction" }
          Action       = "lambda:InvokeFunction"
          Principal    = "events.amazonaws.com"
          SourceArn    = { "Fn::GetAtt" = ["EventBridgeRule", "Arn"] }
        }
      }
    }
    Parameters = {
      LambdaS3Bucket = {
        Type        = "String"
        Description = "S3 bucket where the Lambda function code is stored"
      }
      RedshiftClusterId = {
        Type        = "String"
        Description = "Redshift cluster identifier"
      }
      Database = {
        Type        = "String"
        Description = "Database name"
      }
      DbUser = {
        Type        = "String"
        Description = "Database user"
      }
      SecretName = {
        Type        = "String"
        Description = "Secrets Manager secret name"
      }
      RegionName = {
        Type        = "String"
        Description = "AWS region name"
      }
    }
  })

  parameters = {
    LambdaS3Bucket    = aws_s3_bucket.lambda_bucket.bucket
    RedshiftClusterId = var.redshift_cluster_id
    Database          = var.database
    DbUser            = var.db_user
    SecretName        = var.secret_name
    RegionName        = var.region_name
  }
  capabilities      = ["CAPABILITY_IAM"]
  notification_arns = length(var.notification_arns) > 0 ? var.notification_arns : null
  tags              = local.all_tags
}

