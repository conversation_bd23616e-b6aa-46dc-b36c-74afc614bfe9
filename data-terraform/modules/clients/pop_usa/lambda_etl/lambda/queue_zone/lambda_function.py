import json
import boto3
import time
import os

# Initialize Redshift Data API client
redshift_data = boto3.client("redshift-data")

# Define the Redshift cluster and database details from environment variables
REDSHIFT_CLUSTER_ID = os.environ["REDSHIFT_CLUSTER_ID"]
DATABASE = os.environ["DATABASE"]
DB_USER = os.environ["DB_USER"]
SECRET_NAME = os.environ["SECRET_NAME"]
REGION_NAME = os.environ["REGION_NAME"]

# Define the SQL queries
REFRESH_MV_SQL = "refresh MATERIALIZED view eyecue_pop_usa.mv_last_1_day_queue_zone;"
INSERT_SQL = """
INSERT INTO eyecue_pop_usa.fct_queue_zone (
        record_hash_id,
        fingermark_id,
        event_type,
        version,
        functional_zone,
        queue_level,
        queuing_time_ms,
        datetime_utc,
        meta_kinesis_datastream_arrival_datetime_utc
    )
select
    hash_key,
    fingermark_id,
    event_type,
    version,
    functional_zone,
    queue_level,
    queuing_time_ms,
    datetime_utc,
    meta_kinesis_datastream_arrival_datetime_utc
from
    (
        WITH latest_record AS (
            SELECT
                MAX(meta_kinesis_datastream_arrival_datetime_utc) AS latest_datetime
            FROM
                prod.eyecue_pop_usa.fct_queue_zone
        )
        SELECT
            t.hash_key,
            t.payload.store_id:: text AS fingermark_id,
            't.payload.event_type:: text' AS event_type,
            t.payload.version:: FLOAT8 AS version,
            key:: text AS functional_zone,
            value.queue_level:: INT AS queue_level,
            ROUND(value.queuing_time * 1000):: INT AS queuing_time_ms,
            value.time_utc:: timestamptz AS datetime_utc,
            t.meta_kinesis_datastream_arrival_datetime_utc
        FROM
            eyecue_pop_usa.mv_last_1_day_queue_zone AS t,
            UNPIVOT t.functional_zones AS value AT key
        WHERE
            t.meta_kinesis_datastream_arrival_datetime_utc > (
                SELECT
                    latest_datetime
                FROM
                    latest_record
            )
    );
"""


def execute_sql(sql_statement):
    """
    Function to execute an SQL statement using the Redshift Data API.
    """
    response = redshift_data.execute_statement(
        ClusterIdentifier=REDSHIFT_CLUSTER_ID,
        Database=DATABASE,
        DbUser=DB_USER,
        Sql=sql_statement,
    )

    # Check the execution id for the SQL statement
    query_id = response["Id"]

    # Wait for query to complete
    status = redshift_data.describe_statement(Id=query_id)
    while status["Status"] in ["SUBMITTED", "PICKED", "STARTED"]:
        time.sleep(1)  # Wait before checking the status again
        status = redshift_data.describe_statement(Id=query_id)

    # Check for errors
    if status["Status"] == "FAILED":
        raise Exception(f"SQL execution failed: {status['Error']}")

    return status


def lambda_handler(event, context):
    try:
        # Step 1: Refresh Materialized View
        print("Refreshing Materialized View...")
        refresh_status = execute_sql(REFRESH_MV_SQL)
        print("Materialized View Refreshed Successfully.")

        # Step 2: Insert Data into Fact Table
        print("Inserting Data into Fact Table...")
        insert_status = execute_sql(INSERT_SQL)
        print("Data Inserted Successfully.")

        return {
            "statusCode": 200,
            "body": json.dumps(
                "Materialized View Refreshed and Data Inserted Successfully!"
            ),
        }

    except Exception as e:
        print(f"Error executing SQL: {e}")
        return {"statusCode": 500, "body": json.dumps(f"Error: {str(e)}")}
