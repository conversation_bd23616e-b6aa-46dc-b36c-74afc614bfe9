import json
import boto3
import os

# Initialize Redshift Data API client
redshift_data = boto3.client("redshift-data")

# Define the Redshift cluster and database details from environment variables
REDSHIFT_CLUSTER_ID = os.environ["REDSHIFT_CLUSTER_ID"]
DATABASE = os.environ["DATABASE"]
DB_USER = os.environ["DB_USER"]
SECRET_NAME = os.environ["SECRET_NAME"]
REGION_NAME = os.environ["REGION_NAME"]

# Define the SQL queries
REFRESH_MV_START_SQL = (
    "refresh MATERIALIZED view eyecue_mcd_can.mv_last_1_day_hvi_start_events;"
)
REFRESH_MV_END_SQL = (
    "refresh MATERIALIZED view eyecue_mcd_can.mv_last_1_day_hvi_end_events;"
)
INSERT_SQL = """
BEGIN;
-- Step 1: Update existing records in the fact table
UPDATE eyecue_mcd_can.fct_hvi_events
SET
    hash_id = MD5(CONCAT(CONCAT(COALESCE(ts.fingermark_id, te.fingermark_id, ''), COALESCE(ts.event_id, te.event_id, '')), COALESCE(ts.start_datetime_utc, te.end_datetime_utc))),
    fingermark_id = COALESCE(ts.fingermark_id, te.fingermark_id),
    event_id = COALESCE(ts.event_id, te.event_id, ''),
    event_type = COALESCE(ts.event_type, te.event_type),
    vehicle_id = COALESCE(ts.vehicle_id, te.vehicle_id),
    start_datetime_utc = ts.start_datetime_utc,
    end_datetime_utc = te.end_datetime_utc,
    start_datetime_local = ts.start_datetime_local,
    end_datetime_local = te.end_datetime_local,
    camera_detection_datetime_utc = ts.camera_detection_datetime_utc,
    camera_id = COALESCE(ts.camera_id, te.camera_id),
    roi_id = COALESCE(ts.roi_id, te.roi_id),
    roi_id_match = CASE WHEN ts.roi_id = te.roi_id THEN TRUE ELSE FALSE END,
    roi_type = COALESCE(ts.roi_type, te.roi_type),
    roi_type_match = CASE WHEN ts.roi_type = te.roi_type THEN TRUE ELSE FALSE END,
    total_time = GREATEST(ts.total_time, te.total_time),
    start_confidence = ts.start_confidence,
    end_confidence = te.end_confidence,
    start_frames_life = ts.start_frames_life,
    end_frames_life = te.end_frames_life,
    vehicle_type = COALESCE(ts.vehicle_type, te.vehicle_type),
    lane_number = COALESCE(ts.lane_number, te.lane_number),
    graph_order = ts.graph_order,
    overtaken = GREATEST(ts.overtaken, te.overtaken),
    valid_journey = CASE WHEN ts.valid_journey IS TRUE AND te.valid_journey IS TRUE THEN TRUE ELSE FALSE END,
    tracker_version = COALESCE(ts.tracker_version, te.tracker_version),
    eyeq_server_version = COALESCE(ts.eyeq_server_version, te.eyeq_server_version),
    record_creation_time_utc = GETDATE(),
    meta_kinesis_datastream_arrival_datetime_utc = COALESCE(ts.meta_kinesis_datastream_arrival_datetime_utc, te.meta_kinesis_datastream_arrival_datetime_utc)
FROM 
    eyecue_mcd_can.mv_last_1_day_hvi_start_events ts
FULL OUTER JOIN 
    eyecue_mcd_can.mv_last_1_day_hvi_end_events te
ON 
    ts.event_id = te.event_id
WHERE 
    eyecue_mcd_can.fct_hvi_events.event_id = COALESCE(ts.event_id, te.event_id)
    AND COALESCE(ts.meta_kinesis_datastream_arrival_datetime_utc, te.meta_kinesis_datastream_arrival_datetime_utc) > eyecue_mcd_can.fct_hvi_events.meta_kinesis_datastream_arrival_datetime_utc;

-- Step 2: Insert new records
INSERT INTO eyecue_mcd_can.fct_hvi_events (
    hash_id,
    fingermark_id,
    event_id,
    event_type,
    vehicle_id,
    start_datetime_utc,
    end_datetime_utc,
    start_datetime_local,
    end_datetime_local,
    camera_detection_datetime_utc,
    camera_id,
    roi_id,
    roi_id_match,
    roi_type,
    roi_type_match,
    total_time,
    start_confidence,
    end_confidence,
    start_frames_life,
    end_frames_life,
    vehicle_type,
    lane_number,
    graph_order,
    overtaken,
    valid_journey,
    tracker_version,
    eyeq_server_version,
    record_creation_time_utc,
    meta_kinesis_datastream_arrival_datetime_utc
)
SELECT 
    MD5(CONCAT(CONCAT(COALESCE(ts.fingermark_id, te.fingermark_id, ''), COALESCE(ts.event_id, te.event_id, '')), COALESCE(ts.start_datetime_utc, te.end_datetime_utc))) as hash_id,
    COALESCE(ts.fingermark_id, te.fingermark_id),
    COALESCE(ts.event_id, te.event_id, ''),
    COALESCE(ts.event_type, te.event_type),
    COALESCE(ts.vehicle_id, te.vehicle_id),
    ts.start_datetime_utc,
    te.end_datetime_utc,
    ts.start_datetime_local,
    te.end_datetime_local,
    ts.camera_detection_datetime_utc,
    COALESCE(ts.camera_id, te.camera_id),
    COALESCE(ts.roi_id, te.roi_id),
    CASE WHEN ts.roi_id = te.roi_id THEN TRUE ELSE FALSE END,
    COALESCE(ts.roi_type, te.roi_type),
    CASE WHEN ts.roi_type = te.roi_type THEN TRUE ELSE FALSE END,
    GREATEST(ts.total_time, te.total_time),
    ts.start_confidence,
    te.end_confidence,
    ts.start_frames_life,
    te.end_frames_life,
    COALESCE(ts.vehicle_type, te.vehicle_type),
    COALESCE(ts.lane_number, te.lane_number),
    ts.graph_order,
    GREATEST(ts.overtaken, te.overtaken),
    CASE WHEN ts.valid_journey IS TRUE AND te.valid_journey IS TRUE THEN TRUE ELSE FALSE END,
    COALESCE(ts.tracker_version, te.tracker_version),
    COALESCE(ts.eyeq_server_version, te.eyeq_server_version),
    GETDATE(),
    COALESCE(ts.meta_kinesis_datastream_arrival_datetime_utc, te.meta_kinesis_datastream_arrival_datetime_utc)
FROM 
    eyecue_mcd_can.mv_last_1_day_hvi_start_events ts
FULL OUTER JOIN 
    eyecue_mcd_can.mv_last_1_day_hvi_end_events te
ON 
    ts.event_id = te.event_id
WHERE 
    COALESCE(ts.event_id, te.event_id) NOT IN (SELECT event_id FROM eyecue_mcd_can.fct_hvi_events);
COMMIT;
"""


def execute_sql(sql_statement):
    """
    Function to execute an SQL statement using the Redshift Data API.
    """
    response = redshift_data.execute_statement(
        ClusterIdentifier=REDSHIFT_CLUSTER_ID,
        Database=DATABASE,
        DbUser=DB_USER,
        Sql=sql_statement,
    )

    # Check the execution id for the SQL statement
    query_id = response["Id"]

    # Wait for query to complete
    status = redshift_data.describe_statement(Id=query_id)
    while status["Status"] in ["SUBMITTED", "PICKED", "STARTED"]:
        # time.sleep(1)  # Wait before checking the status again
        status = redshift_data.describe_statement(Id=query_id)

    # Check for errors
    if status["Status"] == "FAILED":
        raise Exception(f"SQL execution failed: {status['Error']}")

    return status


def lambda_handler(event, context):
    try:
        # Step 1: Refresh Materialized View
        print("Refreshing Materialized Views...")
        refresh_status = execute_sql(REFRESH_MV_START_SQL)
        print("Materialized View Start HVI Events Refreshed Successfully.")
        refresh_status = execute_sql(REFRESH_MV_END_SQL)
        print("Materialized View End HVI Events Refreshed Successfully.")

        # Step 2: Insert Data into Fact Table
        print("Inserting Data into Fact Table...")
        insert_status = execute_sql(INSERT_SQL)
        print("Data Inserted Successfully.")

        return {
            "statusCode": 200,
            "body": json.dumps(
                "Materialized Views Refreshed and Data Inserted Successfully!"
            ),
        }

    except Exception as e:
        print(f"Error executing SQL: {e}")
        return {"statusCode": 500, "body": json.dumps(f"Error: {str(e)}")}
