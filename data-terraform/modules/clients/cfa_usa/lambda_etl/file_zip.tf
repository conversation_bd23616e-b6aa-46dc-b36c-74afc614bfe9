
data "archive_file" "lambda_zip_hvi_roi_reporting" {
  type        = "zip"
  source_dir  = "${path.module}/lambda/hvi_roi_reporting"
  output_path = "${path.module}/lambda/hvi_roi_reporting.zip"
}

resource "aws_s3_object" "lambda_zip_hvi_roi_reporting" {
  bucket      = aws_s3_bucket.lambda_bucket.bucket
  key         = "lambda/hvi_roi_reporting.zip"
  source      = data.archive_file.lambda_zip_hvi_roi_reporting.output_path
  source_hash = data.archive_file.lambda_zip_hvi_roi_reporting.output_base64sha256
}


data "archive_file" "lambda_zip_fct_mark_deliver_end" {
  type        = "zip"
  source_dir  = "${path.module}/lambda/fct_mark_deliver_end_etl"
  output_path = "${path.module}/lambda/fct_mark_deliver_end_etl.zip"
}

resource "aws_s3_object" "lambda_zip_fct_mark_deliver_end" {
  bucket      = aws_s3_bucket.lambda_bucket.bucket
  key         = "lambda/fct_mark_deliver_end_etl.zip"
  source      = data.archive_file.lambda_zip_fct_mark_deliver_end.output_path
  source_hash = data.archive_file.lambda_zip_fct_mark_deliver_end.output_base64sha256
}


data "archive_file" "lambda_zip_fct_active_departure_rate" {
  type        = "zip"
  source_dir  = "${path.module}/lambda/fct_active_departure_rate"
  output_path = "${path.module}/lambda/fct_active_departure_rate.zip"
}

resource "aws_s3_object" "lambda_zip_fct_active_departure_rate" {
  bucket      = aws_s3_bucket.lambda_bucket.bucket
  key         = "lambda/fct_active_departure_rate.zip"
  source      = data.archive_file.lambda_zip_fct_active_departure_rate.output_path
  source_hash = data.archive_file.lambda_zip_fct_active_departure_rate.output_base64sha256
}

data "archive_file" "lambda_zip_fct_departure_enhanced_for_heartbeat" {
  type        = "zip"
  source_dir  = "${path.module}/lambda/fct_departure_enhanced_for_heartbeat"
  output_path = "${path.module}/lambda/fct_departure_enhanced_for_heartbeat.zip"
}

resource "aws_s3_object" "lambda_zip_fct_departure_enhanced_for_heartbeat" {
  bucket      = aws_s3_bucket.lambda_bucket.bucket
  key         = "lambda/fct_departure_enhanced_for_heartbeat.zip"
  source      = data.archive_file.lambda_zip_fct_departure_enhanced_for_heartbeat.output_path
  source_hash = data.archive_file.lambda_zip_fct_departure_enhanced_for_heartbeat.output_base64sha256
}
