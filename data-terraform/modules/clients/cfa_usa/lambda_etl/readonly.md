# Terraform Module Documentation

## Overview

This Terraform module is designed to deploy multiple AWS Lambda functions that interact with an Amazon Redshift cluster. The Lambda functions are triggered every 5 minutes using an EventBridge rule. The module also includes the necessary IAM roles and policies, and uses CloudFormation to manage the Lambda functions.

## Providers

- **archive**: Used to create ZIP archives of the Lambda function code.

## Local Variables

- **mandatory_tags**: A set of mandatory tags applied to all resources.
- **all_tags**: A merged set of mandatory tags and user-defined tags.

## Resources

- **aws_s3_bucket.lambda_bucket**: Creates an S3 bucket to store the Lambda function code.
- **aws_cloudwatch_event_rule.every_5_minutes**: Creates an EventBridge rule to trigger the Lambda functions every 5 minutes.

## Lambda Functions

The module includes the following Lambda functions, each packaged as a ZIP file and uploaded to the S3 bucket:

- ### Lead Car

  - **data.archive_file.lambda_zip_lead_car**: Creates a ZIP archive of the Lead Car Lambda function code.
  - **aws_s3_object.lambda_zip_lead_car**: Uploads the ZIP archive to the S3 bucket.

## CloudFormation Stack

The module uses a CloudFormation stack to manage the Lambda functions and their associated resources:

- **aws_cloudformation_stack.lambda_application_stack**: Deploys the CloudFormation stack with the following resources:
  - **Lambda Functions**: Defines the Lambda functions for Lead Car, Departure, Danger Zone, ROI, and HVI.
  - **IAM Role**: Defines the IAM role and policies required for the Lambda functions.
  - **EventBridge Rule** : Defines the EventBridge rule to trigger the Lambda functions every 5 minutes.
  - **Lambda Permissions**: Grants EventBridge permission to invoke the Lambda functions.

## Parameters

The CloudFormation stack requires the following parameters:

- **LambdaS3Bucket**: The S3 bucket where the Lambda function code is stored.
- **RedshiftClusterId**: The Redshift cluster identifier.
- **Database**: The database name.
- **DbUser**: The database user.
- **SecretName**: The Secrets Manager secret name.
- **RegionName**: The AWS region name.
  Tags
  All resources are tagged with the merged set of mandatory and user-defined tags.

## Example Usage

```
module "etl_ptl_usa_sync" {
  source = "./path/to/module"

  s3_bucket_name      = "my-lambda-bucket"
  redshift_cluster_id = "my-redshift-cluster"
  database            = "my-database"
  db_user             = "my-db-user"
  secret_name         = "my-secret-name"
  region_name         = "us-west-2"
  tags = {
    "Environment" = "production"
    "Team"        = "data-team"
  }
}
```
