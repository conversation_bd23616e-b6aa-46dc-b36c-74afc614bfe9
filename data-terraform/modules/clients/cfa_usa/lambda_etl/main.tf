terraform {
  required_providers {
    archive = {
      source  = "hashicorp/archive"
      version = "~> 2.0"
    }
    aws = {
      source = "hashicorp/aws"
    }
  }
}

locals {
  mandatory_tags = {
    "Application" = "Data-Warehouse"
    "Author"      = "Data-Team"
    "MultiRegion" = "false"
    "Terraform"   = "true"
    "Product"     = "Data"
    "Squad"       = "Data"
    "Stack"       = "ETL"
    "Terraform"   = "true"
  }

  all_tags = merge(local.mandatory_tags, var.tags)
}
# Create S3 bucket
resource "aws_s3_bucket" "lambda_bucket" {
  bucket = var.s3_bucket_name
  tags   = local.all_tags
}

resource "aws_s3_bucket_public_access_block" "lambda_bucket" {
  bucket                  = aws_s3_bucket.lambda_bucket.id
  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}
resource "aws_s3_bucket_ownership_controls" "lambda_bucket" {
  bucket = aws_s3_bucket.lambda_bucket.id
  rule {
    object_ownership = "BucketOwnerPreferred"
  }
}

resource "aws_s3_bucket_versioning" "lambda_bucket" {
  depends_on = [aws_s3_bucket_ownership_controls.lambda_bucket]
  bucket     = aws_s3_bucket.lambda_bucket.id
  versioning_configuration {
    status = "Enabled"
  }
}


# Deploy CloudFormation Stack
resource "aws_cloudformation_stack" "lambda_application_stack" {
  name = "etl-cfa-usa-sync-stack"
  template_body = jsonencode({
    AWSTemplateFormatVersion = "2010-09-09"
    Description              = "Lambda application for ETL CFA USA Sync"
    Resources = {
      HviRoiReportDataGenerationFunction = {
        Type = "AWS::Lambda::Function"
        Properties = {
          FunctionName  = "etl_cfa_usa_hvi_roi_reporting_fct_sync"
          Handler       = "lambda_function.lambda_handler"
          Role          = { "Fn::GetAtt" = ["LambdaExecutionRole", "Arn"] }
          Runtime       = "python3.12"
          Architectures = ["arm64"]
          MemorySize    = 4096
          Code = {
            S3Bucket        = { "Ref" = "LambdaS3Bucket" }
            S3Key           = "lambda/hvi_roi_reporting.zip"
            S3ObjectVersion = aws_s3_object.lambda_zip_hvi_roi_reporting.version_id
          }
          Timeout = 600
          Environment = {
            Variables = {
              REDSHIFT_CLUSTER_ID                  = var.redshift_cluster_id
              DATABASE                             = var.database
              DB_USER                              = { "Ref" = "DbUser" }
              SECRET_NAME                          = { "Ref" = "SecretName" }
              REGION_NAME                          = { "Ref" = "RegionName" }
              REDSHIFT_COPY_IAM                    = { "Ref" = "RedshiftCopyIam" }
              DYNAMODB_TABLE_NAME                  = { "Ref" = "DynamoDbTableName" }
              DATA_SELECT_GAP_HVI_ROI_MINS         = { "Ref" = "DataSelectGapHviRoiMins" }
              DUPLICATE_CHECK_GAP_HVI_ROI_MINS     = { "Ref" = "DuplicateCheckGapHviRoiMins" }
              NEXT_EXECUTION_DATE_GAP_HVI_ROI_MINS = { "Ref" = "NextExecutionDateGapHviRoiMins" }
              DESTINATION_SCHEMA_HVI_ROI_REPORTING = { "Ref" = "DestinationSchemaHviRoiReporting" }
              DESTINATION_TABLE_HVI_ROI_REPORTING  = { "Ref" = "DestinationTableHviRoiReporting" }
              S3_BUCKET                            = { "Ref" = "RedshiftCopyDataS3Bucket" }
            }
          }
          Layers = var.pandas_layer_arn
        }
      }
      ActiveDepartureLambdaFunction = {
        Type = "AWS::Lambda::Function"
        Properties = {
          FunctionName  = "etl_cfa_usa_active_departure"
          Handler       = "lambda_function.lambda_handler"
          Role          = { "Fn::GetAtt" = ["LambdaExecutionRole", "Arn"] }
          Runtime       = "python3.12"
          Architectures = ["arm64"]
          MemorySize    = 128
          Code = {
            S3Bucket        = { "Ref" = "LambdaS3Bucket" }
            S3Key           = "lambda/fct_active_departure_rate.zip"
            S3ObjectVersion = aws_s3_object.lambda_zip_fct_active_departure_rate.version_id
          }
          Timeout = 300
          Environment = {
            Variables = {
              SECRET_NAME = { "Ref" = "SecretName" }
              REGION_NAME = { "Ref" = "RegionName" }
            }
          }
          Layers = var.pandas_layer_arn
        }
      }
      LambdaExecutionRole = {
        Type = "AWS::IAM::Role"
        Properties = {
          AssumeRolePolicyDocument = {
            Version = "2012-10-17"
            Statement = [
              {
                Effect = "Allow"
                Principal = {
                  Service = "lambda.amazonaws.com"
                }
                Action = "sts:AssumeRole"
              }
            ]
          }
          Policies = [
            {
              PolicyName = "LambdaPolicy"
              PolicyDocument = {
                Version = "2012-10-17"
                Statement = [
                  {
                    Effect = "Allow"
                    Action = [
                      "redshift-data:ExecuteStatement",
                      "redshift-data:DescribeStatement",
                      "redshift-data:ListStatements",
                      "redshift:GetClusterCredentials",
                      "redshift-data:GetStatementResult",
                      "secretsmanager:GetSecretValue",
                      "logs:CreateLogGroup",
                      "logs:CreateLogStream",
                      "logs:PutLogEvents",
                      "dynamodb:GetItem",
                      "dynamodb:PutItem",
                      "s3:PutObject"
                    ]
                    Resource = "*"
                  }
                ]
              }
            }
          ]
        }
      }
      EventBridgeRule = {
        Type = "AWS::Events::Rule"
        Properties = {
          Name               = "trigger-hvi-roi-reporting-generation-every-10-minutes"
          ScheduleExpression = "rate(10 minutes)"
          Targets = [
            {
              Arn = { "Fn::GetAtt" = ["HviRoiReportDataGenerationFunction", "Arn"] }
              Id  = "HviRoiReportDataGenerationFunction"
            },
            {
              Arn = { "Fn::GetAtt" = ["ActiveDepartureLambdaFunction", "Arn"] }
              Id  = "ActiveDepartureLambdaFunction"
            }
          ]
        }
      }
      LambdaInvokePermissionHviRoiReporting = {
        Type = "AWS::Lambda::Permission"
        Properties = {
          FunctionName = { "Ref" = "HviRoiReportDataGenerationFunction" }
          Action       = "lambda:InvokeFunction"
          Principal    = "events.amazonaws.com"
          SourceArn    = { "Fn::GetAtt" = ["EventBridgeRule", "Arn"] }
        }
      }
      LambdaInvokePermissionActiveDeparture = {
        Type = "AWS::Lambda::Permission"
        Properties = {
          FunctionName = { "Ref" = "ActiveDepartureLambdaFunction" }
          Action       = "lambda:InvokeFunction"
          Principal    = "events.amazonaws.com"
          SourceArn    = { "Fn::GetAtt" = ["EventBridgeRule", "Arn"] }
        }
      }

      FctMarkDeliverEndFunction = {
        Type = "AWS::Lambda::Function"
        Properties = {
          FunctionName  = "etl_cfa_usa_fct_mark_deliver_end"
          Handler       = "lambda_function.lambda_handler"
          Role          = { "Fn::GetAtt" = ["LambdaExecutionRoleForFctMarkDeliverEndFunction", "Arn"] }
          Runtime       = "python3.12"
          Architectures = ["arm64"]
          MemorySize    = 128
          Code = {
            S3Bucket        = { "Ref" = "LambdaS3Bucket" }
            S3Key           = "lambda/fct_mark_deliver_end_etl.zip"
            S3ObjectVersion = aws_s3_object.lambda_zip_fct_mark_deliver_end.version_id
          }

          Timeout = 290
          Environment = {
            Variables = {
              REDSHIFT_CLUSTER_ID = { "Ref" = "RedshiftClusterId" }
              DATABASE            = { "Ref" = "Database" }
              DB_USER             = { "Ref" = "DbUser" }
              SECRET_NAME         = { "Ref" = "SecretName" }
              REGION_NAME         = { "Ref" = "RegionName" }
            }
          }
        }
      }
      LambdaExecutionRoleForFctMarkDeliverEndFunction = {
        Type = "AWS::IAM::Role"
        Properties = {
          AssumeRolePolicyDocument = {
            Version = "2012-10-17"
            Statement = [
              {
                Effect = "Allow"
                Principal = {
                  Service = "lambda.amazonaws.com"
                }
                Action = "sts:AssumeRole"
              }
            ]
          }
          Policies = [
            {
              PolicyName = "LambdaPolicy"
              PolicyDocument = {
                Version = "2012-10-17"
                Statement = [
                  {
                    Effect = "Allow"
                    Action = [
                      "secretsmanager:GetSecretValue",
                      "logs:CreateLogGroup",
                      "logs:CreateLogStream",
                      "logs:PutLogEvents",
                    ]
                    Resource = "*"
                  },
                  {
                    Effect = "Allow"
                    Action = [
                      "redshift-data:DescribeStatement",
                      "redshift-data:GetStatementResult"
                    ]
                    Resource : [
                      "*"
                    ]
                  },
                  {
                    Effect = "Allow"
                    Action = [
                      "redshift-data:ExecuteStatement",
                      "redshift-data:DescribeStatement",
                      "redshift-data:ListStatements",
                      "redshift:GetClusterCredentials",
                      "redshift-data:GetStatementResult"
                    ]
                    Resource : [
                      "arn:aws:redshift:${var.region_name}:${var.aws_account_id}:cluster:${var.redshift_cluster_id}",
                      "arn:aws:redshift:${var.region_name}:${var.aws_account_id}:dbuser:${var.redshift_cluster_id}/${var.db_user}",
                      "arn:aws:redshift:${var.region_name}:${var.aws_account_id}:dbname:${var.redshift_cluster_id}/${var.database}"
                    ]
                  }
                ]
              }
            }
          ]
        }
      }
      EventBridgeRuleForFctMarkDeliverEndFunction = {
        Type = "AWS::Events::Rule"
        Properties = {
          Name               = "trigger-fct-mark-deliver-end-fn-every-5-mins-rule"
          ScheduleExpression = "rate(5 minutes)"
          Targets = [
            {
              Arn = { "Fn::GetAtt" = ["FctMarkDeliverEndFunction", "Arn"] }
              Id  = "FctMarkDeliverEndFunction"
            }
          ]
        }
      }
      LambdaInvokePermissionForFctMarkDeliverEndFunction = {
        Type = "AWS::Lambda::Permission"
        Properties = {
          FunctionName = { "Ref" = "FctMarkDeliverEndFunction" }
          Action       = "lambda:InvokeFunction"
          Principal    = "events.amazonaws.com"
          SourceArn    = { "Fn::GetAtt" = ["EventBridgeRuleForFctMarkDeliverEndFunction", "Arn"] }
        }
      }

      FctDepartureEnhancedFunction = {
        Type = "AWS::Lambda::Function"
        Properties = {
          FunctionName  = "fct_departure_enhanced_sync_for_heartbeat"
          Handler       = "lambda_function.lambda_handler"
          Role          = { "Fn::GetAtt" = ["LambdaExecutionRoleForFctDepartureEnhancedFunction", "Arn"] }
          Runtime       = "python3.12"
          Architectures = ["arm64"]
          MemorySize    = 128
          Code = {
            S3Bucket        = { "Ref" = "LambdaS3Bucket" }
            S3Key           = "lambda/fct_departure_enhanced_for_heartbeat.zip"
            S3ObjectVersion = aws_s3_object.lambda_zip_fct_departure_enhanced_for_heartbeat.version_id
          }

          Timeout = 290
          Environment = {
            Variables = {
              REDSHIFT_CLUSTER_ID  = { "Ref" = "RedshiftClusterId" }
              DATABASE             = { "Ref" = "Database" }
              DB_USER              = { "Ref" = "DbUser" }
              SECRET_NAME          = { "Ref" = "SecretName" }
              REGION_NAME          = { "Ref" = "RegionName" }
              DYNAMO_DB_TABLE_NAME = var.redshift_query_id_tracking_dynamodb_table_name
              SNS_TOPIC_ARNS       = join(",", var.notification_arns)
            }
          }
        }
      }
      LambdaExecutionRoleForFctDepartureEnhancedFunction = {
        Type = "AWS::IAM::Role"
        Properties = {
          AssumeRolePolicyDocument = {
            Version = "2012-10-17"
            Statement = [
              {
                Effect = "Allow"
                Principal = {
                  Service = "lambda.amazonaws.com"
                }
                Action = "sts:AssumeRole"
              }
            ]
          }
          Policies = [
            {
              PolicyName = "LambdaPolicy"
              PolicyDocument = {
                Version = "2012-10-17"
                Statement = [
                  {
                    Effect = "Allow"
                    Action = [
                      "secretsmanager:GetSecretValue",
                      "logs:CreateLogGroup",
                      "logs:CreateLogStream",
                      "logs:PutLogEvents",
                    ]
                    Resource = "*"
                  },
                  {
                    Effect = "Allow"
                    Action = [
                      "redshift-data:DescribeStatement",
                      "redshift-data:GetStatementResult"
                    ]
                    Resource : [
                      "*"
                    ]
                  },
                  {
                    Effect = "Allow"
                    Action = [
                      "redshift-data:ExecuteStatement",
                      "redshift-data:DescribeStatement",
                      "redshift-data:ListStatements",
                      "redshift:GetClusterCredentials",
                      "redshift-data:GetStatementResult"
                    ]
                    Resource : [
                      "arn:aws:redshift:${var.region_name}:${var.aws_account_id}:cluster:${var.redshift_cluster_id}",
                      "arn:aws:redshift:${var.region_name}:${var.aws_account_id}:dbuser:${var.redshift_cluster_id}/${var.db_user}",
                      "arn:aws:redshift:${var.region_name}:${var.aws_account_id}:dbname:${var.redshift_cluster_id}/${var.database}"
                    ]
                  },
                  {
                    "Action" : [
                      "dynamodb:PutItem",
                      "dynamodb:GetItem",
                      "dynamodb:UpdateItem",
                      "dynamodb:DeleteItem",
                      "dynamodb:Scan",
                      "dynamodb:Query"
                    ],
                    "Resource" : "arn:aws:dynamodb:${var.region_name}:${var.aws_account_id}:table/${var.redshift_query_id_tracking_dynamodb_table_name}",
                    "Effect" : "Allow"
                  }
                ]
              }
            }
          ]
        }
      }
      EventBridgeRuleForFctDepartureEnhancedFunction = {
        Type = "AWS::Events::Rule"
        Properties = {
          Name               = "trigger-fct-departure_enhanced-fn-every-30-mins-rule"
          ScheduleExpression = "rate(30 minutes)"
          Targets = [
            {
              Arn = { "Fn::GetAtt" = ["FctDepartureEnhancedFunction", "Arn"] }
              Id  = "FctDepartureEnhancedFunction"
            }
          ]
        }
      }
      LambdaInvokePermissionForFctDepartureEnhancedFunction = {
        Type = "AWS::Lambda::Permission"
        Properties = {
          FunctionName = { "Ref" = "FctDepartureEnhancedFunction" }
          Action       = "lambda:InvokeFunction"
          Principal    = "events.amazonaws.com"
          SourceArn    = { "Fn::GetAtt" = ["EventBridgeRuleForFctDepartureEnhancedFunction", "Arn"] }
        }
      }
    }

    Parameters = {
      LambdaS3Bucket = {
        Type        = "String"
        Description = "S3 bucket where the Lambda function code is stored"
      }
      RedshiftClusterId = {
        Type        = "String"
        Description = "Redshift cluster identifier"
      }
      Database = {
        Type        = "String"
        Description = "Database name"
      }
      DbUser = {
        Type        = "String"
        Description = "Database user"
      }
      SecretName = {
        Type        = "String"
        Description = "Secrets Manager secret name"
      }
      RegionName = {
        Type        = "String"
        Description = "AWS region name"
      }
      RedshiftCopyIam = {
        Type        = "String"
        Description = "IAM Role for Redshift COPY"
      }
      DynamoDbTableName = {
        Type        = "String"
        Description = "DynamoDB table name to keep track of the last sync time"
      }
      DataSelectGapHviRoiMins = {
        Type        = "Number"
        Description = "Data selection gap for hvi_roi in minutes"
      }
      DuplicateCheckGapHviRoiMins = {
        Type        = "Number"
        Description = "Duplicate data filtering gap for hvi_roi in minutes"
      }
      NextExecutionDateGapHviRoiMins = {
        Type        = "Number"
        Description = "Next execution date gap for hvi_roi in minutes"
      }
      DestinationSchemaHviRoiReporting = {
        Type        = "String"
        Description = "Destination schema for hvi_roi_reporting"
      }
      DestinationTableHviRoiReporting = {
        Type        = "String"
        Description = "Destination table for hvi_roi_reporting"
      }
      RedshiftCopyDataS3Bucket = {
        Type        = "String"
        Description = "Destination S3 bucket for Redshift COPY"
      }
    }
  })

  parameters = {
    LambdaS3Bucket                   = aws_s3_bucket.lambda_bucket.bucket
    RedshiftClusterId                = var.redshift_cluster_id
    Database                         = var.database
    DbUser                           = var.db_user
    SecretName                       = var.secret_name
    RegionName                       = var.region_name
    RedshiftCopyIam                  = var.redshift_copy_iam
    DynamoDbTableName                = var.dynamodb_table_name
    DataSelectGapHviRoiMins          = var.data_select_gap_hvi_roi_mins
    DuplicateCheckGapHviRoiMins      = var.duplicate_check_gap_hvi_roi_mins
    NextExecutionDateGapHviRoiMins   = var.next_execution_date_gap_hvi_roi_mins
    DestinationSchemaHviRoiReporting = var.destination_schema_hvi_roi_reporting
    DestinationTableHviRoiReporting  = var.destination_table_hvi_roi_reporting
    RedshiftCopyDataS3Bucket         = var.redshift_copy_s3_bucket
  }
  capabilities      = ["CAPABILITY_IAM"]
  notification_arns = var.notification_arns
  tags              = local.all_tags
}

