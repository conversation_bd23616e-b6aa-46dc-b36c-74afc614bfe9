variable "secrets_manager_arns" {
  description = "List of ARNs of the Secrets Manager secrets"
  type        = list(string)
}

variable "tags" {
  description = "Additional tags to apply to resources"
  type        = map(string)
  default     = {}
}
variable "redshift_cluster_id" {
  description = "Redshift cluster identifier"
  type        = string
}

variable "database" {
  description = "Database name"
  type        = string
}

variable "db_user" {
  description = "Database user"
  type        = string
}

variable "secret_name" {
  description = "Secrets Manager secret name"
  type        = string
}

variable "region_name" {
  description = "AWS region name"
  type        = string
}

variable "s3_bucket_name" {
  description = "S3 bucket to upload Lambda function code"
  type        = string
  default     = "your-s3-bucket-name"
}

variable "redshift_copy_iam" {
  description = "IAM Role for Redshift COPY"
  type        = string
}

variable "dynamodb_table_name" {
  description = "DynamoDB table name to keep track of the last sync time"
  type        = string
}

variable "data_select_gap_hvi_roi_mins" {
  description = "Data selection gap for hvi_roi in minutes"
  type        = number
}

variable "duplicate_check_gap_hvi_roi_mins" {
  description = "Duplicate data filtering gap for hvi_roi in minutes"
  type        = number
}

variable "next_execution_date_gap_hvi_roi_mins" {
  description = "Next execution date gap for hvi_roi in minutes"
  type        = number
}

variable "destination_schema_hvi_roi_reporting" {
  description = "Destination schema for hvi_roi_reporting"
  type        = string
}

variable "destination_table_hvi_roi_reporting" {
  description = "Destination table for hvi_roi_reporting"
  type        = string
}

variable "redshift_copy_s3_bucket" {
  description = "S3 bucket for Redshift COPY"
  type        = string
}

variable "notification_arns" {
  description = "ARN for SNS topic to send notifications"
  type        = list(string)
  default     = null
}

variable "pandas_layer_arn" {
  type = list(string)
  #https://aws-sdk-pandas.readthedocs.io/en/3.9.1/layers.html
  default = ["arn:aws:lambda:us-east-1:************:layer:AWSSDKPandas-Python312-Arm64:13"]
}

variable "aws_account_id" {
  description = "AWS account id"
  type        = string
}

variable "redshift_query_id_tracking_dynamodb_table_name" {
  description = "Redshift query tracking dynamodb table name"
  type        = string
}
