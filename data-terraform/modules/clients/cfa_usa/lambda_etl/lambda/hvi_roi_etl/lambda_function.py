import json
import boto3
import time
import os

# Initialize Redshift Data API client
redshift_data = boto3.client("redshift-data")

# Define the Redshift cluster and database details from environment variables
REDSHIFT_CLUSTER_ID = os.environ["REDSHIFT_CLUSTER_ID"]
DATABASE = os.environ["DATABASE"]
DB_USER = os.environ["DB_USER"]
REGION_NAME = os.environ["REGION_NAME"]

# Define the SQL queries
REFRESH_MV_SQL = (
    "REFRESH MATERIALIZED VIEW eyecue_cfa_usa.mv_last_2days_roi_hvi_events;"
)
INSERT_SQL_HVI_START = """
    INSERT INTO prod.eyecue_cfa_usa.flattened_hvi_events_start (
        meta_kinesis_datastream_arrival_datetime_utc,
        meta_redshift_refresh_datetime_utc,
        md5_hash_id,
        artificial,
        camera_id,
        complete,
        confidence,
        creation_time,
        event_id,
        event_time,
        event_time_tz,
        eyeq_server_version,
        frames_life,
        hvi_event,
        label,
        lane_number,
        "order",
        overtaken,
        person_id,
        roi_event,
        roi_id,
        roi_type,
        total_time,
        tracker_id,
        tracker_version,
        valid_journey,
        vehicle_db_id,
        vehicle_id,
        store_id,
        frame_id,
        event_type,
        client_name,
        coordinates_mqtt_x1,
        coordinates_mqtt_x2,
        coordinates_mqtt_xc,
        coordinates_mqtt_y1,
        coordinates_mqtt_y2,
        coordinates_mqtt_yc
    )
    SELECT 
        meta_kinesis_datastream_arrival_datetime_utc,
        meta_redshift_refresh_datetime_utc,
        md5_hash_id,
        artificial,
        camera_id,
        complete,
        confidence,
        creation_time,
        event_id,
        event_time,
        event_time_tz,
        eyeq_server_version,
        frames_life,
        hvi_event,
        label,
        lane_number,
        "order",
        overtaken,
        person_id,
        roi_event,
        roi_id,
        roi_type,
        total_time,
        tracker_id,
        tracker_version,
        valid_journey,
        vehicle_db_id,
        vehicle_id,
        store_id,
        frame_id,
        event_type,
        client_name,
        coordinates_mqtt_x1,
        coordinates_mqtt_x2,
        coordinates_mqtt_xc,
        coordinates_mqtt_y1,
        coordinates_mqtt_y2,
        coordinates_mqtt_yc
    FROM
        (
            WITH latest_record AS (
                SELECT
                    MAX(meta_kinesis_datastream_arrival_datetime_utc) AS latest_datetime
                FROM
                    prod.eyecue_cfa_usa.flattened_hvi_events_start
            )
            SELECT 
                meta_kinesis_datastream_arrival_datetime_utc,
                meta_redshift_refresh_datetime_utc,
                md5_hash_id,
                artificial,
                camera_id,
                complete,
                confidence,
                creation_time,
                event_id,
                event_time,
                event_time_tz,
                eyeq_server_version,
                frames_life,
                hvi_event,
                label,
                lane_number,
                "order",
                overtaken,
                person_id,
                roi_event,
                roi_id,
                roi_type,
                total_time,
                tracker_id,
                tracker_version,
                valid_journey,
                vehicle_db_id,
                vehicle_id,
                store_id,
                frame_id,
                event_type,
                client_name,
                coordinates_mqtt_x1,
                coordinates_mqtt_x2,
                coordinates_mqtt_xc,
                coordinates_mqtt_y1,
                coordinates_mqtt_y2,
                coordinates_mqtt_yc
            FROM
                prod.eyecue_cfa_usa.mv_last_2days_roi_hvi_events t
            WHERE
                t.meta_kinesis_datastream_arrival_datetime_utc > (
                    SELECT
                        latest_datetime
                    FROM
                        latest_record
                )
                AND t.roi_event = 'start'
                AND t.event_type = 'hvi'
        );
"""

INSERT_SQL_HVI_END = """
    INSERT INTO prod.eyecue_cfa_usa.flattened_hvi_events_end (
        meta_kinesis_datastream_arrival_datetime_utc,
        meta_redshift_refresh_datetime_utc,
        md5_hash_id,
        artificial,
        camera_id,
        complete,
        confidence,
        creation_time,
        event_id,
        event_time,
        event_time_tz,
        eyeq_server_version,
        frames_life,
        hvi_event,
        label,
        lane_number,
        "order",
        overtaken,
        person_id,
        roi_event,
        roi_id,
        roi_type,
        total_time,
        tracker_id,
        tracker_version,
        valid_journey,
        vehicle_db_id,
        vehicle_id,
        store_id,
        frame_id,
        event_type,
        client_name,
        coordinates_mqtt_x1,
        coordinates_mqtt_x2,
        coordinates_mqtt_xc,
        coordinates_mqtt_y1,
        coordinates_mqtt_y2,
        coordinates_mqtt_yc
    )
    SELECT 
        meta_kinesis_datastream_arrival_datetime_utc,
        meta_redshift_refresh_datetime_utc,
        md5_hash_id,
        artificial,
        camera_id,
        complete,
        confidence,
        creation_time,
        event_id,
        event_time,
        event_time_tz,
        eyeq_server_version,
        frames_life,
        hvi_event,
        label,
        lane_number,
        "order",
        overtaken,
        person_id,
        roi_event,
        roi_id,
        roi_type,
        total_time,
        tracker_id,
        tracker_version,
        valid_journey,
        vehicle_db_id,
        vehicle_id,
        store_id,
        frame_id,
        event_type,
        client_name,
        coordinates_mqtt_x1,
        coordinates_mqtt_x2,
        coordinates_mqtt_xc,
        coordinates_mqtt_y1,
        coordinates_mqtt_y2,
        coordinates_mqtt_yc
    FROM
        (
            WITH latest_record AS (
                SELECT
                    MAX(meta_kinesis_datastream_arrival_datetime_utc) AS latest_datetime
                FROM
                    prod.eyecue_cfa_usa.flattened_hvi_events_end
            )
            SELECT 
                meta_kinesis_datastream_arrival_datetime_utc,
                meta_redshift_refresh_datetime_utc,
                md5_hash_id,
                artificial,
                camera_id,
                complete,
                confidence,
                creation_time,
                event_id,
                event_time,
                event_time_tz,
                eyeq_server_version,
                frames_life,
                hvi_event,
                label,
                lane_number,
                "order",
                overtaken,
                person_id,
                roi_event,
                roi_id,
                roi_type,
                total_time,
                tracker_id,
                tracker_version,
                valid_journey,
                vehicle_db_id,
                vehicle_id,
                store_id,
                frame_id,
                event_type,
                client_name,
                coordinates_mqtt_x1,
                coordinates_mqtt_x2,
                coordinates_mqtt_xc,
                coordinates_mqtt_y1,
                coordinates_mqtt_y2,
                coordinates_mqtt_yc
            FROM
                prod.eyecue_cfa_usa.mv_last_2days_roi_hvi_events t
            WHERE
                t.meta_kinesis_datastream_arrival_datetime_utc > (
                    SELECT
                        latest_datetime
                    FROM
                        latest_record
                )
                AND t.roi_event = 'end'
                AND t.event_type = 'hvi'
        );
"""

INSERT_SQL_ROI_START = """
    INSERT INTO prod.eyecue_cfa_usa.flattened_roi_events_start (
        meta_kinesis_datastream_arrival_datetime_utc,
        meta_redshift_refresh_datetime_utc,
        md5_hash_id,
        artificial,
        camera_id,
        complete,
        confidence,
        creation_time,
        event_id,
        event_time,
        event_time_tz,
        eyeq_server_version,
        frames_life,
        hvi_event,
        label,
        lane_number,
        "order",
        overtaken,
        person_id,
        roi_event,
        roi_id,
        roi_type,
        total_time,
        tracker_id,
        tracker_version,
        valid_journey,
        vehicle_db_id,
        vehicle_id,
        store_id,
        frame_id,
        event_type,
        client_name,
        coordinates_mqtt_x1,
        coordinates_mqtt_x2,
        coordinates_mqtt_xc,
        coordinates_mqtt_y1,
        coordinates_mqtt_y2,
        coordinates_mqtt_yc
    )
    SELECT 
        meta_kinesis_datastream_arrival_datetime_utc,
        meta_redshift_refresh_datetime_utc,
        md5_hash_id,
        artificial,
        camera_id,
        complete,
        confidence,
        creation_time,
        event_id,
        event_time,
        event_time_tz,
        eyeq_server_version,
        frames_life,
        hvi_event,
        label,
        lane_number,
        "order",
        overtaken,
        person_id,
        roi_event,
        roi_id,
        roi_type,
        total_time,
        tracker_id,
        tracker_version,
        valid_journey,
        vehicle_db_id,
        vehicle_id,
        store_id,
        frame_id,
        event_type,
        client_name,
        coordinates_mqtt_x1,
        coordinates_mqtt_x2,
        coordinates_mqtt_xc,
        coordinates_mqtt_y1,
        coordinates_mqtt_y2,
        coordinates_mqtt_yc
    FROM
        (
            WITH latest_record AS (
                SELECT
                    MAX(meta_kinesis_datastream_arrival_datetime_utc) AS latest_datetime
                FROM
                    prod.eyecue_cfa_usa.flattened_roi_events_start
            )
            SELECT 
                meta_kinesis_datastream_arrival_datetime_utc,
                meta_redshift_refresh_datetime_utc,
                md5_hash_id,
                artificial,
                camera_id,
                complete,
                confidence,
                creation_time,
                event_id,
                event_time,
                event_time_tz,
                eyeq_server_version,
                frames_life,
                hvi_event,
                label,
                lane_number,
                "order",
                overtaken,
                person_id,
                roi_event,
                roi_id,
                roi_type,
                total_time,
                tracker_id,
                tracker_version,
                valid_journey,
                vehicle_db_id,
                vehicle_id,
                store_id,
                frame_id,
                event_type,
                client_name,
                coordinates_mqtt_x1,
                coordinates_mqtt_x2,
                coordinates_mqtt_xc,
                coordinates_mqtt_y1,
                coordinates_mqtt_y2,
                coordinates_mqtt_yc
            FROM
                prod.eyecue_cfa_usa.mv_last_2days_roi_hvi_events t
            WHERE
                t.meta_kinesis_datastream_arrival_datetime_utc > (
                    SELECT
                        latest_datetime
                    FROM
                        latest_record
                )
                AND t.roi_event = 'start'
                AND t.event_type = 'roi'
        );
"""

INSERT_SQL_ROI_END = """
    INSERT INTO prod.eyecue_cfa_usa.flattened_roi_events_end (
        meta_kinesis_datastream_arrival_datetime_utc,
        meta_redshift_refresh_datetime_utc,
        md5_hash_id,
        artificial,
        camera_id,
        complete,
        confidence,
        creation_time,
        event_id,
        event_time,
        event_time_tz,
        eyeq_server_version,
        frames_life,
        hvi_event,
        label,
        lane_number,
        "order",
        overtaken,
        person_id,
        roi_event,
        roi_id,
        roi_type,
        total_time,
        tracker_id,
        tracker_version,
        valid_journey,
        vehicle_db_id,
        vehicle_id,
        store_id,
        frame_id,
        event_type,
        client_name,
        coordinates_mqtt_x1,
        coordinates_mqtt_x2,
        coordinates_mqtt_xc,
        coordinates_mqtt_y1,
        coordinates_mqtt_y2,
        coordinates_mqtt_yc
    )
    SELECT 
        meta_kinesis_datastream_arrival_datetime_utc,
        meta_redshift_refresh_datetime_utc,
        md5_hash_id,
        artificial,
        camera_id,
        complete,
        confidence,
        creation_time,
        event_id,
        event_time,
        event_time_tz,
        eyeq_server_version,
        frames_life,
        hvi_event,
        label,
        lane_number,
        "order",
        overtaken,
        person_id,
        roi_event,
        roi_id,
        roi_type,
        total_time,
        tracker_id,
        tracker_version,
        valid_journey,
        vehicle_db_id,
        vehicle_id,
        store_id,
        frame_id,
        event_type,
        client_name,
        coordinates_mqtt_x1,
        coordinates_mqtt_x2,
        coordinates_mqtt_xc,
        coordinates_mqtt_y1,
        coordinates_mqtt_y2,
        coordinates_mqtt_yc
    FROM
        (
            WITH latest_record AS (
                SELECT
                    MAX(meta_kinesis_datastream_arrival_datetime_utc) AS latest_datetime
                FROM
                    prod.eyecue_cfa_usa.flattened_roi_events_end
            )
            SELECT 
                meta_kinesis_datastream_arrival_datetime_utc,
                meta_redshift_refresh_datetime_utc,
                md5_hash_id,
                artificial,
                camera_id,
                complete,
                confidence,
                creation_time,
                event_id,
                event_time,
                event_time_tz,
                eyeq_server_version,
                frames_life,
                hvi_event,
                label,
                lane_number,
                "order",
                overtaken,
                person_id,
                roi_event,
                roi_id,
                roi_type,
                total_time,
                tracker_id,
                tracker_version,
                valid_journey,
                vehicle_db_id,
                vehicle_id,
                store_id,
                frame_id,
                event_type,
                client_name,
                coordinates_mqtt_x1,
                coordinates_mqtt_x2,
                coordinates_mqtt_xc,
                coordinates_mqtt_y1,
                coordinates_mqtt_y2,
                coordinates_mqtt_yc
            FROM
                prod.eyecue_cfa_usa.mv_last_2days_roi_hvi_events t
            WHERE
                t.meta_kinesis_datastream_arrival_datetime_utc > (
                    SELECT
                        latest_datetime
                    FROM
                        latest_record
                )
                AND t.roi_event = 'end'
                AND t.event_type = 'roi'
        );
"""


def execute_sql(sql_statement):
    """
    Function to execute an SQL statement using the Redshift Data API.
    """
    response = redshift_data.execute_statement(
        ClusterIdentifier=REDSHIFT_CLUSTER_ID,
        Database=DATABASE,
        DbUser=DB_USER,
        Sql=sql_statement,
    )

    # Check the execution id for the SQL statement
    query_id = response["Id"]

    # Wait for query to complete
    status = redshift_data.describe_statement(Id=query_id)
    while status["Status"] in ["SUBMITTED", "PICKED", "STARTED"]:
        # time.sleep(1)  # Wait before checking the status again
        status = redshift_data.describe_statement(Id=query_id)

    # Check for errors
    if status["Status"] == "FAILED":
        raise Exception(f"SQL execution failed: {status['Error']}")

    return status


def lambda_handler(event, context):
    try:
        # Step 1: Refresh Materialized View
        print("Refreshing Materialized View...")
        refresh_status = execute_sql(REFRESH_MV_SQL)
        print(refresh_status)
        print("Materialized View Refreshed Successfully.")

        # Step 2: Insert Data into HVI Start Table
        print("Inserting Data into HVI Start Table ...")
        insert_status = execute_sql(INSERT_SQL_HVI_START)
        print(insert_status)
        print("Data Inserted Successfully.")

        # Step 3: Insert Data into HVI End Table
        print("Inserting Data into HVI End Table ...")
        insert_status = execute_sql(INSERT_SQL_HVI_END)
        print(insert_status)
        print("Data Inserted Successfully.")

        # Step 4: Insert Data into ROI Start Table
        print("Inserting Data into ROI Start Table ...")
        insert_status = execute_sql(INSERT_SQL_ROI_START)
        print(insert_status)
        print("Data Inserted Successfully.")

        # Step 4: Insert Data into ROI End Table
        print("Inserting Data into ROI End Table ...")
        insert_status = execute_sql(INSERT_SQL_ROI_END)
        print(insert_status)
        print("Data Inserted Successfully.")

        return {
            "statusCode": 200,
            "body": json.dumps(
                "Materialized View Refreshed and Data Inserted Successfully!"
            ),
        }

    except Exception as e:
        print(f"Error executing SQL: {e}")
        return {"statusCode": 500, "body": json.dumps(f"Error: {str(e)}")}
