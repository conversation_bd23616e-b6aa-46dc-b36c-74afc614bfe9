import json
import boto3
import os
import logging
from botocore.exceptions import ClientError
from datetime import datetime, timedelta
import traceback

# Initialize AWS clients
redshift_data = boto3.client("redshift-data")
dynamodb = boto3.resource("dynamodb")
sns_client = boto3.client("sns")

# Environment variables
REDSHIFT_CLUSTER_ID = os.environ["REDSHIFT_CLUSTER_ID"]
DATABASE = os.environ["DATABASE"]
DB_USER = os.environ["DB_USER"]
REGION_NAME = os.environ["REGION_NAME"]
DYNAMO_DB_TABLE_NAME = os.environ["DYNAMO_DB_TABLE_NAME"]
SNS_TOPIC_ARNS = os.environ["SNS_TOPIC_ARNS"]
REDSHIFT_QUERY_NAME = "cfa_usa_update_departure_enhanced_fact_table"

# Set up logging
logger = logging.getLogger()
logger.setLevel(logging.INFO)

# DynamoDB table
table = dynamodb.Table(DYNAMO_DB_TABLE_NAME)


def get_wednesday_range():
    now_utc = datetime.utcnow()
    weekday = now_utc.weekday()  # Monday = 0

    days_since_wed = (weekday - 2) % 7
    start_date = now_utc - timedelta(days=days_since_wed + 14)
    start_date = start_date.replace(hour=0, minute=0, second=0, microsecond=0)

    days_until_wed = (2 - weekday) % 7
    end_date = now_utc + timedelta(days=days_until_wed + 14)
    end_date = end_date.replace(hour=23, minute=59, second=59, microsecond=0)

    return start_date.strftime("%Y-%m-%d %H:%M:%S"), end_date.strftime(
        "%Y-%m-%d %H:%M:%S"
    )


def fetch_previous_query_status():
    try:
        response = table.get_item(Key={"redshift_query_name": REDSHIFT_QUERY_NAME})
        item = response.get("Item", {})
        return (
            item.get("query_id"),
            item.get("proc_from_date"),
            item.get("proc_to_date"),
        )
    except ClientError as e:
        logger.error(f"[DynamoDB] Failed to fetch previous query ID: {e}")
        return None, None, None


def store_new_query_info(query_id, from_date, to_date):
    try:
        item = {
            "redshift_query_name": REDSHIFT_QUERY_NAME,
            "query_id": query_id,
            "proc_from_date": from_date,
            "proc_to_date": to_date,
            "execution_time_utc": datetime.utcnow().isoformat() + "Z",
        }
        table.put_item(Item=item)
        logger.info(f"[DynamoDB] Stored item: {json.dumps(item)}")
    except ClientError as e:
        logger.error(f"[DynamoDB] Failed to update query info: {e}")
        raise


def check_redshift_query_status(query_id):
    try:
        response = redshift_data.describe_statement(Id=query_id)
        return response["Status"]
    except ClientError as e:
        logger.error(f"[Redshift] Failed to describe query {query_id}: {e}")
        return "UNKNOWN"


def execute_redshift_procedure(from_date, to_date):
    sql = f"CALL prod.load_fact_cfa_usa_departure_enriched('{from_date}', '{to_date}')"
    response = redshift_data.execute_statement(
        ClusterIdentifier=REDSHIFT_CLUSTER_ID,
        Database=DATABASE,
        DbUser=DB_USER,
        Sql=sql,
    )
    logger.info(f"[Redshift] Procedure execution started: {response['Id']}")
    return response["Id"]


def notify_sns_failure(subject, message):
    try:
        notification_arns = os.environ["notification_arns"].split(",")
        for arn in notification_arns:
            arn = arn.strip()
            if not arn:
                continue
            sns_client.publish(
                TopicArn=arn,
                Subject=subject,
                Message=message,
            )
            logger.info(f"[SNS] Error notification sent to: {arn}")
    except Exception as e:
        logger.error(f"[SNS] Failed to send error notification: {e}")


def lambda_handler(event, context):
    logger.info("Lambda triggered to refresh Redshift fact table.")

    try:
        previous_query_id, prev_from, prev_to = fetch_previous_query_status()

        if previous_query_id:
            status = check_redshift_query_status(previous_query_id)
            logger.info(f"Previous query ID {previous_query_id} has status: {status}")

            if status in ["SUBMITTED", "PICKED", "STARTED"]:
                logger.info("Previous query still in progress. Skipping new execution.")
                return {
                    "statusCode": 200,
                    "body": json.dumps("Query already in progress."),
                }

            if status in ["FAILED", "ABORTED"]:
                notify_sns_failure(
                    "CFA_USA_FACT_TABLE_REFRESH_ERROR[fact_cfa_usa_departure_enriched]",
                    f"Last attempt to refresh fact table failed (query_id: {previous_query_id}, range: {prev_from} to {prev_to}).\nStarting a new attempt...",
                )

        proc_from_date, proc_to_date = get_wednesday_range()
        logger.info(f"New procedure date range: {proc_from_date} to {proc_to_date}")

        new_query_id = execute_redshift_procedure(proc_from_date, proc_to_date)
        store_new_query_info(new_query_id, proc_from_date, proc_to_date)

        return {
            "statusCode": 200,
            "body": json.dumps(f"New Redshift query started: {new_query_id}"),
        }

    except Exception as e:
        logger.error(f"[Lambda Error] Unhandled exception: {e}")
        trace = traceback.format_exc()
        notify_sns_failure(
            "CFA_USA_FACT_TABLE_REFRESH_FATAL_ERROR[fact_cfa_usa_departure_enriched]",
            f"{e}\n\nTraceback:\n{trace}",
        )
        return {
            "statusCode": 500,
            "body": json.dumps("Fatal error occurred. Aborting execution."),
        }
