import boto3
import io
import pandas as pd


class S3Manager:
    def __init__(self, bucket_name, logger):
        self.s3_client = boto3.client("s3")
        self.bucket_name = bucket_name
        self.logger = logger

    def upload_dataframe_as_parquet(self, df, key):
        parquet_buffer = io.BytesIO()
        df.to_parquet(parquet_buffer, index=False)
        self.s3_client.put_object(
            Bucket=self.bucket_name, Key=key, Body=parquet_buffer.getvalue()
        )
        return key

    def save_df_to_s3(self, df, file_name):

        # Define the data types to match Redshift table structure, excluding date columns
        dtypes = {
            "hvi_count": "int32",
            "avg_interaction_duration": "float64",
            "min_interaction_duration": "float64",
            "max_interaction_duration": "float64",
            "start_start_to_end_end_interaction_duration_seconds": "float64",
            "roi_start_to_hvi_start_duration": "float64",
            "hvi_end_to_roi_end_duration": "float64",
            "roi_duration": "float64",
            "avg_duration_between_hvis": "float64",
            "lane_number": "int32",
            "roi_id": "string",
            "roi_type": "string",
            "vehicle_db_id": "int32",
            "vehicle__id": "string",
            "store_id": "string",
            "store_name": "string",
            "mapped_function": "string",
        }

        # Apply data types to non-date columns
        df = df.astype(dtypes)

        # Set date columns to `datetime64[ns]` format for Parquet compatibility
        date_columns = [
            "first_start_interaction_time_local",
            "last_end_interaction_time_local",
            "event_time_local",
            "roi_start",
            "roi_end",
        ]
        for col in date_columns:
            df[col] = pd.to_datetime(df[col])

        new_key = f"cfa-usa/{file_name}.parquet"
        self.logger.info(f"Saving Parquet file for Redshift upload: {new_key}")

        self.upload_dataframe_as_parquet(df, new_key)

        return new_key
