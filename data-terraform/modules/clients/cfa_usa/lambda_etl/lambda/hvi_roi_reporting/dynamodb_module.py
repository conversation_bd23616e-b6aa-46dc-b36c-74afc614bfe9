import boto3
import datetime as dt
from datetime import datetime


class DynamoDBManager:
    """
    Class for managing DynamoDB interactions related to execution scheduling.

    This class provides methods to get and set the next execution date in a DynamoDB table.

    Attributes:
    table_name (str): The name of the DynamoDB table.
    logger (Logger): Logger for logging information and errors.
    """

    def __init__(self, table_name, logger):
        self.dynamodb = boto3.resource("dynamodb")
        self.table = self.dynamodb.Table(table_name)
        self.logger = logger

    def get_next_execution_date(self):
        """
        Retrieve the next execution date from the DynamoDB table.

        This method retrieves the value for the item with the id 'next_execution_date_time'.
        If no such item exists, it defaults to one day ago.

        Returns:
        datetime: The next execution date as a datetime object.
        """
        response = self.table.get_item(Key={"id": "next_execution_date_time"})
        if "Item" in response:
            return datetime.strptime(response["Item"]["value"], "%Y-%m-%d %H:%M:%S")
        else:
            # Default to one day ago if no date is found
            return datetime.now() - dt.<PERSON><PERSON><PERSON>(days=1)

    def set_next_execution_date(self, next_execution_date):
        """
        Set the next execution date in the DynamoDB table.

        This method updates the value for the item with the id 'next_execution_date_time'.

        Parameters:
        next_execution_date (datetime): The next execution date to be set.
        """
        self.table.put_item(
            Item={
                "id": "next_execution_date_time",
                "value": next_execution_date.strftime("%Y-%m-%d %H:%M:%S"),
            }
        )
