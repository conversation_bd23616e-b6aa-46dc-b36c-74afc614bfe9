import boto3


class DatabaseManager:
    """
    Class for managing database interactions with Amazon Redshift.

    This class provides methods to execute SQL queries, retrieve query results, and copy data to Redshift.

    Attributes:
    cluster_id (str): The identifier of the Redshift cluster.
    database (str): The name of the Redshift database.
    db_user (str): The Redshift database user.
    logger (Logger): Logger for logging information and errors.
    """

    def __init__(self, cluster_id, database, db_user, logger):
        self.redshift_data = boto3.client("redshift-data")
        self.cluster_id = cluster_id
        self.database = database
        self.db_user = db_user
        self.logger = logger

    def execute_query(self, sql_statement):
        """
        Execute a SQL query on the Redshift cluster.

        Parameters:
        sql_statement (str): The SQL query to be executed.

        Returns:
        str: The query ID of the executed statement.
        """
        response = self.redshift_data.execute_statement(
            ClusterIdentifier=self.cluster_id,
            Database=self.database,
            DbUser=self.db_user,
            Sql=sql_statement,
        )
        query_id = response["Id"]

        status = self.redshift_data.describe_statement(Id=query_id)
        while status["Status"] in ["SUBMITTED", "PICKED", "STARTED"]:
            status = self.redshift_data.describe_statement(Id=query_id)

        if status["Status"] == "FAILED":
            raise Exception(f"SQL execution failed: {status['Error']}")

        return query_id

    def get_query_results(self, query_id):
        """
        Retrieve the results of a previously executed query.

        Parameters:
        query_id (str): The ID of the query whose results are to be retrieved.

        Returns:
        tuple: A tuple containing the records and the column metadata of the query result.
        """

        records = []
        next_token = None

        while True:
            if next_token:
                result_response = self.redshift_data.get_statement_result(
                    Id=query_id, NextToken=next_token
                )
            else:
                result_response = self.redshift_data.get_statement_result(Id=query_id)

            records.extend(result_response["Records"])
            next_token = result_response.get("NextToken", None)

            # If there's no next token, we've retrieved all data
            if not next_token:
                break

        column_metadata = result_response["ColumnMetadata"]
        return records, column_metadata

    def copy_data_to_redshift(
        self,
        bucket,
        redshift_file_path,
        redshift_copy_iam,
        destination_schema,
        destination_table,
    ):
        """
        Copy data from an S3 bucket to a Redshift table using the COPY command.

        Parameters:
        bucket (str): The name of the S3 bucket containing the data.
        redshift_file_path (str): The file path within the S3 bucket.
        redshift_copy_iam (str): The IAM role ARN for Redshift COPY operation.
        destination_schema (str): The schema in Redshift where the data will be copied.
        destination_table (str): The table in Redshift where the data will be copied.
        """

        copy_query = f"""
        COPY {destination_schema}.{destination_table} (
            hvi_count, 
            avg_interaction_duration, 
            min_interaction_duration, 
            max_interaction_duration, 
            first_start_interaction_time_local, 
            last_end_interaction_time_local, 
            start_start_to_end_end_interaction_duration_seconds, 
            roi_start_to_hvi_start_duration, 
            hvi_end_to_roi_end_duration, 
            roi_duration, 
            avg_duration_between_hvis, 
            lane_number, 
            roi_id, 
            roi_type, 
            vehicle_db_id, 
            vehicle__id, 
            store_id, 
            store_name, 
            event_time_local, 
            roi_start, 
            roi_end, 
            mapped_function
        )
        FROM 's3://{bucket}/{redshift_file_path}'
        IAM_ROLE '{redshift_copy_iam}'
        FORMAT AS PARQUET;
        """
        # client = self.redshift_data
        try:
            self.execute_query(copy_query)

            # TODO if the file needs to be deleted use this.
            # s3_client.delete_object(Bucket=bucket, Key=redshift_file_path)
        except Exception as e:
            self.logger.error(f"Error executing Redshift copy command: {str(e)}")
            raise


def get_hvi_roi_query(
    event_type, roi_event, start_date, end_date, take_start_date=False
):
    """
    Generate a custom SQL query to retrieve HVI or ROI data from Redshift.

    Parameters:
    event_type (str): The type of event ('hvi' or 'roi').
    roi_event (str): The type of ROI event ('start' or 'end').
    start_date (datetime): The start date for the data selection.
    end_date (datetime): The end date for the data selection.
    take_start_date (bool): Whether to include the start date in the condition.

    Returns:
    str: The generated SQL query string.
    """
    # Dictionary to map event types and roi_events to table names
    table_names = {
        ("hvi", "start"): "eyecue_cfa_usa.flattened_hvi_events_start",
        ("hvi", "end"): "eyecue_cfa_usa.flattened_hvi_events_end",
        ("roi", "start"): "eyecue_cfa_usa.flattened_roi_events_start",
        ("roi", "end"): "eyecue_cfa_usa.flattened_roi_events_end",
    }

    # Determine the table name based on event_type and roi_event
    table_name = table_names.get((event_type, roi_event))
    if not table_name:
        raise ValueError("Invalid event_type or roi_event")

    # Format the condition for the WHERE clause based on take_both_sides parameter
    if take_start_date:
        date_condition = f"""
                ev.meta_kinesis_datastream_arrival_datetime_utc >= '{start_date:%Y-%m-%d %H:%M:%S}'
                AND ev.meta_kinesis_datastream_arrival_datetime_utc < '{end_date:%Y-%m-%d %H:%M:%S}'
            """
    else:
        date_condition = f"""
                ev.meta_kinesis_datastream_arrival_datetime_utc > '{start_date:%Y-%m-%d %H:%M:%S}'
                AND ev.meta_kinesis_datastream_arrival_datetime_utc < '{end_date:%Y-%m-%d %H:%M:%S}'
            """

    # Format the custom query string
    custom_query = f"""
            SELECT
                ev.event_type::TEXT,
                ev.event_time::TIMESTAMPTZ AS event_datetime_utc,
                ev.event_time_tz::TIMESTAMP AS event_datetime_local,
                ev.lane_number::INT,
                ev.person_id::TEXT,
                ev.roi_event::TEXT,
                ev.roi_id::TEXT,
                ev.roi_type::TEXT,
                ev.total_time::FLOAT,
                ev.vehicle_id::TEXT,
                ev.store_id::TEXT,
                ev.frame_id::TEXT,
                s.location_description
            FROM {table_name} ev
            JOIN prod.dim_cfa_site s ON ev.store_id::TEXT = s.streaming_site_id
            WHERE {date_condition}
        """
    return custom_query
