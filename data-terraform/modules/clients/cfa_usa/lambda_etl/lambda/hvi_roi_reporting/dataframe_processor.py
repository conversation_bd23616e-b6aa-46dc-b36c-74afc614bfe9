import pandas as pd

def records_to_dataframe(records, column_metadata):
    rows = [[list(cell.values())[0] for cell in record] for record in records]
    column_names = [col["name"] for col in column_metadata]
    return pd.DataFrame(rows, columns=column_names)

def format_data_types(df):
    formatted_df = df.astype(
        {
            "event_type": "string",
            "event_datetime_utc": "datetime64[us, UTC]",
            "event_datetime_local": "datetime64[us]",
            "lane_number": "Int64",
            "person_id": "string",
            "roi_event": "string",
            "roi_id": "string",
            "roi_type": "string",
            "total_time": "float64",
            "vehicle_id": "string",
            "store_id": "string",
            "frame_id": "string",
            "location_description": "string",
        }
    )
    return formatted_df
