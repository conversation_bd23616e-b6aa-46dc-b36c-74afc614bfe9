import pandas as pd
import datetime as dt
import numpy as np
from datetime import datetime
from datetime import timedelta
import time
import concurrent.futures

from dataframe_processor import records_to_dataframe, format_data_types
from database_module import get_hvi_roi_query


class HviReportGenerator:
    """
    Class for generating HVI (Human Vehicle Interaction) reports.

    This class is responsible for interacting with Redshift, S3, and DynamoDB,
    processing HVI and ROI (Region of Interest) data, and generating statistical
    reports for HVI.

    Attributes:
    boto3_session (boto3.Session): Boto3 session for interacting with AWS services.
    redshift_copy_iam (str): IAM role for Redshift COPY operation.
    db_manager (DatabaseManager): Instance for managing database interactions.
    s3_manager (S3Manager): Instance for managing S3 interactions.
    dynamodb_manager (DynamoDBManager): Instance for managing DynamoDB interactions.
    logger (Logger): Logger for logging information and errors.
    """

    def __init__(
        self,
        boto3,
        redshift_copy_iam,
        db_manager,
        s3_manager,
        dynamodb_manager,
        destination_schema,
        destination_table,
        logger,
    ):
        self.logger = logger
        self.boto3_session = boto3.Session()
        self.redshift_copy_iam = redshift_copy_iam
        self.destination_schema = destination_schema
        self.destination_table = destination_table

        self.db_manager = db_manager
        self.s3_manager = s3_manager
        self.dynamodb_manager = dynamodb_manager

    # Function to execute SQL query and get the result as Pandas DataFrame
    def get_df_from_sql(self, sql_statement):
        """
        Execute a SQL query and retrieve the result as a Pandas DataFrame.

        Parameters:
        sql_statement (str): SQL query to be executed.

        Returns:
        DataFrame: Resulting DataFrame from the SQL query.
        """

        query_id = self.db_manager.execute_query(sql_statement)
        records, column_metadata = self.db_manager.get_query_results(query_id)
        df = records_to_dataframe(records, column_metadata)

        return df

    def process_data(self, start_hvi_df, end_hvi_df, start_roi_df, end_roi_df):
        """
        Process HVI and ROI data to generate statistical reports.

        Parameters:
        start_hvi_df (DataFrame): DataFrame containing start HVI data.
        end_hvi_df (DataFrame): DataFrame containing end HVI data.
        start_roi_df (DataFrame): DataFrame containing start ROI data.
        end_roi_df (DataFrame): DataFrame containing end ROI data.

        Returns:
        DataFrame: Combined and processed DataFrame containing HVI and ROI statistics.
        """

        # Data Processing Starts Here.

        ##########
        # HVI data
        ##########

        # "merge" puts the start and end for each HVI on the same record
        combined_hvi_df = start_hvi_df.merge(
            end_hvi_df,
            how="left",
            on=["store_id", "person_id", "vehicle_id", "frame_id"],
            suffixes=["_start", "_end"],
        )

        # Just some renaming of columns to tidy things up; not strictly necessary, but some renaming would have to be done later anyway
        combined_hvi_df = combined_hvi_df.rename(
            columns={
                "event_datetime_local_start": "hvi_start_dt_local",
                "event_datetime_local_end": "hvi_end_dt_local",
                "lane_number_start": "lane_number",
                "roi_id_start": "roi_id",
                "roi_type_start": "roi_type",
                "total_time_end": "total_time",
            }
        )

        # Re-ordering the columns; again not strictly necessary, just helps to tidy up and make visual inspection of records easier
        combined_hvi_df = combined_hvi_df[
            [
                "store_id",
                "roi_id",
                "roi_type",
                "lane_number",
                "vehicle_id",
                "person_id",
                "frame_id",
                "hvi_start_dt_local",
                "hvi_end_dt_local",
                "total_time",
                "location_description_end",
            ]
        ]

        # Sort by the vehicle ID and HVI start time; this is necessary to be able to compute inter-HVI durations
        combined_hvi_df = combined_hvi_df.sort_values(
            ["vehicle_id", "hvi_start_dt_local", "roi_type"]
        )

        # "cumcount" effectively numbers each item in a group from 0 through to length(group) - 1; when cumcount is 0, that is the first item in the group
        combined_hvi_df["first_hvi"] = (
            combined_hvi_df.groupby(["store_id", "roi_id", "vehicle_id"]).cumcount()
            == 0
        )
        combined_hvi_df["hvi_count"] = combined_hvi_df.groupby(
            ["vehicle_id", "roi_id"]
        )["vehicle_id"].transform("count")

        # Find the previous HVI end time to be able to compute inter-HVI durations
        combined_hvi_df.loc[~combined_hvi_df["first_hvi"], "prev_hvi_end_dt_local"] = (
            combined_hvi_df["hvi_end_dt_local"].shift(1)
        )

        # Compute the inter-HVI duration; this can only be done if there is more than 1 HVI and the HVI is not the first HVI.
        # Note that when (~combined_hvi_df['first_hvi'])) evaluates to FALSE (i.e. it is the first HVI, that is counted as 0, and the result of the entire
        # calculation will be 0, i.e. the HVI duration interval for the first HVI will, or may, be 0. This is not desired, as we will later be doing a mean
        # and this would take into account the 0s. By setting this to NaN (np.nan), these records will be disregarded from the average.

        combined_hvi_df["prev_hvi_interval_duration"] = (
            (
                combined_hvi_df["hvi_start_dt_local"]
                - combined_hvi_df["prev_hvi_end_dt_local"]
            ).dt.total_seconds()
            * (~combined_hvi_df["first_hvi"])
        ).replace(0, np.nan)

        ###########
        ## ROI data
        ###########

        # As for HVI, "merge" puts the start and end for each ROI on the same record
        combined_roi_df = start_roi_df.merge(
            end_roi_df,
            how="left",
            on=["store_id", "roi_id", "vehicle_id"],
            suffixes=["_start", "_end"],
        )

        # Just some renaming of columns to tidy things up; not strictly necessary, but some renaming would have to be done later anyway
        combined_roi_df = combined_roi_df.rename(
            columns={
                "event_datetime_local_start": "roi_start_dt_local",
                "event_datetime_local_end": "roi_end_dt_local",
                "lane_number_start": "lane_number",
                "roi_type_start": "roi_type",
                "total_time_end": "total_time",
            }
        )

        combined_roi_df["roi_duration"] = (
            combined_roi_df["roi_end_dt_local"] - combined_roi_df["roi_start_dt_local"]
        ).astype("timedelta64[s]")

        # Re-ordering the columns; again not strictly necessary, just helps to tidy up and make visual inspection of records easier
        combined_roi_df = combined_roi_df[
            [
                "store_id",
                "vehicle_id",
                "roi_id",
                "roi_type",
                "lane_number",
                "roi_start_dt_local",
                "roi_end_dt_local",
                "total_time",
                "roi_duration",
                "location_description_end",
            ]
        ]

        # #######
        #  Determine HVI Statistics Against Each Store/ROI/Vehicle ID
        # #######

        combined_roi_df = combined_roi_df.merge(
            # we are combining the existing combined_roi_df with the HVI statistics for each Store/ROI/Vehicle ID combination,
            # i.e. every existing combined_roi_df record will get the corresponding HVI data for it, if that exists
            combined_hvi_df.groupby(
                [
                    "store_id",
                    "roi_id",
                    "roi_type",
                    "vehicle_id",
                    "location_description_end",
                ]
            ).agg(
                hvi_count=pd.NamedAgg(column="hvi_count", aggfunc="min"),
                first_hvi_dt_local=pd.NamedAgg(
                    column="hvi_start_dt_local", aggfunc="min"
                ),
                last_hvi_dt_local=pd.NamedAgg(column="hvi_end_dt_local", aggfunc="max"),
                average_hvi_duration=pd.NamedAgg(column="total_time", aggfunc="mean"),
                minimum_hvi_duration=pd.NamedAgg(column="total_time", aggfunc="min"),
                maximum_hvi_duration=pd.NamedAgg(column="total_time", aggfunc="max"),
                average_inter_hvi_duration=pd.NamedAgg(
                    column="prev_hvi_interval_duration", aggfunc="mean"
                ),
            ),
            how="left",
            left_on=[
                "store_id",
                "roi_id",
                "roi_type",
                "vehicle_id",
                "location_description_end",
            ],
            right_index=True,
        )

        # Calculate some HVI statistics for the record
        combined_roi_df["time_to_first_hvi"] = (
            combined_roi_df["first_hvi_dt_local"]
            - combined_roi_df["roi_start_dt_local"]
        ).dt.total_seconds()
        combined_roi_df["time_from_last_hvi"] = (
            combined_roi_df["roi_end_dt_local"] - combined_roi_df["last_hvi_dt_local"]
        ).dt.total_seconds()
        combined_roi_df["time_from_start_of_first_hvi_to_end_of_last_hvi"] = (
            combined_roi_df["last_hvi_dt_local"] - combined_roi_df["first_hvi_dt_local"]
        ).dt.total_seconds()

        # Remove -ve numbers
        combined_roi_df = combined_roi_df.loc[
            (
                combined_roi_df[
                    [
                        "hvi_count",
                        "average_hvi_duration",
                        "minimum_hvi_duration",
                        "maximum_hvi_duration",
                        "time_to_first_hvi",
                        "time_from_last_hvi",
                        "time_from_start_of_first_hvi_to_end_of_last_hvi",
                    ]
                ]
                >= 0
            ).all(axis=1)
        ]

        # Suspect ROIs
        combined_roi_df_suspect = combined_roi_df.loc[
            ~(
                combined_roi_df[
                    [
                        "hvi_count",
                        "average_hvi_duration",
                        "minimum_hvi_duration",
                        "maximum_hvi_duration",
                        "time_to_first_hvi",
                        "time_from_last_hvi",
                        "time_from_start_of_first_hvi_to_end_of_last_hvi",
                    ]
                ]
                >= 0
            ).all(axis=1)
        ]

        # #######
        # Determine HVI Statistics Against Each Store/ROI
        # #######

        # Now we can glean some higher level statistics at the Store/ROI level
        store_roi_stats_from_hvi_df = combined_hvi_df.groupby(
            ["store_id", "roi_id", "roi_type", "vehicle_id", "location_description_end"]
        ).agg(
            average_hvi_duration=pd.NamedAgg(column="total_time", aggfunc="mean"),
            minimum_hvi_duration=pd.NamedAgg(column="total_time", aggfunc="min"),
            maximum_hvi_duration=pd.NamedAgg(column="total_time", aggfunc="max"),
            count_of_hvis=pd.NamedAgg(column="total_time", aggfunc="count"),
            lane_number=pd.NamedAgg(column="lane_number", aggfunc="min"),
            average_inter_hvi_duration=pd.NamedAgg(
                column="prev_hvi_interval_duration", aggfunc="mean"
            ),
            first_hvi_start=pd.NamedAgg(column="hvi_start_dt_local", aggfunc="min"),
            last_hvi_end=pd.NamedAgg(column="hvi_end_dt_local", aggfunc="max"),
        )

        store_roi_stats_from_roi_df = combined_roi_df.groupby(
            ["store_id", "roi_id", "roi_type", "vehicle_id", "location_description_end"]
        ).agg(
            mean_time_to_first_hvi=pd.NamedAgg(
                column="time_to_first_hvi", aggfunc="mean"
            ),
            mean_time_from_last_hvi=pd.NamedAgg(
                column="time_from_last_hvi", aggfunc="mean"
            ),
            event_time_local=pd.NamedAgg(column="roi_start_dt_local", aggfunc="min"),
            roi_start=pd.NamedAgg(column="roi_start_dt_local", aggfunc="min"),
            roi_end=pd.NamedAgg(column="roi_start_dt_local", aggfunc="max"),
            mean_time_from_start_of_first_hvi_to_end_of_last_hvi=pd.NamedAgg(
                column="time_from_start_of_first_hvi_to_end_of_last_hvi", aggfunc="mean"
            ),
        )

        # #######
        # Combine the Statistics
        # #######

        combined_stats_df = store_roi_stats_from_roi_df.merge(
            store_roi_stats_from_hvi_df, how="left", left_index=True, right_index=True
        )

        roi_mapping = {
            "START": "1 START",
            "ORDER": "2 ORDER",
            "order": "2 ORDER",
            "PREDELIVER": "3 PREDELIVER",
            "DELIVER": "4 DELIVER",
            "PRESENTER": "4 DELIVER",
            "ENTRY": "0 ENTRY",
        }

        functional_mapping = {
            "START": "1 ORDER",
            "ORDER": "1 ORDER",
            "order": "1 ORDER",
            "PREDELIVER": "2 DELIVER",
            "DELIVER": "2 DELIVER",
            "PRESENTER": "2 DELIVER",
            "ENTRY": "1 ORDER",
        }
        combined_stats_df = combined_stats_df.reset_index()

        combined_stats_df["mapped_roi"] = combined_stats_df["roi_type"].apply(
            lambda x: roi_mapping.get(x, "0" + x)
        )
        combined_stats_df["mapped_function"] = combined_stats_df["roi_type"].apply(
            lambda x: functional_mapping.get(x, "0" + x)
        )

        # drop roi_type, count_of_hvis
        combined_stats_df.drop(["roi_type"], axis=1, inplace=True)

        # add roi_duration, vehicle_db_id
        combined_stats_df["roi_duration"] = (
            combined_stats_df["roi_end"] - combined_stats_df["roi_start"]
        ).dt.total_seconds()

        # combined_stats_df['roi_duration']= combined_stats_df['roi_duration'].total_seconds()
        combined_stats_df["vehicle_db_id"] = 1

        # rename to match orginal file
        combined_stats_df = combined_stats_df.rename(
            columns={
                "average_hvi_duration": "avg_interaction_duration",
                "minimum_hvi_duration": "min_interaction_duration",
                "maximum_hvi_duration": "max_interaction_duration",
                "first_hvi_start": "first_start_interaction_time_local",
                "last_hvi_end": "last_end_interaction_time_local",
                "mean_time_from_start_of_first_hvi_to_end_of_last_hvi": "start_start_to_end_end_interaction_duration_seconds",
                "mean_time_to_first_hvi": "roi_start_to_hvi_start_duration",
                "mean_time_from_last_hvi": "hvi_end_to_roi_end_duration",
                "average_inter_hvi_duration": "avg_duration_between_hvis",
                "mapped_roi": "roi_type",
                "vehicle_id": "vehicle__id",
                "location_description_end": "store_name",
                "count_of_hvis": "hvi_count",
            }
        )

        # reorder to match current file
        combined_stats_df = combined_stats_df[
            [
                "hvi_count",
                "avg_interaction_duration",
                "min_interaction_duration",
                "max_interaction_duration",
                "first_start_interaction_time_local",
                "last_end_interaction_time_local",
                "start_start_to_end_end_interaction_duration_seconds",
                "roi_start_to_hvi_start_duration",
                "hvi_end_to_roi_end_duration",
                "roi_duration",
                "avg_duration_between_hvis",
                "lane_number",
                "roi_id",
                "roi_type",
                "vehicle_db_id",
                "vehicle__id",
                "store_id",
                "store_name",
                "event_time_local",
                "roi_start",
                "roi_end",
                "mapped_function",
            ]
        ]

        tst = ["1 ORDER", "2 DELIVER"]
        combined_stats_df.drop(
            combined_stats_df[~combined_stats_df["mapped_function"].isin(tst)].index,
            inplace=True,
        )

        return combined_stats_df

    def get_updated_duplicate_removed_df(
        self, combined_stats_df, duplicate_check_start, duplicate_check_end
    ):
        # Remove duplicates from newly calculated df
        duplicates_check_query = f"""
            SELECT *
                FROM eyecue_cfa_usa.hvi_reporting_automation
                WHERE event_time_local BETWEEN '{duplicate_check_start:%Y-%m-%d %H:%M:%S}' AND '{duplicate_check_end:%Y-%m-%d %H:%M:%S}'
            """
        duplicate_check_df = self.get_df_from_sql(duplicates_check_query)

        self.logger.info(f"duplicate_check_df size ---->>>> {len(duplicate_check_df)}")

        # Casting duplicate_check_df to appropriate data types
        duplicate_check_df = duplicate_check_df.astype(
            {
                "hvi_reporting_automation_id": "int64",
                "hvi_count": "int64",
                "avg_interaction_duration": "float64",
                "min_interaction_duration": "float64",
                "max_interaction_duration": "float64",
                "first_start_interaction_time_local": "datetime64[us]",
                "last_end_interaction_time_local": "datetime64[us]",
                "start_start_to_end_end_interaction_duration_seconds": "float64",
                "roi_start_to_hvi_start_duration": "float64",
                "hvi_end_to_roi_end_duration": "float64",
                "roi_duration": "float64",
                "avg_duration_between_hvis": "float64",
                "lane_number": "Int64",
                "roi_id": "string",
                "roi_type": "string",
                "vehicle_db_id": "int64",
                "vehicle__id": "string",
                "store_id": "string",
                "store_name": "string",
                "event_time_local": "datetime64[us]",
                "roi_start": "datetime64[us]",
                "roi_end": "datetime64[us]",
                "mapped_function": "string",
            }
        )

        # Define the key columns for grouping
        key_columns = ["store_id", "vehicle__id"]

        # Group the combined_stats_df and duplicate_check_df by key columns
        combined_grouped = combined_stats_df.groupby(key_columns)
        duplicate_grouped = duplicate_check_df.groupby(key_columns)

        # DataFrame to hold rows that need further processing
        rows_to_process = []

        # Iterate over the groups in combined_stats_df
        for key, combined_group in combined_grouped:
            if key in duplicate_grouped.groups:
                duplicate_group = duplicate_grouped.get_group(key)

                # Compare the lengths of the grouped results
                if len(combined_group) != len(duplicate_group):
                    # Lengths are not equal, meaning new data is in combined_stats_df
                    # Collect the IDs of rows from duplicate_check_df that need to be removed
                    rows_to_process.append(duplicate_group)
                    # Remove these combinations from duplicate_check_df
                    # duplicate_check_df = duplicate_check_df[
                    #     ~duplicate_check_df.index.isin(duplicate_group.index)
                    # ]
                else:
                    # Lengths are equal, remove these rows from combined_stats_df
                    combined_stats_df = combined_stats_df[
                        ~combined_stats_df.index.isin(combined_group.index)
                    ]

        # Concatenate rows that need further processing into a DataFrame
        if rows_to_process:
            rows_to_process_df = pd.concat(rows_to_process).reset_index(drop=True)
            # Collect 'hvi_reporting_automation_id' from rows_to_process_df
            hvi_reporting_deletion_ids = rows_to_process_df[
                "hvi_reporting_automation_id"
            ].tolist()
        else:
            hvi_reporting_deletion_ids = []

        # Reset index for combined_stats_df after modifications
        combined_stats_df = combined_stats_df.reset_index(drop=True)

        return combined_stats_df, hvi_reporting_deletion_ids

    def should_stop_execution(self, date_time_now_utc, end_date):
        # Get current date time (UTC) as a string in the format 'YYYY-MM-DD HH:MM:SS'
        current_date_time_str = date_time_now_utc.strftime("%Y-%m-%d %H:%M:%S")
        end_date_str = end_date.strftime("%Y-%m-%d %H:%M:%S")

        # Convert both strings to datetime objects for comparison
        current_date_time = datetime.strptime(
            current_date_time_str, "%Y-%m-%d %H:%M:%S"
        )
        end_date_time = datetime.strptime(end_date_str, "%Y-%m-%d %H:%M:%S")

        return current_date_time <= end_date_time

    # Define a function to combine dataframes with prv and next dataframes
    def merge_with_prv_next(self, main_df, prv_df, next_df):
        # Step 1: Get all unique vehicle__id and store_id combinations from the main dataframe
        unique_combinations = main_df[["vehicle_id", "store_id"]].drop_duplicates()

        # Step 2: Find all records that match those unique combinations in prv and next dataframes
        merged_prv = prv_df.merge(
            unique_combinations, on=["vehicle_id", "store_id"], how="inner"
        )
        merged_next = next_df.merge(
            unique_combinations, on=["vehicle_id", "store_id"], how="inner"
        )

        # Step 3: Append those matching records to the main dataframe
        updated_df = pd.concat([main_df, merged_prv, merged_next], ignore_index=True)

        # Step 4: Remove duplicate rows based on specific key columns
        key_columns = ["store_id", "vehicle_id", "roi_id", "event_datetime_local"]
        updated_df = updated_df.drop_duplicates(subset=key_columns, keep="first")

        return updated_df

    def delete_from_hvi_reporting_automation(self, hvi_reporting_deletion_ids):
        if hvi_reporting_deletion_ids:
            hvi_reporting_automation_ids_str = ", ".join(
                [str(id) for id in hvi_reporting_deletion_ids]
            )

            delete_query = f"""
                DELETE FROM eyecue_cfa_usa.hvi_reporting_automation
                WHERE hvi_reporting_automation_id IN ({hvi_reporting_automation_ids_str})
            """

            self.logger.info(
                f"Deleting rows from hvi_reporting_automation: {delete_query}"
            )

            self.db_manager.execute_query(delete_query)

    def generate_statistics(
        self,
        data_select_gap_mins,
        duplicate_check_gap_mins,
        next_execution_gap_mins,
        s3_bucket,
    ):
        """
        Generate HVI report statistics and store results in S3 and Redshift.

        Parameters:
        data_select_gap_mins (int): Time gap in minutes for selecting data.
        duplicate_check_gap_mins (int): Time gap in minutes for checking duplicates.
        next_execution_gap_mins (int): Time gap in minutes for the next execution.
        s3_bucket (str): S3 bucket name for storing the generated statistics.
        """

        try:
            # Arrange the data selection and duplicate check date ranges

            # Here take equal dates in the sql query
            next_execution_date_time = self.dynamodb_manager.get_next_execution_date()
            data_selection_start_date = next_execution_date_time - timedelta(
                minutes=data_select_gap_mins
            )
            data_selection_end_date = next_execution_date_time

            date_time_now_utc = datetime.now(tz=dt.timezone.utc)

            self.logger.info(f"Current date time(UTC) {date_time_now_utc}")
            if self.should_stop_execution(date_time_now_utc, data_selection_end_date):
                self.logger.info(
                    f"Current date time(UTC) {date_time_now_utc} is less than or equal to data_selection_end_date(UTC) {data_selection_end_date}. Stopping execution."
                )
                return

            prv_data_selection_start_date = data_selection_start_date - timedelta(
                minutes=data_select_gap_mins
            )
            prv_data_selection_end_date = data_selection_start_date

            next_data_selection_start_date = data_selection_end_date
            next_data_selection_end_date = data_selection_end_date + timedelta(
                minutes=data_select_gap_mins
            )

            duplicate_check_start = next_execution_date_time - timedelta(
                minutes=duplicate_check_gap_mins
            )
            duplicate_check_end = next_execution_date_time + timedelta(
                minutes=duplicate_check_gap_mins
            )

            next_execution_date_time_new = next_execution_date_time + timedelta(
                minutes=next_execution_gap_mins
            )

            self.logger.info(
                f"Data Selection Range: {data_selection_start_date} -- {data_selection_end_date}"
            )

            self.logger.info(
                f"Prv Date Range: {prv_data_selection_start_date} -- {prv_data_selection_end_date}"
            )

            self.logger.info(
                f"Next Date Range: {next_data_selection_start_date} -- {next_data_selection_end_date}"
            )

            self.logger.info(
                f"Duplicate Check Date Range: {duplicate_check_start} -- {duplicate_check_end}"
            )

            # Generate SQL queries for HVI and ROI data selection
            hvi_selection_start_query = get_hvi_roi_query(
                "hvi", "start", data_selection_start_date, data_selection_end_date, True
            )
            hvi_selection_end_query = get_hvi_roi_query(
                "hvi", "end", data_selection_start_date, data_selection_end_date, True
            )
            roi_selection_start_query = get_hvi_roi_query(
                "roi", "start", data_selection_start_date, data_selection_end_date, True
            )
            roi_selection_end_query = get_hvi_roi_query(
                "roi", "end", data_selection_start_date, data_selection_end_date, True
            )

            # Generate SQL queries for HVI and ROI data selection for previous data
            hvi_prv_start_query = get_hvi_roi_query(
                "hvi",
                "start",
                prv_data_selection_start_date,
                prv_data_selection_end_date,
            )
            hvi_prv_end_query = get_hvi_roi_query(
                "hvi", "end", prv_data_selection_start_date, prv_data_selection_end_date
            )
            roi_prv_start_query = get_hvi_roi_query(
                "roi",
                "start",
                prv_data_selection_start_date,
                prv_data_selection_end_date,
            )
            roi_prv_end_query = get_hvi_roi_query(
                "roi", "end", prv_data_selection_start_date, prv_data_selection_end_date
            )

            # Generate SQL queries for HVI and ROI data selection for next data
            hvi_next_start_query = get_hvi_roi_query(
                "hvi",
                "start",
                next_data_selection_start_date,
                next_data_selection_end_date,
            )
            hvi_next_end_query = get_hvi_roi_query(
                "hvi",
                "end",
                next_data_selection_start_date,
                next_data_selection_end_date,
            )
            roi_next_start_query = get_hvi_roi_query(
                "roi",
                "start",
                next_data_selection_start_date,
                next_data_selection_end_date,
            )
            roi_next_end_query = get_hvi_roi_query(
                "roi",
                "end",
                next_data_selection_start_date,
                next_data_selection_end_date,
            )

            # Log queries

            # self.logger.info(
            #     "===================================== START: LOG QUERIES ====================================="
            # )
            # self.logger.info(f"hvi_selection_start_query: {hvi_selection_start_query}")
            # self.logger.info(f"hvi_selection_end_query: {hvi_selection_end_query}")
            # self.logger.info(f"roi_selection_start_query: {roi_selection_start_query}")
            # self.logger.info(f"roi_selection_end_query: {roi_selection_end_query}")

            # self.logger.info(f"hvi_prv_start_query: {hvi_prv_start_query}")
            # self.logger.info(f"hvi_prv_end_query: {hvi_prv_end_query}")
            # self.logger.info(f"roi_prv_start_query: {roi_prv_start_query}")
            # self.logger.info(f"roi_prv_end_query: {roi_prv_end_query}")

            # self.logger.info(f"hvi_next_start_query: {hvi_next_start_query}")
            # self.logger.info(f"hvi_next_end_query: {hvi_next_end_query}")
            # self.logger.info(f"roi_next_start_query: {roi_next_start_query}")
            # self.logger.info(f"roi_next_end_query: {roi_next_end_query}")
            # self.logger.info(
            #     "===================================== END: LOG QUERIES ====================================="
            # )

            # Fetch data and handle any errors
            # query_execution_start_time = time.time()

            # try:
            #     start_hvi_df = self.get_df_from_sql(hvi_selection_start_query)
            #     end_hvi_df = self.get_df_from_sql(hvi_selection_end_query)
            #     start_roi_df = self.get_df_from_sql(roi_selection_start_query)
            #     end_roi_df = self.get_df_from_sql(roi_selection_end_query)

            #     start_hvi_df_prv = self.get_df_from_sql(hvi_prv_start_query)
            #     end_hvi_df_prv = self.get_df_from_sql(hvi_prv_end_query)
            #     start_roi_df_prv = self.get_df_from_sql(roi_prv_start_query)
            #     end_roi_df_prv = self.get_df_from_sql(roi_prv_end_query)

            #     start_hvi_df_next = self.get_df_from_sql(hvi_next_start_query)
            #     end_hvi_df_next = self.get_df_from_sql(hvi_next_end_query)
            #     start_roi_df_next = self.get_df_from_sql(roi_next_start_query)
            #     end_roi_df_next = self.get_df_from_sql(roi_next_end_query)

            # except Exception as e:
            #     self.logger.error(f"Error fetching data from Redshift: {str(e)}")
            #     raise

            # query_execution_end_time = time.time()
            # total_query_exec_time = (
            #     query_execution_end_time - query_execution_start_time
            # )
            # self.logger.info(
            #     f"Time taken to execute queries =======>>> : {total_query_exec_time:.2f} seconds"
            # )

            # # Print DataFrame sizes
            # self.logger.info(f"start_hvi_df size: {len(start_hvi_df)}")
            # self.logger.info(f"end_hvi_df size: {len(end_hvi_df)}")
            # self.logger.info(f"start_roi_df size: {len(start_roi_df)}")
            # self.logger.info(f"end_roi_df size: {len(end_roi_df)}")
            # self.logger.info(f"start_hvi_df_prv size: {len(start_hvi_df_prv)}")
            # self.logger.info(f"end_hvi_df_prv size: {len(end_hvi_df_prv)}")
            # self.logger.info(f"start_roi_df_prv size: {len(start_roi_df_prv)}")
            # self.logger.info(f"end_roi_df_prv size: {len(end_roi_df_prv)}")
            # self.logger.info(f"start_hvi_df_next size: {len(start_hvi_df_next)}")
            # self.logger.info(f"end_hvi_df_next size: {len(end_hvi_df_next)}")
            # self.logger.info(f"start_roi_df_next size: {len(start_roi_df_next)}")
            # self.logger.info(f"end_roi_df_next size: {len(end_roi_df_next)}")

            query_execution_start_time_2 = time.time()

            # #######=
            try:
                # Define a function to get DataFrame from SQL query
                def fetch_data(sql_query):
                    return self.get_df_from_sql(sql_query)

                # Define all queries to be executed
                queries = [
                    hvi_selection_start_query,
                    hvi_selection_end_query,
                    roi_selection_start_query,
                    roi_selection_end_query,
                    hvi_prv_start_query,
                    hvi_prv_end_query,
                    roi_prv_start_query,
                    roi_prv_end_query,
                    hvi_next_start_query,
                    hvi_next_end_query,
                    roi_next_start_query,
                    roi_next_end_query,
                ]

                # Dictionary to hold the result DataFrames
                results = {}

                # Use ThreadPoolExecutor to run database queries in parallel
                with concurrent.futures.ThreadPoolExecutor(max_workers=4) as executor:
                    # Start the operations and mark each future with its query label
                    future_to_query = {
                        executor.submit(fetch_data, query): idx
                        for idx, query in enumerate(queries)
                    }

                    for future in concurrent.futures.as_completed(future_to_query):
                        query_idx = future_to_query[future]
                        try:
                            results[query_idx] = future.result()
                        except Exception as e:
                            self.logger.error(
                                f"Error fetching data from Redshift for query index {query_idx}: {str(e)}"
                            )
                            raise

                # Assign the results to respective DataFrames after all are complete
                start_hvi_df = results[0]
                end_hvi_df = results[1]
                start_roi_df = results[2]
                end_roi_df = results[3]
                start_hvi_df_prv = results[4]
                end_hvi_df_prv = results[5]
                start_roi_df_prv = results[6]
                end_roi_df_prv = results[7]
                start_hvi_df_next = results[8]
                end_hvi_df_next = results[9]
                start_roi_df_next = results[10]
                end_roi_df_next = results[11]

            except Exception as e:
                self.logger.error(f"Error fetching data from Redshift: {str(e)}")
                raise

            query_execution_end_time2 = time.time()
            total_query_exec_time2 = (
                query_execution_end_time2 - query_execution_start_time_2
            )
            self.logger.info(
                f"Time taken to execute multiple =======>>> : {total_query_exec_time2:.2f} seconds"
            )

            # Print DataFrame sizes
            self.logger.info(f"start_hvi_df size: {len(start_hvi_df)}")
            self.logger.info(f"end_hvi_df size: {len(end_hvi_df)}")
            self.logger.info(f"start_roi_df size: {len(start_roi_df)}")
            self.logger.info(f"end_roi_df size: {len(end_roi_df)}")
            self.logger.info(f"start_hvi_df_prv size: {len(start_hvi_df_prv)}")
            self.logger.info(f"end_hvi_df_prv size: {len(end_hvi_df_prv)}")
            self.logger.info(f"start_roi_df_prv size: {len(start_roi_df_prv)}")
            self.logger.info(f"end_roi_df_prv size: {len(end_roi_df_prv)}")
            self.logger.info(f"start_hvi_df_next size: {len(start_hvi_df_next)}")
            self.logger.info(f"end_hvi_df_next size: {len(end_hvi_df_next)}")
            self.logger.info(f"start_roi_df_next size: {len(start_roi_df_next)}")
            self.logger.info(f"end_roi_df_next size: {len(end_roi_df_next)}")

            # Preprpcess data before calculating statistics
            size_before_start_hvi_df = len(start_hvi_df)
            size_before_end_hvi_df = len(end_hvi_df)
            size_before_start_roi_df = len(start_roi_df)
            size_before_end_roi_df = len(end_roi_df)

            start_hvi_df = self.merge_with_prv_next(
                start_hvi_df, start_hvi_df_prv, start_hvi_df_next
            )
            end_hvi_df = self.merge_with_prv_next(
                end_hvi_df, end_hvi_df_prv, end_hvi_df_next
            )
            start_roi_df = self.merge_with_prv_next(
                start_roi_df, start_roi_df_prv, start_roi_df_next
            )
            end_roi_df = self.merge_with_prv_next(
                end_roi_df, end_roi_df_prv, end_roi_df_next
            )

            size_after_start_hvi_df = len(start_hvi_df)
            size_after_end_hvi_df = len(end_hvi_df)
            size_after_start_roi_df = len(start_roi_df)
            size_after_end_roi_df = len(end_roi_df)

            self.logger.info(
                f"start_hvi_df before:after ---->>>> {size_before_start_hvi_df}:{size_after_start_hvi_df}"
            )
            self.logger.info(
                f"end_hvi_df before:after ---->>>> {size_before_end_hvi_df}:{size_after_end_hvi_df}"
            )
            self.logger.info(
                f"start_roi_df before:after ---->>>> {size_before_start_roi_df}:{size_after_start_roi_df}"
            )
            self.logger.info(
                f"end_roi_df before:after ---->>>> {size_before_end_roi_df}:{size_after_end_roi_df}"
            )

            # Format data
            try:
                start_hvi_df = format_data_types(start_hvi_df)
                end_hvi_df = format_data_types(end_hvi_df)
                start_roi_df = format_data_types(start_roi_df)
                end_roi_df = format_data_types(end_roi_df)

            except Exception as e:
                self.logger.error(f"Error formatting data types: {str(e)}")
                raise

            # Process and generate statistics
            try:
                combined_stats_df = self.process_data(
                    start_hvi_df, end_hvi_df, start_roi_df, end_roi_df
                )
            except Exception as e:
                self.logger.error(f"Error processing data: {str(e)}")
                raise

            self.logger.info(
                f"Processed Results Rows before Duplicate Removal --> {len(combined_stats_df)}"
            )

            # Remove duplicates from calculated data
            try:
                filtered_combined_stats_df, hvi_reporting_deletion_ids = (
                    self.get_updated_duplicate_removed_df(
                        combined_stats_df, duplicate_check_start, duplicate_check_end
                    )
                )

            except Exception as e:
                self.logger.error(f"Error removing duplicates: {str(e)}")
                raise

            self.logger.info(
                f"Processed Results Rows after Duplicate Removal --> {len(filtered_combined_stats_df)}"
            )
            self.logger.info(
                f"Rows to be removed from hvi_reporting_automation: {len(hvi_reporting_deletion_ids)}"
            )

            # Delete rows from table hvi_reporting_automation
            try:
                if len(hvi_reporting_deletion_ids) > 0:
                    self.delete_from_hvi_reporting_automation(
                        hvi_reporting_deletion_ids
                    )

            except Exception as e:
                self.logger.error(
                    f"Error deleting rows from hvi_reporting_automation: {str(e)}"
                )
                raise

            # Save to S3
            try:
                file_name = f"{data_selection_start_date.strftime('%Y-%m-%d %H:%M:%S')}_{data_selection_end_date.strftime('%Y-%m-%d %H:%M:%S')}"
                file_key = self.s3_manager.save_df_to_s3(
                    filtered_combined_stats_df, file_name
                )
                self.logger.info(f"Generated parquet file name: {file_name}")
            except Exception as e:
                self.logger.error(f"Error saving data to S3: {str(e)}")
                raise

            # Copy data to Redshift
            try:
                self.db_manager.copy_data_to_redshift(
                    s3_bucket,
                    file_key,
                    self.redshift_copy_iam,
                    self.destination_schema,
                    self.destination_table,
                )

            except Exception as e:
                self.logger.error(f"Error when copy data to Redshift: {str(e)}")
                raise

            # Update DynamoDB next_execution_date if all steps are successful
            self.dynamodb_manager.set_next_execution_date(next_execution_date_time_new)
            self.logger.info("Successfully updated next_execution_date in DynamoDB")

        except Exception as e:
            self.logger.error(f"Error in HVI Report Generation: {str(e)}")
            raise
