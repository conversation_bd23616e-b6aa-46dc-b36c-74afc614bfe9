import json
import boto3
import os
import logging

import database_module
import s3_module
import dynamodb_module
import hvi_report_module

# Set up logging
logger = logging.getLogger()
logger.setLevel("INFO")


def lambda_handler(event, context):
    """
    AWS Lambda function to generate HVI report data.

    This function performs the following actions:
    1. Retrieves necessary configuration values from environment variables.
    2. Initializes instances for managing Redshift, S3, and DynamoDB interactions.
    3. Generates HVI report statistics based on the provided configurations.
    4. Returns a status response indicating the success or failure of the operation.

    Parameters:
    event (dict): The input event to the Lambda function, containing triggering information.
    context (LambdaContext): The runtime information of the Lambda function.

    Returns:
    dict: Response containing status code and message.
    """

    try:
        # Load environment variables for configurations
        redshift_cluster_id = os.environ["REDSHIFT_CLUSTER_ID"]
        redshift_database = os.environ["DATABASE"]
        redshift_db_user = os.environ["DB_USER"]
        s3_bucket = os.environ["S3_BUCKET"]
        redshift_copy_iam = os.environ["REDSHIFT_COPY_IAM"]
        dynamodb_table_name = os.environ["DYNAMODB_TABLE_NAME"]
        data_select_gap_mins = int(os.environ["DATA_SELECT_GAP_HVI_ROI_MINS"])
        duplicate_check_gap_mins = int(os.environ["DUPLICATE_CHECK_GAP_HVI_ROI_MINS"])
        next_execution_gap_mins = int(
            os.environ["NEXT_EXECUTION_DATE_GAP_HVI_ROI_MINS"]
        )
        destination_schema = os.environ["DESTINATION_SCHEMA_HVI_ROI_REPORTING"]
        destination_table = os.environ["DESTINATION_TABLE_HVI_ROI_REPORTING"]

        # Initialize the DatabaseManager instance for interacting with Redshift
        db_manager = database_module.DatabaseManager(
            redshift_cluster_id, redshift_database, redshift_db_user, logger
        )

        # Initialize the S3Manager instance for interacting with S3
        s3_manager = s3_module.S3Manager(s3_bucket, logger)

        # Initialize the DynamoDBManager instance for interacting with DynamoDB
        dynamodb_manager = dynamodb_module.DynamoDBManager(dynamodb_table_name, logger)

        # Create an instance of the HviReportGenerator to generate report statistics
        hvi_report_generator = hvi_report_module.HviReportGenerator(
            boto3,
            redshift_copy_iam,
            db_manager,
            s3_manager,
            dynamodb_manager,
            destination_schema,
            destination_table,
            logger,
        )

        # Generate report statistics and store results in S3 and DynamoDB
        hvi_report_generator.generate_statistics(
            data_select_gap_mins,
            duplicate_check_gap_mins,
            next_execution_gap_mins,
            s3_bucket,
        )

        # Return a successful response indicating that the report data was generated successfully
        return {
            "statusCode": 200,
            "body": json.dumps("HVI Report Data Successfully Generated!"),
        }

    except Exception as e:
        # Log the error and return a failure response
        logger.error(f"Error in lambda_handler: {str(e)}")
        return {
            "statusCode": 500,
            "body": json.dumps("Error occurred during processing"),
        }
