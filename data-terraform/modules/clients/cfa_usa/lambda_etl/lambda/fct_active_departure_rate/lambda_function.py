import json
import boto3
import os
import re
import pandas as pd
import sql_data as sql

class RedshiftClient:
    def __init__(self, aws_secret_name, profile=None, region_name=None, cluster_id=None):
        self.secret_name = aws_secret_name
        self.profile = profile
        self.region_name = region_name
        self.cluster_id = cluster_id

        self.session = boto3.Session(region_name=self.region_name, profile_name=self.profile)
        self.secret = self.get_aws_secret()
        self.execute_params = self.get_execute_params()
        self.redshift_client = self.session.client("redshift-data")

    def get_aws_secret(self):
        try:
            client = self.session.client("secretsmanager")
            response = client.get_secret_value(SecretId=self.secret_name)
            secret = json.loads(response['SecretString'])
            return secret
        except Exception as e:
            print(f"Error getting secret {self.secret_name}, Exception: {e}")
            return None

    def get_execute_params(self):
        if not self.secret:
            raise ValueError("Secret retrieval failed; can't continue without DB credentials.")

        db_user = self.secret.get("username", self.secret.get("USERNAME"))
        database = self.secret.get("dbname", self.secret.get("DATABASE"))
        host = self.secret.get("host", self.secret.get("HOST"))

        if self.cluster_id:
            cluster_identifier = self.cluster_id
        else:
            match = re.match(r"([^.]+)", host) if host else None
            if match:
                cluster_identifier = match.group(1)
            else:
                raise ValueError("Invalid or missing host for extracting ClusterIdentifier.")

        execute_params = {
            'ClusterIdentifier': cluster_identifier,
            'Database': database,
            'DbUser': db_user
        }
        return execute_params

    def execute_query(self, sql, params=None):
        try:
            if not sql:
                raise Exception("SQL statement is missing or empty.")

            query_payload = self.execute_params.copy()
            query_payload['Sql'] = sql

            if params:
                query_payload['Parameters'] = params

            response = self.redshift_client.execute_statement(**query_payload)
            execution_id = response['Id']
            print(f"Execution ID: {execution_id}")
            return execution_id
        except Exception as e:
            print(f"Error executing query {self.secret_name}, Exception: {e}")
            return None

    def get_statement_result(self, execution_id):
        while True:
            statement_status, status = self.get_statement_status(execution_id)
            if status == 'FINISHED':
                break
            elif status == 'FAILED':
                raise Exception(f"Query execution failed with status: {status}. Exception: {statement_status.get('Error')}")

        result = self.redshift_client.get_statement_result(Id=execution_id)
        columns = [col['label'] for col in result['ColumnMetadata']]
        records = result['Records']
        data = [
            {col: list(record[col_index].values())[0] for col_index, col in enumerate(columns)}
            for record in records
        ]
        return pd.DataFrame(data, columns=columns)

    def get_statement_status(self, execution_id):
        statement_status = self.redshift_client.describe_statement(Id=execution_id)
        return statement_status, statement_status['Status']


def lambda_handler(event, context):
    aws_secret_name = os.environ["SECRET_NAME"]
    try:
        eyecue_redshift = RedshiftClient(
            aws_secret_name=aws_secret_name,
            region_name=os.environ["REGION_NAME"]
        )

        print("Inserting Data into fct_mark_deliver_end Table ...")
        query_1_execution_id = eyecue_redshift.execute_query(sql.query_1)

        if not query_1_execution_id:
            raise Exception("Query execution failed – no execution ID returned.")

        while True:
            statement_status, status = eyecue_redshift.get_statement_status(query_1_execution_id)
            if status == 'FINISHED':
                break
            elif status == 'FAILED':
                raise Exception(f"Query1 execution failed with status: {status}")

        print(f"Query 1 execution status: {statement_status}")
        print("Data Inserted Successfully.")

        return {
            "statusCode": 200,
            "body": json.dumps("Materialized View Refreshed and Data Inserted Successfully!")
        }

    except Exception as e:
        print(f"Error executing SQL: {e}")
        return {"statusCode": 500, "body": json.dumps(f"Error: {str(e)}")}
