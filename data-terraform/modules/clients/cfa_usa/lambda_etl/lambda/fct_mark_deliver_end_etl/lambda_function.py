import json
import boto3
import time
import os

# Initialize Redshift Data API client
redshift_data = boto3.client("redshift-data")

# Define the Redshift cluster and database details from environment variables
REDSHIFT_CLUSTER_ID = os.environ["REDSHIFT_CLUSTER_ID"]
DATABASE = os.environ["DATABASE"]
DB_USER = os.environ["DB_USER"]
REGION_NAME = os.environ["REGION_NAME"]

# Define the SQL queries
REFRESH_MV_SQL = (
    "REFRESH MATERIALIZED VIEW eyecue_cfa_usa.mv_last_2days_mark_deliver_end;"
)
INSERT_SQL_HVI_START = """
    INSERT INTO
    eyecue_cfa_usa.fct_mark_deliver_end (
        hash_key,
        fingermark_id,
        deliver_lane,
        mark_deliver_end,
        vehicle_deletion_time_str,
        vehicle_deletion_time_local,
        vehicle_deletion_time_utc,
        meta_kinesis_datastream_arrival_datetime_utc
    )
WITH
    latest_record AS (
        SELECT MAX(
                meta_kinesis_datastream_arrival_datetime_utc
            ) AS latest_datetime
        FROM eyecue_cfa_usa.fct_mark_deliver_end
    )
SELECT
    hash_key,
    fingermark_id,
    deliver_lane,
    mark_deliver_end,
    vehicle_deletion_time_str,
    vehicle_deletion_time_local,
    vehicle_deletion_time_utc,
    meta_kinesis_datastream_arrival_datetime_utc
from eyecue_cfa_usa.mv_last_2days_mark_deliver_end
WHERE
    meta_kinesis_datastream_arrival_datetime_utc > (
        SELECT latest_datetime
        FROM latest_record
    )
"""


def execute_sql(sql_statement):
    """
    Function to execute an SQL statement using the Redshift Data API.
    """
    response = redshift_data.execute_statement(
        ClusterIdentifier=REDSHIFT_CLUSTER_ID,
        Database=DATABASE,
        DbUser=DB_USER,
        Sql=sql_statement,
    )

    # Check the execution id for the SQL statement
    query_id = response["Id"]

    # Wait for query to complete
    status = redshift_data.describe_statement(Id=query_id)
    while status["Status"] in ["SUBMITTED", "PICKED", "STARTED"]:
        # time.sleep(1)  # Wait before checking the status again
        status = redshift_data.describe_statement(Id=query_id)

    # Check for errors
    if status["Status"] == "FAILED":
        raise Exception(f"SQL execution failed: {status['Error']}")

    return status


def lambda_handler(event, context):
    try:
        # Step 1: Refresh Materialized View
        print("Refreshing Materialized View...")
        refresh_status = execute_sql(REFRESH_MV_SQL)
        print(refresh_status)
        print("Materialized View Refreshed Successfully.")

        # Step 2: Insert Data Fact Table
        print("Inserting Data into fct_mark_deliver_end Table ...")
        insert_status = execute_sql(INSERT_SQL_HVI_START)
        print(insert_status)
        print("Data Inserted Successfully.")

        return {
            "statusCode": 200,
            "body": json.dumps(
                "Materialized View Refreshed and Data Inserted Successfully!"
            ),
        }

    except Exception as e:
        print(f"Error executing SQL: {e}")
        return {"statusCode": 500, "body": json.dumps(f"Error: {str(e)}")}
