variable "secrets_manager_arns" {
  description = "List of ARNs of the Secrets Manager secrets"
  type        = list(string)
}

variable "tags" {
  description = "Additional tags to apply to resources"
  type        = map(string)
  default     = {}
}
variable "redshift_cluster_id" {
  description = "Redshift cluster identifier"
  type        = string
}

variable "database" {
  description = "Database name"
  type        = string
}

variable "db_user" {
  description = "Database user"
  type        = string
}

variable "secret_name" {
  description = "Secrets Manager secret name"
  type        = string
}

variable "region_name" {
  description = "AWS region name"
  type        = string
}

variable "s3_bucket_name" {
  description = "S3 bucket to upload Lambda function code"
  type        = string
  default     = "your-s3-bucket-name"
}

variable "notification_arns" {
  description = "ARN for SNS topic to send notifications"
  type        = list(string)
  default     = null
}

