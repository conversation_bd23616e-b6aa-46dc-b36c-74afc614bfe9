terraform {
  required_providers {
    archive = {
      source  = "hashicorp/archive"
      version = "~> 2.0"
    }
    aws = {
      source = "hashicorp/aws"
    }
  }
}

locals {
  mandatory_tags = {
    "Application" = "Data-Warehouse"
    "Author"      = "Data-Team"
    "MultiRegion" = "False"
    "Terraform"   = "True"
    "Product"     = "Data"
    "Squad"       = "Data"
    "Stack"       = "ETL"
  }

  all_tags = merge(local.mandatory_tags, var.tags)
}

# Create S3 bucket
resource "aws_s3_bucket" "lambda_bucket" {
  bucket = var.s3_bucket_name
  tags   = local.all_tags

}

resource "aws_s3_bucket_public_access_block" "lambda_bucket" {
  bucket                  = aws_s3_bucket.lambda_bucket.id
  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}
resource "aws_s3_bucket_ownership_controls" "lambda_bucket" {
  bucket = aws_s3_bucket.lambda_bucket.id
  rule {
    object_ownership = "BucketOwnerPreferred"
  }
}

resource "aws_s3_bucket_versioning" "lambda_bucket" {
  depends_on = [aws_s3_bucket_ownership_controls.lambda_bucket]
  bucket     = aws_s3_bucket.lambda_bucket.id
  versioning_configuration {
    status = "Enabled"
  }
}

# Dynamodb table to track email sending dates
resource "aws_dynamodb_table" "daily_email_execution_tracking_table" {
  name         = "tim_can_daily_email_execution_days"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "date"

  attribute {
    name = "date"
    type = "S"
  }

  point_in_time_recovery {
    enabled = true
  }

  tags = var.tags
}

# Daily email sending initializer lambda
data "archive_file" "daily_email_initializer" {
  type        = "zip"
  source_dir  = "${path.module}/lambda/daily_email_initializer"
  output_path = "${path.module}/lambda/daily_email_initializer.zip"
}

resource "aws_s3_object" "daily_email_initializer" {
  bucket = aws_s3_bucket.lambda_bucket.bucket
  key    = "lambda/daily_email_initializer.zip"
  source = data.archive_file.daily_email_initializer.output_path

  # Compute hash only from lambda_function.py to trigger archive update only when this file changes
  source_hash = filemd5("${path.module}/lambda/daily_email_initializer/lambda_function.py")
}

# Daily email sending lambda
data "archive_file" "daily_email_sender" {
  type        = "zip"
  source_dir  = "${path.module}/lambda/daily_email_sender"
  output_path = "${path.module}/lambda/daily_email_sender.zip"
}

resource "aws_s3_object" "daily_email_sender" {
  bucket = aws_s3_bucket.lambda_bucket.bucket
  key    = "lambda/daily_email_sender.zip"
  source = data.archive_file.daily_email_sender.output_path

  # Compute hash only from lambda_function.py to trigger archive update only when this file changes
  source_hash = filemd5("${path.module}/lambda/daily_email_sender/lambda_function.py")
}

# Deploy CloudFormation Stack
resource "aws_cloudformation_stack" "lambda_application_stack" {
  name = "daily-email-tim-can-stack"
  template_body = jsonencode({
    AWSTemplateFormatVersion = "2010-09-09"
    Description              = "Lambda application for sending daily emails to TIM CAN"
    Resources = {
      DailyEmailSendingInitializerFn = {
        Type = "AWS::Lambda::Function"
        Properties = {
          FunctionName  = "daily_email_sending_initializer_tim_can"
          Handler       = "lambda_function.lambda_handler"
          Role          = { "Fn::GetAtt" = ["LambdaExecutionRoleForInitializerFn", "Arn"] }
          Runtime       = "python3.12"
          Architectures = ["arm64"]
          MemorySize    = 128
          Code = {
            S3Bucket        = var.s3_bucket_name
            S3Key           = "lambda/daily_email_initializer.zip"
            S3ObjectVersion = aws_s3_object.daily_email_initializer.version_id
          }

          Timeout = 900
          Environment = {
            Variables = {
              REDSHIFT_CLUSTER_ID                  = var.redshift_cluster_id
              DATABASE                             = var.database
              DB_USER                              = var.db_user
              REGION_NAME                          = var.region_name
              DATA_UNLOAD_IAM_ROLE                 = var.data_unload_iam_role
              DESTINATION_S3_BUCKET                = var.s3_bucket_name
              TIME_ZONE                            = var.execution_time_zone
              EXECUTION_TIME_WINDOW_START_TIME     = var.execution_time_window_start_time
              EXECUTION_TIME_WINDOW_END_TIME       = var.execution_time_window_end_time
              EXECUTION_TRACKING_DYNAMO_TABLE_NAME = aws_dynamodb_table.daily_email_execution_tracking_table.name
              SNS_NOTIFICATION_ARNS_RAW            = length(var.notification_arns) > 0 ? join(",", var.notification_arns) : null

            }
          }
        }
      }
      LambdaExecutionRoleForInitializerFn = {
        Type = "AWS::IAM::Role"
        Properties = {
          AssumeRolePolicyDocument = {
            Version = "2012-10-17"
            Statement = [
              {
                Effect = "Allow"
                Principal = {
                  Service = "lambda.amazonaws.com"
                }
                Action = "sts:AssumeRole"
              }
            ]
          }
          Policies = [
            {
              PolicyName = "LambdaPolicy"
              PolicyDocument = {
                Version = "2012-10-17"
                Statement = [
                  {
                    "Effect" : "Allow",
                    "Action" : [
                      "dynamodb:PutItem",
                      "dynamodb:GetItem",
                      "dynamodb:UpdateItem",
                      "dynamodb:DeleteItem",
                      "dynamodb:Scan",
                      "dynamodb:Query"
                    ],
                    "Resource" : aws_dynamodb_table.daily_email_execution_tracking_table.arn
                  },

                  {
                    Effect = "Allow"
                    Action = [
                      "redshift-data:ExecuteStatement",
                      "redshift-data:DescribeStatement",
                      "redshift-data:ListStatements",
                      "redshift:GetClusterCredentials",
                      "redshift-data:GetStatementResult",
                      "secretsmanager:DescribeSecret",
                      "secretsmanager:ListSecretVersionIds",
                      "redshift-data:ListDatabases",
                      "redshift-data:ListTables",
                      "secretsmanager:GetRandomPassword",
                      "secretsmanager:GetResourcePolicy",
                      "secretsmanager:GetSecretValue",
                      "redshift-data:DescribeTable",
                      "redshift-data:ListSchemas",
                      "secretsmanager:ListSecrets"
                    ]
                    Resource : [
                      "arn:aws:redshift:${var.region_name}:${var.aws_account_id}:cluster:${var.redshift_cluster_id}",
                      "arn:aws:redshift:${var.region_name}:${var.aws_account_id}:dbuser:${var.redshift_cluster_id}/${var.db_user}",
                      "arn:aws:redshift:${var.region_name}:${var.aws_account_id}:dbname:${var.redshift_cluster_id}/${var.database}"
                    ]
                  },

                  {
                    Effect = "Allow"
                    Action = [
                      "redshift-data:DescribeStatement",
                      "redshift-data:GetStatementResult"
                    ]
                    Resource : [
                      "*"
                    ]
                  },

                  {
                    Effect = "Allow"
                    Action = [
                      "secretsmanager:GetSecretValue",
                      "logs:CreateLogGroup",
                      "logs:CreateLogStream",
                      "logs:PutLogEvents",
                    ]
                    Resource = "*"
                  }

                ]
              }
            }
          ]
        }
      }
      EventBridgeRuleForInitializerFn = {
        Type = "AWS::Events::Rule"
        Properties = {
          Name               = "daily-email-initialization-every-15m-tim-can"
          ScheduleExpression = "rate(15 minutes)"
          Targets = [
            {
              Arn = { "Fn::GetAtt" = ["DailyEmailSendingInitializerFn", "Arn"] }
              Id  = "DailyEmailSendingInitializerFn"
            }
          ]
        }
      }
      LambdaInvokePermissionForInitializerFn = {
        Type = "AWS::Lambda::Permission"
        Properties = {
          FunctionName = { "Ref" = "DailyEmailSendingInitializerFn" }
          Action       = "lambda:InvokeFunction"
          Principal    = "events.amazonaws.com"
          SourceArn    = { "Fn::GetAtt" = ["EventBridgeRuleForInitializerFn", "Arn"] }
        }
      }

      DailyEmailSenderFn = {
        Type = "AWS::Lambda::Function"
        Properties = {
          FunctionName  = "daily_email_seder_tim_can"
          Handler       = "lambda_function.lambda_handler"
          Role          = { "Fn::GetAtt" = ["LambdaExecutionRoleForEmailSenderFn", "Arn"] }
          Runtime       = "python3.12"
          Architectures = ["arm64"]
          MemorySize    = 128
          Code = {
            S3Bucket        = var.s3_bucket_name
            S3Key           = "lambda/daily_email_sender.zip"
            S3ObjectVersion = aws_s3_object.daily_email_sender.version_id
          }

          Timeout = 900
          Environment = {
            Variables = {
              REGION_NAME                          = var.region_name
              SES_REGION_NAME                      = var.ses_region_name
              EXECUTION_TRACKING_DYNAMO_TABLE_NAME = aws_dynamodb_table.daily_email_execution_tracking_table.name
              SES_SENDER                           = var.ses_sender_email
              SES_RECIPIENT_RAW                    = var.email_receiver_emails
              SES_BCC_EMAIL_RAW                    = var.bcc_emails
              SNS_NOTIFICATION_ARNS_RAW            = length(var.notification_arns) > 0 ? join(",", var.notification_arns) : null

            }
          }
        }
      }

      LambdaExecutionRoleForEmailSenderFn = {
        Type = "AWS::IAM::Role"
        Properties = {
          AssumeRolePolicyDocument = {
            Version = "2012-10-17"
            Statement = [
              {
                Effect = "Allow"
                Principal = {
                  Service = "lambda.amazonaws.com"
                }
                Action = "sts:AssumeRole"
              }
            ]
          }
          Policies = [
            {
              PolicyName = "LambdaPolicy"
              PolicyDocument = {
                Version = "2012-10-17"
                Statement = [
                  {
                    "Effect" : "Allow",
                    "Action" : [
                      "dynamodb:PutItem",
                      "dynamodb:GetItem",
                      "dynamodb:UpdateItem",
                      "dynamodb:DeleteItem",
                      "dynamodb:Scan",
                      "dynamodb:Query"
                    ],
                    "Resource" : aws_dynamodb_table.daily_email_execution_tracking_table.arn
                  },

                  {
                    "Effect" : "Allow",
                    "Action" : [
                      "s3:PutObject",
                      "s3:GetObjectAcl",
                      "s3:GetObject",
                      "s3:ListBucket"
                    ],
                    "Resource" : [
                      "arn:aws:s3:::${var.s3_bucket_name}/*",
                      "arn:aws:s3:::${var.s3_bucket_name}"
                    ]
                  },

                  {
                    "Effect" : "Allow",
                    "Action" : [
                      "ses:SendEmail",
                      "ses:SendRawEmail"
                    ],
                    "Resource" : "*"
                  },

                  {
                    "Effect" : "Allow",
                    "Action" : "ses:*",
                    "Resource" : [
                      "arn:aws:ses:*:${var.aws_account_id}:identity/*",
                      "arn:aws:ses:*:${var.aws_account_id}:configuration-set/*",
                      "arn:aws:ses:*:${var.aws_account_id}:template/*"
                    ]
                  },

                  {
                    Effect = "Allow"
                    Action = [
                      "secretsmanager:GetSecretValue",
                      "logs:CreateLogGroup",
                      "logs:CreateLogStream",
                      "logs:PutLogEvents",
                    ]
                    Resource = "*"
                  }

                ]
              }
            }
          ]
        }
      }


    },

    Outputs = {
      DailyEmailSenderFnArn = {
        Value = { "Fn::GetAtt" = ["DailyEmailSenderFn", "Arn"] }
      }
    }
  })
  capabilities = ["CAPABILITY_IAM"]
  #   notification_arns = var.notification_arns TODO
  tags = var.tags
}

resource "aws_lambda_permission" "allow_s3_invoke_email_sender" {
  statement_id  = "AllowExecutionFromS3"
  action        = "lambda:InvokeFunction"
  function_name = aws_cloudformation_stack.lambda_application_stack.outputs["DailyEmailSenderFnArn"]
  principal     = "s3.amazonaws.com"
  source_arn    = "arn:aws:s3:::${var.s3_bucket_name}"
}

resource "aws_s3_bucket_notification" "trigger_lambda_on_redshift_results" {
  bucket = aws_s3_bucket.lambda_bucket.id

  lambda_function {
    lambda_function_arn = aws_cloudformation_stack.lambda_application_stack.outputs["DailyEmailSenderFnArn"]
    events              = ["s3:ObjectCreated:*"]
    filter_prefix       = "redshift-results/"
  }

  depends_on = [
    aws_lambda_permission.allow_s3_invoke_email_sender
  ]
}

