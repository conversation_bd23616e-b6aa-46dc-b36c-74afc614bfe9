variable "tags" {
  description = "Additional tags to apply to resources"
  type        = map(string)
  default     = {}
}

variable "s3_bucket_name" {
  description = "S3 bucket to upload Lambda function code"
  type        = string
  default     = "your-s3-bucket-name"
}

variable "redshift_cluster_id" {
  description = "Redshift cluster identifier"
  type        = string
}

variable "database" {
  description = "Database name"
  type        = string
}

variable "db_user" {
  description = "Database user"
  type        = string
}

variable "region_name" {
  description = "AWS region name"
  type        = string
}

variable "ses_region_name" {
  description = "SES region name"
  type        = string
}

variable "execution_time_window_start_time" {
  type = string
}

variable "execution_time_window_end_time" {
  type = string
}

variable "execution_time_zone" {
  type = string
}

variable "data_unload_iam_role" {
  type = string
}

variable "aws_account_id" {
  description = "AWS Account ID"
  type        = string
}

variable "ses_sender_email" {
  type = string
}

variable "email_receiver_emails" {
  type = string
}

variable "bcc_emails" {
  type = string
}

variable "notification_arns" {
  description = "ARN for SNS topic to send notifications"
  type        = list(string)
  default     = null
}
