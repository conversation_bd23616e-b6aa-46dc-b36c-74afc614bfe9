import json
import boto3
import os
import logging
import tempfile
import zipfile
import base64
from datetime import datetime
from email.mime.multipart import MI<PERSON><PERSON><PERSON>ipart
from email.mime.application import MIMEApplication
from email.mime.text import MIMEText

# --- Environment Variables ---
REGION_NAME = os.environ["REGION_NAME"]
SES_REGION_NAME = os.environ["SES_REGION_NAME"]
SES_SENDER = os.environ["SES_SENDER"]
SES_RECIPIENT_RAW = os.environ["SES_RECIPIENT_RAW"]
SES_BCC_EMAIL_RAW = os.environ.get("SES_BCC_EMAIL_RAW", "")
SNS_NOTIFICATION_ARNS_RAW = os.environ.get("SNS_NOTIFICATION_ARNS_RAW", "")
EXECUTION_TRACKING_DYNAMO_TABLE_NAME = os.environ[
    "EXECUTION_TRACKING_DYNAMO_TABLE_NAME"
]

# --- Parse recipient emails (comma-separated) ---
SES_RECIPIENTS = [
    email.strip() for email in SES_RECIPIENT_RAW.split(",") if email.strip()
]
SES_BCC_EMAILS = [
    email.strip() for email in SES_BCC_EMAIL_RAW.split(",") if email.strip()
]
SNS_NOTIFICATION_ARNS = [
    sns_arn.strip()
    for sns_arn in SNS_NOTIFICATION_ARNS_RAW.split(",")
    if sns_arn.strip()
]

# --- AWS Clients ---
s3_client = boto3.client("s3", region_name=REGION_NAME)
ses_client = boto3.client("ses", region_name=SES_REGION_NAME)
dynamodb_client = boto3.resource("dynamodb", region_name=REGION_NAME)
sns_client = boto3.client("sns", region_name=REGION_NAME)

# --- Logging ---
logger = logging.getLogger()
logger.setLevel(logging.INFO)


def lambda_handler(event, context):
    try:
        for record in event["Records"]:
            s3_bucket = record["s3"]["bucket"]["name"]
            s3_key = record["s3"]["object"]["key"]
            logger.info(f"New Redshift export detected: s3://{s3_bucket}/{s3_key}")

            # Extract date from S3 key
            parts = s3_key.split("/")
            if len(parts) < 3:
                send_sns_error_notification(
                    f"Invalid S3 key format for exported results {s3_key}",
                )
                raise Exception("Invalid S3 key format")

            file_name = parts[2]  # e.g., '2025-04-02.csv000000'
            report_date_str = file_name.split(".")[0]  # → '2025-04-02'
            date_obj = datetime.strptime(report_date_str, "%Y-%m-%d")
            subject_date_str = date_obj.strftime(
                "%A %d %B, %Y"
            )  # e.g. Monday 31 March, 2025
            today_local_date = parts[1]  # e.g. 2025-03-31

            zip_filename_date = date_obj.strftime("%d_%m_%Y")
            zip_filename = f"tim_can_{zip_filename_date}.zip"

            with tempfile.TemporaryDirectory() as tmpdir:
                csv_path = os.path.join(tmpdir, f"tim_can_{zip_filename_date}.csv")
                zip_path = os.path.join(tmpdir, zip_filename)

                # Download and zip the file
                s3_client.download_file(s3_bucket, s3_key, csv_path)
                logger.info(f"Downloaded CSV to {csv_path}")

                with zipfile.ZipFile(zip_path, "w", zipfile.ZIP_DEFLATED) as zipf:
                    zipf.write(csv_path, arcname=f"tim_can_{zip_filename_date}.csv")
                logger.info(f"Created zip: {zip_path}")

                # Send email and update DynamoDB
                send_email_with_zip(
                    zip_path, zip_filename, subject_date_str, today_local_date
                )

        return {
            "statusCode": 200,
            "body": json.dumps("Email sent and tracking table updated."),
        }

    except Exception as e:
        logger.error(f"Lambda execution error: {str(e)}")
        send_sns_error_notification(f"Lambda execution error: {str(e)}")
        return {"statusCode": 500, "body": json.dumps(f"Error: {str(e)}")}


def send_email_with_zip(zip_path, zip_filename, subject_date_str, today_local_date):
    subject = (
        f"Fingermark Eyecue Drive-Thru Data for Tim Hortons for {subject_date_str}"
    )

    body_html = f"""
    <html>
    <body>
        <p>Hi Scott,</p>
        <p>Please find attached the Fingermark Eyecue™ Drive-Thru Data for Tim Hortons for {subject_date_str}.</p>
        <p>If you have any questions on the data, please contact the Data Team at Fingermark on <a href='mailto:<EMAIL>'><EMAIL></a>.</p>
        <p>Thank you,<br/>Data Engineering Team</p>
    </body>
    </html>
    """

    msg = MIMEMultipart()
    msg["Subject"] = subject
    msg["From"] = SES_SENDER
    msg["To"] = ", ".join(SES_RECIPIENTS)
    if SES_BCC_EMAILS:
        msg["Bcc"] = ", ".join(SES_BCC_EMAILS)

    # HTML body
    msg.attach(MIMEText(body_html, "html"))

    # Zip attachment
    with open(zip_path, "rb") as f:
        part = MIMEApplication(f.read())
        part.add_header("Content-Disposition", f'attachment; filename="{zip_filename}"')
        msg.attach(part)

    all_destinations = SES_RECIPIENTS + SES_BCC_EMAILS

    # Send via SES
    response = ses_client.send_raw_email(
        Source=SES_SENDER,
        Destinations=all_destinations,
        RawMessage={"Data": msg.as_string()},
    )

    logger.info(f"Email sent via SES. Message ID: {response['MessageId']}")

    # Update DynamoDB tracking table
    try:
        execution_tracking_table = dynamodb_client.Table(
            EXECUTION_TRACKING_DYNAMO_TABLE_NAME
        )

        execution_tracking_table.update_item(
            Key={"date": today_local_date},
            UpdateExpression="SET #s = :s",
            ExpressionAttributeNames={"#s": "status"},
            ExpressionAttributeValues={":s": "EMAIL_SENT"},
        )

        logger.info(
            f"DynamoDB table '{EXECUTION_TRACKING_DYNAMO_TABLE_NAME}' updated for date {today_local_date}"
        )
    except Exception as e:
        logger.error(f"Failed to update DynamoDB: {str(e)}")
        send_sns_error_notification(f"Failed to update DynamoDB: {str(e)}")


def send_sns_error_notification(message):
    subject = "TIM-CAN Daily Email Error"
    try:
        for arn in SNS_NOTIFICATION_ARNS:
            try:
                response = sns_client.publish(
                    TopicArn=arn, Subject=subject, Message=message
                )
                logger.info(
                    f"SNS notification sent to {arn}. Message ID: {response['MessageId']}"
                )
            except Exception as e:
                logger.error(f"Failed to send SNS notification to {arn}: {str(e)}")

    except Exception as e:
        logger.error(f"Unexpected error setting up SNS client or publishing: {str(e)}")
