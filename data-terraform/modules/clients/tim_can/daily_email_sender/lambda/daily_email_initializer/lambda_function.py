import json
import boto3
import time
import os
import uuid
from datetime import datetime, timedelta
import pytz  # For handling time zones
import logging

# Initialize Redshift Data API client
redshift_data = boto3.client("redshift-data")

# Define the Redshift cluster and database details from environment variables
REDSHIFT_CLUSTER_ID = os.environ["REDSHIFT_CLUSTER_ID"]
DATABASE = os.environ["DATABASE"]
DB_USER = os.environ["DB_USER"]
REGION_NAME = os.environ["REGION_NAME"]
DATA_UNLOAD_IAM_ROLE = os.environ["DATA_UNLOAD_IAM_ROLE"]
DESTINATION_S3_BUCKET = os.environ["DESTINATION_S3_BUCKET"]
TIME_ZONE = os.environ["TIME_ZONE"]
EXECUTION_TIME_WINDOW_START_TIME = os.environ["EXECUTION_TIME_WINDOW_START_TIME"]
EXECUTION_TIME_WINDOW_END_TIME = os.environ["EXECUTION_TIME_WINDOW_END_TIME"]
EXECUTION_TRACKING_DYNAMO_TABLE_NAME = os.environ.get(
    "EXECUTION_TRACKING_DYNAMO_TABLE_NAME"
)
SNS_NOTIFICATION_ARNS_RAW = os.environ.get("SNS_NOTIFICATION_ARNS_RAW", "")

SNS_NOTIFICATION_ARNS = [
    sns_arn.strip()
    for sns_arn in SNS_NOTIFICATION_ARNS_RAW.split(",")
    if sns_arn.strip()
]

# Define the SQL queries
REFRESH_MV_SQL = "refresh MATERIALIZED view eyecue_tim_can.v_daily_data_last_5_days;"

# Set up logging
logger = logging.getLogger()
logger.setLevel("INFO")

dynamodb_client = boto3.resource("dynamodb", region_name=REGION_NAME)
sns_client = boto3.client("sns", region_name=REGION_NAME)


def execute_sql(sql_statement):
    """
    Function to execute an SQL statement using the Redshift Data API.
    """
    response = redshift_data.execute_statement(
        ClusterIdentifier=REDSHIFT_CLUSTER_ID,
        Database=DATABASE,
        DbUser=DB_USER,
        Sql=sql_statement,
    )

    # Check the execution id for the SQL statement
    query_id = response["Id"]

    # Wait for query to complete
    status = redshift_data.describe_statement(Id=query_id)
    while status["Status"] in ["SUBMITTED", "PICKED", "STARTED"]:
        # time.sleep(1)  # Wait before checking the status again
        status = redshift_data.describe_statement(Id=query_id)

    # Check for errors
    if status["Status"] == "FAILED":
        send_sns_error_notification(
            f"SQL execution failed for sql {sql_statement}: {status['Error']}"
        )
        raise Exception(f"SQL execution failed: {status['Error']}")

    return status


def execute_sql_async(sql_statement):
    """
    Function to execute an SQL statement using the Redshift Data API.
    """
    response = redshift_data.execute_statement(
        ClusterIdentifier=REDSHIFT_CLUSTER_ID,
        Database=DATABASE,
        DbUser=DB_USER,
        Sql=sql_statement,
    )

    query_id = response["Id"]

    return query_id


def is_time_permits(local_time_now):
    is_in_allowed_time_window = (
        local_time_now.time()
        >= datetime.strptime(EXECUTION_TIME_WINDOW_START_TIME, "%H:%M").time()
        and local_time_now.time()
        <= datetime.strptime(EXECUTION_TIME_WINDOW_END_TIME, "%H:%M").time()
    )

    return is_in_allowed_time_window


def run_report_execution_logic(local_time_now, yesterday_local_time):
    # 1. Refresh materialized view
    logger.info("START: Executing materialized view refresh")
    execute_sql(REFRESH_MV_SQL)
    logger.info("END: Executing materialized view refresh")

    # 2. Submit csv export request to redshift

    query_uuid = str(uuid.uuid4())
    today_date_str = local_time_now.strftime("%Y-%m-%d")
    yesterday_date_str = yesterday_local_time.strftime("%Y-%m-%d")

    unload_statement = f"""
        SELECT * FROM eyecue_tim_can.v_daily_data_last_5_days
        WHERE
        journey_start_d_local = \\'{yesterday_date_str}\\'
        ORDER BY
        restaurant_id, journey_start_dt_local
    """

    s3_path = f"s3://{DESTINATION_S3_BUCKET}/redshift-results/{today_date_str}/{yesterday_date_str}.csv000"

    unload_sql = f"""
        UNLOAD ('{unload_statement}')
        TO '{s3_path}'
        IAM_ROLE '{DATA_UNLOAD_IAM_ROLE}'
        FORMAT AS CSV HEADER DELIMITER ','
        PARALLEL OFF
        ALLOWOVERWRITE;
        """

    logger.info(f"Unload SQL query: {unload_sql}")

    execution_id = None

    try:
        execution_id = execute_sql_async(unload_sql)
    except Exception as e:
        send_sns_error_notification(
            f"Error executing data unload for query : {unload_sql} : {e}"
        )
        logger.error(f"Error executing query: {e}")

    return execution_id


def lambda_handler(event, context):
    try:
        local_tz = pytz.timezone(TIME_ZONE)
        local_time_now = datetime.now(local_tz)

        logger.info(f"{TIME_ZONE} time Now ---------------->> : {local_time_now}")

        if is_time_permits(local_time_now):
            # Format today's date in timezone as a string
            today_local_date = local_time_now.strftime("%Y-%m-%d")
            yesterday_local_time = local_time_now - timedelta(days=1)

            # Reference the execution tracking table
            execution_tracking_table = dynamodb_client.Table(
                EXECUTION_TRACKING_DYNAMO_TABLE_NAME
            )

            # Check if today's date already exists in the execution tracking table
            response = execution_tracking_table.get_item(Key={"date": today_local_date})

            # If today's date is not present, proceed
            if "Item" not in response:
                # Insert today's date into execution tracking table
                execution_tracking_table.put_item(
                    Item={"date": today_local_date, "status": "INITIATED"}
                )

                exe_id = run_report_execution_logic(
                    local_time_now, yesterday_local_time
                )

                if exe_id is None:
                    return {
                        "statusCode": 500,
                        "body": json.dumps({"message": "Redshift execution failed"}),
                    }

                execution_tracking_table.update_item(
                    Key={"date": today_local_date},
                    UpdateExpression="SET #s = :s",
                    ExpressionAttributeNames={"#s": "status"},
                    ExpressionAttributeValues={":s": "SUBMIT_TO_REDSHIFT"},
                )

                return {
                    "statusCode": 200,
                    "body": json.dumps({"message": "Reports processed successfully"}),
                }

            else:
                logger.info(
                    f"Execution already performed for today's date: {today_local_date}"
                )
                return {
                    "statusCode": 200,
                    "body": json.dumps(
                        {"message": "Execution already performed for today's date."}
                    ),
                }
        else:
            logger.info(
                f"Current time is outside the allowed execution window ({EXECUTION_TIME_WINDOW_START_TIME} - {EXECUTION_TIME_WINDOW_END_TIME} : {TIME_ZONE} time)."
            )
            return {
                "statusCode": 200,
                "body": json.dumps({"message": "Outside execution window"}),
            }

    except Exception as e:
        logger.info(f"Error running lambda: {str(e)}")
        send_sns_error_notification(f"Error running lambda: {str(e)}")
        return {"statusCode": 500, "body": json.dumps(f"Error: {str(e)}")}


def send_sns_error_notification(message):
    subject = "TIM-CAN Daily Email Initializer"
    try:
        for arn in SNS_NOTIFICATION_ARNS:
            try:
                response = sns_client.publish(
                    TopicArn=arn, Subject=subject, Message=message
                )
                logger.info(
                    f"SNS notification sent to {arn}. Message ID: {response['MessageId']}"
                )
            except Exception as e:
                logger.error(f"Failed to send SNS notification to {arn}: {str(e)}")

    except Exception as e:
        logger.error(f"Unexpected error setting up SNS client or publishing: {str(e)}")
