import json
import boto3
import os

# Initialize Redshift Data API client
redshift_data = boto3.client("redshift-data")

# Define the Redshift cluster and database details from environment variables
REDSHIFT_CLUSTER_ID = os.environ["REDSHIFT_CLUSTER_ID"]
DATABASE = os.environ["DATABASE"]
DB_USER = os.environ["DB_USER"]
SECRET_NAME = os.environ["SECRET_NAME"]
REGION_NAME = os.environ["REGION_NAME"]

# Define the SQL queries
CALL_SQL = """CALL upload_data_to_fct_journey_from_view_table('eyecue_tim_can');"""


def execute_sql(sql_statement):
    """
    Function to execute an SQL statement using the Redshift Data API.
    """
    response = redshift_data.execute_statement(
        ClusterIdentifier=REDSHIFT_CLUSTER_ID,
        Database=DATABASE,
        DbUser=DB_USER,
        Sql=sql_statement,
    )

    # Check the execution id for the SQL statement
    query_id = response["Id"]

    # Wait for query to complete
    status = redshift_data.describe_statement(Id=query_id)
    while status["Status"] in ["SUBMITTED", "PICKED", "STARTED"]:
        # time.sleep(1)  # Wait before checking the status again
        status = redshift_data.describe_statement(Id=query_id)

    # Check for errors
    if status["Status"] == "FAILED":
        raise Exception(f"SQL execution failed: {status['Error']}")

    return status


def lambda_handler(event, context):
    try:
        # Step 1: Insert Data into Fact Table - Call Stored Procedure
        print("Inserting Data into Fact Table...")
        execute_sql(CALL_SQL)
        print("Data Inserted Successfully.")

        return {
            "statusCode": 200,
            "body": json.dumps("Data Inserted Successfully!"),
        }

    except Exception as e:
        print(f"Error executing SQL: {e}")
        return {"statusCode": 500, "body": json.dumps(f"Error: {str(e)}")}
